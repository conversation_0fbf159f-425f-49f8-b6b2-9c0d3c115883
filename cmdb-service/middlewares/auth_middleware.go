package middlewares

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/devops-microservices/cmdb-service/config"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// API响应结构
type APIResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// 用户信息结构
type UserInfo struct {
	ID          uint     `json:"id"`
	Username    string   `json:"username"`
	Permissions []string `json:"permissions"`
	Roles       []string `json:"roles"`
}

// 权限检查结果
type PermissionCheckResult struct {
	HasPermission bool `json:"has_permission"`
}

// TokenResponse 刷新令牌的响应结构
type TokenResponse struct {
	Access  string    `json:"access"`
	Refresh string    `json:"refresh"`
	User    *UserInfo `json:"username"`
}

// JWTAuthMiddleware 创建JWT认证中间件
func JWTAuthMiddleware(cfg *config.Config, logger *logrus.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从请求头获取Authorization
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			logger.Warn("请求未包含认证信息")
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    40101,
				"message": "未授权访问，请先登录",
			})
			c.Abort()
			return
		}

		// 验证token格式
		parts := strings.SplitN(authHeader, " ", 2)
		if !(len(parts) == 2 && parts[0] == "Bearer") {
			logger.Warn("认证信息格式不正确")
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    40101,
				"message": "认证信息格式不正确",
			})
			c.Abort()
			return
		}

		token := parts[1]

		// 尝试验证access token
		userInfo, err := validateToken(token, cfg, logger)

		// 如果token验证失败，尝试使用refresh token刷新
		if err != nil {
			logger.Warnf("Access token验证失败: %v，尝试使用refresh token", err)

			// 从Cookie或Authorization-Refresh头中获取refresh token
			var refreshToken string
			refreshTokenCookie, err := c.Cookie("refresh_token")
			if err == nil && refreshTokenCookie != "" {
				refreshToken = refreshTokenCookie
			} else {
				refreshToken = c.GetHeader("Authorization-Refresh")
				if strings.HasPrefix(refreshToken, "Bearer ") {
					refreshToken = strings.TrimPrefix(refreshToken, "Bearer ")
				}
			}

			// 如果没有refresh token，则返回未授权
			if refreshToken == "" {
				logger.Warn("没有找到refresh token")
				c.JSON(http.StatusUnauthorized, gin.H{
					"code":    40101,
					"message": "认证令牌无效或已过期，请重新登录",
				})
				c.Abort()
				return
			}

			// 尝试刷新token
			newToken, err := refreshAccessToken(refreshToken, cfg, logger)
			if err != nil {
				logger.Warnf("刷新token失败: %v", err)
				c.JSON(http.StatusUnauthorized, gin.H{
					"code":    40101,
					"message": "认证令牌无效或已过期，请重新登录",
				})
				c.Abort()
				return
			}

			// 使用新token验证
			userInfo, err = validateToken(newToken, cfg, logger)
			if err != nil {
				logger.Warnf("使用刷新后的token验证失败: %v", err)
				c.JSON(http.StatusUnauthorized, gin.H{
					"code":    40101,
					"message": "认证令牌无效或已过期，请重新登录",
				})
				c.Abort()
				return
			}

			// 将新token设置到响应头中
			c.Header("New-Access-Token", newToken)
			logger.Info("Token已刷新并验证成功")
		}

		// 将用户信息存储到上下文中
		c.Set("user_id", userInfo.ID)
		c.Set("username", userInfo.Username)
		c.Set("user_permissions", userInfo.Permissions)
		c.Set("user_roles", userInfo.Roles)

		c.Next()
	}
}

// refreshAccessToken 通过调用ucenter-service刷新token
func refreshAccessToken(refreshTokenStr string, cfg *config.Config, logger *logrus.Logger) (string, error) {
	ucenterURL := cfg.UCenterService.URL
	if ucenterURL == "" {
		ucenterURL = "http://ucenter-service:9982" // 默认地址
	}

	// 创建HTTP客户端，设置超时
	client := &http.Client{
		Timeout: 5 * time.Second,
	}

	// 准备请求体
	reqBody := fmt.Sprintf(`{"refresh":"%s"}`, refreshTokenStr)
	req, err := http.NewRequest("POST", fmt.Sprintf("%s/api/v1/ucenter/auth/token/refresh", ucenterURL),
		strings.NewReader(reqBody))
	if err != nil {
		return "", fmt.Errorf("创建刷新token请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("请求ucenter服务刷新token失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取刷新token响应失败: %w", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("刷新token失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	// 解析响应
	var apiResp APIResponse
	if err := json.Unmarshal(body, &apiResp); err != nil {
		return "", fmt.Errorf("解析刷新token响应失败: %w", err)
	}

	// 检查API响应状态
	if apiResp.Code != 20000 {
		return "", fmt.Errorf("API错误: %d - %s", apiResp.Code, apiResp.Message)
	}

	// 提取新的access token
	tokenData, ok := apiResp.Data.(map[string]interface{})
	if !ok {
		return "", errors.New("无法解析token数据")
	}

	newAccessToken, ok := tokenData["access"].(string)
	if !ok || newAccessToken == "" {
		return "", errors.New("刷新token响应中没有access字段")
	}

	return newAccessToken, nil
}

// validateToken 通过调用ucenter-service验证token
func validateToken(token string, cfg *config.Config, logger *logrus.Logger) (*UserInfo, error) {
	ucenterURL := cfg.UCenterService.URL
	if ucenterURL == "" {
		ucenterURL = "http://ucenter-service:9982" // 默认地址
	}

	// 创建HTTP客户端，设置超时
	client := &http.Client{
		Timeout: 5 * time.Second,
	}

	// 准备请求，获取当前用户信息
	req, err := http.NewRequest("GET", fmt.Sprintf("%s/api/v1/ucenter/auth/me", ucenterURL), nil)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置Authorization头
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", token))

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("请求ucenter服务失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("验证失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	// 解析响应
	var apiResp APIResponse
	if err := json.Unmarshal(body, &apiResp); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	// 检查API响应状态
	if apiResp.Code != 20000 {
		return nil, fmt.Errorf("API错误: %d - %s", apiResp.Code, apiResp.Message)
	}

	// 解析用户数据
	userData, ok := apiResp.Data.(map[string]interface{})
	if !ok {
		return nil, errors.New("无法解析用户数据")
	}

	// 提取用户ID和用户名
	userID, _ := userData["id"].(float64)
	username, _ := userData["username"].(string)

	// 提取权限列表
	var permissions []string
	if perms, ok := userData["permissions"].([]interface{}); ok {
		for _, p := range perms {
			if pStr, ok := p.(string); ok {
				permissions = append(permissions, pStr)
			}
		}
	}

	// 提取角色列表
	var roles []string
	if r, ok := userData["roles"].([]interface{}); ok {
		for _, role := range r {
			if roleStr, ok := role.(string); ok {
				roles = append(roles, roleStr)
			}
		}
	}

	return &UserInfo{
		ID:          uint(userID),
		Username:    username,
		Permissions: permissions,
		Roles:       roles,
	}, nil
}

// RequirePermission 创建权限检查中间件
func RequirePermission(permission string, cfg *config.Config, logger *logrus.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从上下文获取用户权限
		userPermissions, exists := c.Get("user_permissions")
		if !exists {
			logger.Warn("没有找到用户权限信息")
			c.JSON(http.StatusForbidden, gin.H{
				"code":    40300,
				"message": "无权访问",
			})
			c.Abort()
			return
		}

		// 检查权限
		permissions, ok := userPermissions.([]string)
		if !ok {
			logger.Warn("用户权限格式不正确")
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    50000,
				"message": "服务器内部错误",
			})
			c.Abort()
			return
		}

		// 检查是否有所需权限
		hasPermission := false
		for _, p := range permissions {
			if p == permission {
				hasPermission = true
				break
			}
		}

		// 检查是否是超级管理员
		userRoles, _ := c.Get("user_roles")
		roles, ok := userRoles.([]string)
		if ok {
			for _, role := range roles {
				if role == "超级管理员" {
					hasPermission = true
					break
				}
			}
		}

		if !hasPermission {
			logger.Warnf("用户缺少所需权限: %s", permission)
			c.JSON(http.StatusForbidden, gin.H{
				"code":    40300,
				"message": "您没有权限执行此操作",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// CheckPermission 检查用户是否具有特定权限
func CheckPermission(token, permission string, cfg *config.Config, logger *logrus.Logger) (bool, error) {
	ucenterURL := cfg.UCenterService.URL
	if ucenterURL == "" {
		ucenterURL = "http://ucenter-service:9982" // 默认地址
	}

	// 创建HTTP客户端，设置超时
	client := &http.Client{
		Timeout: 5 * time.Second,
	}

	// 准备请求
	url := fmt.Sprintf("%s/api/v1/ucenter/auth/has-permission?permission=%s", ucenterURL, permission)
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return false, fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置Authorization头
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", token))

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		return false, fmt.Errorf("请求ucenter服务失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return false, fmt.Errorf("读取响应失败: %w", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return false, fmt.Errorf("请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	// 解析响应
	var apiResp APIResponse
	if err := json.Unmarshal(body, &apiResp); err != nil {
		return false, fmt.Errorf("解析响应失败: %w", err)
	}

	// 检查API响应状态
	if apiResp.Code != 20000 {
		return false, fmt.Errorf("API错误: %d - %s", apiResp.Code, apiResp.Message)
	}

	// 解析权限检查结果
	resultData, ok := apiResp.Data.(map[string]interface{})
	if !ok {
		return false, errors.New("无法解析权限检查结果")
	}

	// 提取权限检查结果
	hasPermission, ok := resultData["has_permission"].(bool)
	if !ok {
		return false, errors.New("无法获取权限检查结果")
	}

	return hasPermission, nil
}
