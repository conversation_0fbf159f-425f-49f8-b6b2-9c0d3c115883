package main

import (
	"context"
	"flag"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/devops-microservices/cmdb-service/config"
	_ "github.com/devops-microservices/cmdb-service/docs"
	"github.com/devops-microservices/cmdb-service/routes"
	"github.com/gin-gonic/gin"
	"github.com/nats-io/nats.go"
	"github.com/sirupsen/logrus"
)

var (
	// 启动优化标志
	skipMigration    = flag.Bool("skip-migration", false, "跳过数据库迁移检查（加快启动）")
	forceAutoMigrate = flag.Bool("force-migrate", false, "强制执行数据库迁移，即使设置了skip-migration")
)

// @title CMDB Service API
// @version 1.0
// @description DevOps微服务平台的CMDB管理服务
// @termsOfService http://swagger.io/terms/

// @contact.name API Support
// @contact.url http://www.swagger.io/support
// @contact.email <EMAIL>

// @license.name Apache 2.0
// @license.url http://www.apache.org/licenses/LICENSE-2.0.html

// @host localhost:8081
// @BasePath /api/cmdb
func main() {
	// 解析命令行参数
	var port int
	var configPath string
	flag.IntVar(&port, "port", 0, "服务端口，默认使用配置文件中的端口")
	flag.StringVar(&configPath, "config", "", "配置文件路径，默认使用工作目录下的config.yaml")
	flag.Parse()

	startTime := time.Now()

	// 初始化日志
	log := initLogger()
	log.Info("🚀 初始化CMDB服务")

	// 加载配置
	cfg, err := config.LoadConfig(configPath)
	if err != nil {
		log.Fatalf("❌ 加载配置失败: %v", err)
	}

	// 验证配置
	if err := config.ValidateConfig(cfg); err != nil {
		log.Fatalf("❌ 配置验证失败: %v", err)
	}

	// 如果命令行指定了端口，则覆盖配置文件中的端口
	if port > 0 {
		log.Infof("🔧 使用命令行指定的端口: %d", port)
		cfg.Server.Port = port
	}

	// 初始化数据库
	log.Info("📊 初始化数据库...")
	db, err := config.InitDatabase(&cfg.Database, log)
	if err != nil {
		log.Fatalf("❌ 初始化数据库失败: %v", err)
	}

	// 根据标志决定是否跳过迁移
	// if !*skipMigration || *forceAutoMigrate {
	if err := config.MigrateDatabase(db, log); err != nil {
		log.Warnf("⚠️ 数据库迁移失败: %v", err)
	}
	// } else {
	// 	log.Info("⏭️ 跳过数据库迁移检查")
	// }

	// 连接NATS（可选）
	var natsConn *nats.Conn
	if cfg.Nats.URL != "" {
		log.Info("📡 连接NATS...")
		natsConn, err = nats.Connect(cfg.Nats.URL)
		if err != nil {
			log.Warnf("⚠️ 连接NATS失败: %v", err)
		} else {
			log.Infof("✅ 已连接NATS: %s", cfg.Nats.URL)
			defer natsConn.Close()
		}
	}

	// 初始化Gin
	router := initGin(log)

	// 注册路由
	routes.SetupRoutes(router, db, cfg, log)

	// 启动HTTP服务器
	srv := &http.Server{
		Addr:         fmt.Sprintf("%s:%d", cfg.Server.Host, cfg.Server.Port),
		Handler:      router,
		ReadTimeout:  time.Duration(cfg.Server.ReadTimeout) * time.Second,
		WriteTimeout: time.Duration(cfg.Server.WriteTimeout) * time.Second,
		IdleTimeout:  time.Duration(cfg.Server.IdleTimeout) * time.Second,
	}

	// 优雅启动和关闭
	go func() {
		log.Infof("🌐 CMDB服务启动在 %s:%d", cfg.Server.Host, cfg.Server.Port)
		log.Infof("📖 API文档地址: http://%s:%d/swagger/index.html", cfg.Server.Host, cfg.Server.Port)
		log.Infof("⚡ 启动耗时: %v", time.Since(startTime))

		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("❌ 启动服务器失败: %v", err)
		}
	}()

	// 等待中断信号以优雅地关闭服务器
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Info("🛑 正在关闭CMDB服务...")

	// 设置5秒的超时时间来完成关闭
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := srv.Shutdown(ctx); err != nil {
		log.Fatalf("❌ 服务器强制关闭: %v", err)
	}

	log.Info("✅ CMDB服务已退出")
}

// initLogger 初始化日志
func initLogger() *logrus.Logger {
	log := logrus.New()
	log.SetFormatter(&logrus.JSONFormatter{
		TimestampFormat: "2006-01-02 15:04:05",
	})
	log.SetLevel(logrus.InfoLevel)
	return log
}

// initGin 初始化Gin
func initGin(log *logrus.Logger) *gin.Engine {
	// 设置Gin模式
	gin.SetMode(gin.ReleaseMode)

	router := gin.New()

	// 添加中间件
	router.Use(gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		return fmt.Sprintf("%s - [%s] \"%s %s %s %d %s \"%s\" %s\"\n",
			param.ClientIP,
			param.TimeStamp.Format("02/Jan/2006:15:04:05 -0700"),
			param.Method,
			param.Path,
			param.Request.Proto,
			param.StatusCode,
			param.Latency,
			param.Request.UserAgent(),
			param.ErrorMessage,
		)
	}))

	router.Use(gin.Recovery())

	// CORS中间件
	router.Use(func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	})

	return router
}
