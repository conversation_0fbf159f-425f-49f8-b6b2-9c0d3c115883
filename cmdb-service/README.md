# CMDB Service

DevOps微服务平台的CMDB管理服务，负责管理产品、项目、应用、环境等基础配置数据。

## 功能特性

- **产品管理**: 管理产品信息，支持区域关联
- **环境管理**: 管理部署环境配置
- **项目管理**: 管理项目信息，支持产品关联
- **应用管理**: 管理微应用信息，支持项目关联
- **应用服务管理**: 管理应用在不同环境的配置
- **资源管理**: 管理K8s集群、IDC、开发语言等资源
- **RESTful API**: 提供完整的REST API接口
- **Swagger文档**: 自动生成API文档
- **数据库迁移**: 自动数据库结构迁移
- **优雅启停**: 支持优雅启动和关闭

## 技术栈

- **语言**: Go 1.20+
- **框架**: Gin (HTTP路由)
- **ORM**: GORM (数据库操作)
- **数据库**: 支持MySQL、PostgreSQL、SQLite
- **消息队列**: NATS (可选)
- **文档**: Swagger/OpenAPI
- **日志**: Logrus
- **配置**: Viper (YAML配置)

## 快速开始

### 环境要求

- Go 1.20+
- 数据库 (MySQL/PostgreSQL/SQLite)
- NATS (可选)

### 本地开发

1. 克隆代码
```bash
git clone https://github.com/devops-microservices/cmdb-service.git
cd cmdb-service
```

2. 安装依赖
```bash
go mod download
```

3. 配置数据库
编辑 `config/config.yaml` 文件，配置数据库连接信息。

4. 运行服务
```bash
go run main.go
```

5. 访问API文档
```
http://localhost:8081/swagger/index.html
```

### Docker部署

1. 构建镜像
```bash
docker build -t cmdb-service:latest .
```

2. 运行容器
```bash
docker run -d \
  --name cmdb-service \
  -p 8081:8081 \
  -v $(pwd)/config:/root/config \
  -v $(pwd)/data:/data \
  cmdb-service:latest
```

### 配置说明

#### 数据库配置

支持三种数据库类型：

**SQLite** (默认，适合开发环境)
```yaml
database:
  type: "sqlite"
  name: "cmdb.db"
```

**MySQL**
```yaml
database:
  type: "mysql"
  host: "localhost"
  port: 3306
  user: "root"
  password: "password"
  name: "cmdb"
  charset: "utf8mb4"
```

**PostgreSQL**
```yaml
database:
  type: "postgres"
  host: "localhost"
  port: 5432
  user: "postgres"
  password: "password"
  name: "cmdb"
  ssl_mode: "disable"
```

#### 服务配置

```yaml
server:
  host: "0.0.0.0"      # 监听地址
  port: 8081           # 监听端口
  read_timeout: 60     # 读取超时(秒)
  write_timeout: 60    # 写入超时(秒)
  idle_timeout: 60     # 空闲超时(秒)
```

#### NATS配置 (可选)

```yaml
nats:
  url: "nats://localhost:4222"
  subject: "cmdb.events"
```

## API接口

### 产品管理
- `GET /api/cmdb/product` - 获取产品列表
- `GET /api/cmdb/product/{id}` - 获取产品详情
- `POST /api/cmdb/product` - 创建产品
- `PUT /api/cmdb/product/{id}` - 更新产品
- `DELETE /api/cmdb/product/{id}` - 删除产品

### 环境管理
- `GET /api/cmdb/environment` - 获取环境列表
- `GET /api/cmdb/environment/{id}` - 获取环境详情
- `POST /api/cmdb/environment` - 创建环境

### 项目管理
- `GET /api/cmdb/project` - 获取项目列表
- `GET /api/cmdb/project/{id}` - 获取项目详情
- `POST /api/cmdb/project` - 创建项目

### 应用管理
- `GET /api/cmdb/app` - 获取应用列表
- `GET /api/cmdb/app/{id}` - 获取应用详情
- `POST /api/cmdb/app` - 创建应用

### 应用服务管理
- `GET /api/cmdb/app/service` - 获取应用服务列表
- `GET /api/cmdb/app/service/{id}` - 获取应用服务详情
- `POST /api/cmdb/app/service` - 创建应用服务

### 资源管理
- `GET /api/cmdb/kubernetes` - 获取K8s集群列表
- `GET /api/cmdb/app/language` - 获取开发语言列表
- `GET /api/cmdb/region` - 获取区域列表
- `GET /api/cmdb/asset/idc` - 获取IDC列表

详细的API文档请访问: `http://localhost:8081/swagger/index.html`

## 数据模型

### 核心实体关系

```
Region (区域)
  └── IDC (数据中心)
      └── Product (产品)
          └── Project (项目)
              └── MicroApp (应用)
                  └── AppInfo (应用服务)
                      └── Environment (环境)
```

### 主要数据表

- `cmdb_product` - 产品表
- `cmdb_environment` - 环境表
- `cmdb_project` - 项目表
- `cmdb_microapp` - 应用表
- `cmdb_appinfo` - 应用服务表
- `cmdb_kubernetesclusters` - K8s集群表
- `cmdb_devlanguage` - 开发语言表
- `cmdb_region` - 区域表
- `cmdb_idc` - IDC表

## 开发指南

### 项目结构

```
cmdb-service/
├── config/          # 配置模块
├── controllers/     # 控制器层
├── models/          # 数据模型
├── routes/          # 路由配置
├── services/        # 业务逻辑层
├── main.go          # 入口文件
├── config.yaml      # 配置文件
├── Dockerfile       # Docker构建文件
├── go.mod           # Go模块定义
└── README.md        # 项目文档
```

### 添加新功能

1. 在 `models/` 中定义数据模型
2. 在 `services/` 中实现业务逻辑
3. 在 `controllers/` 中添加HTTP处理器
4. 在 `routes/` 中注册路由
5. 更新Swagger注释

### 构建和部署

```bash
# 本地构建
go build -o cmdb-service .

# 跨平台构建
GOOS=linux GOARCH=amd64 go build -o cmdb-service-linux .

# Docker构建
docker build -t cmdb-service:latest .
```

## 命令行选项

```bash
./cmdb-service -h

Usage of ./cmdb-service:
  -config string
        配置文件路径，默认使用工作目录下的config.yaml
  -port int
        服务端口，默认使用配置文件中的端口
  -skip-migration
        跳过数据库迁移检查（加快启动）
```

## 健康检查

服务提供健康检查端点：

```bash
curl http://localhost:8081/health
```

响应：
```json
{
  "status": "ok",
  "service": "cmdb-service"
}
```

## 许可证

本项目采用Apache 2.0许可证。详见 [LICENSE](LICENSE) 文件。 