package main

import (
	"fmt"
	"log"

	"github.com/devops-microservices/cmdb-service/config"
	"github.com/sirupsen/logrus"
)

func main() {
	// 初始化日志
	logger := logrus.New()
	logger.SetFormatter(&logrus.JSONFormatter{
		TimestampFormat: "2006-01-02 15:04:05",
	})
	logger.SetLevel(logrus.InfoLevel)

	// 加载配置
	cfg, err := config.LoadConfig("")
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 初始化数据库连接
	db, err := config.InitDatabase(&cfg.Database, logger)
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}

	// 使用原始SQL创建MicroAppTag表
	logger.Info("开始创建MicroAppTag表...")

	// 检查表是否已存在
	var exists bool
	query := "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'cmdb_microapp_tag')"
	db.Raw(query).Scan(&exists)

	if exists {
		logger.Info("表cmdb_microapp_tag已存在，跳过创建")
	} else {
		// 创建表的SQL
		createTableSQL := `
		CREATE TABLE cmdb_microapp_tag (
			id SERIAL PRIMARY KEY,
			app_id INTEGER NOT NULL,
			tag VARCHAR(100) NOT NULL,
			created_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
			update_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
		);
		CREATE INDEX idx_cmdb_microapp_tag_app_id ON cmdb_microapp_tag (app_id);
		`

		if err := db.Exec(createTableSQL).Error; err != nil {
			logger.Errorf("创建表失败: %v", err)
		} else {
			logger.Info("表cmdb_microapp_tag创建成功")
		}
	}

	// 打印完成信息
	fmt.Println("迁移完成！")
}
