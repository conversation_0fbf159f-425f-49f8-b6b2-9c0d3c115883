# CMDB服务配置文件
server:
  host: "0.0.0.0"
  port: 9981
  read_timeout: 60
  write_timeout: 60
  idle_timeout: 60

database:
  type: "postgres" # 支持: mysql, postgres, sqlite
  host: "localhost"
  port: 5432
  username: "postgres"
  password: "password"
  dbname: "opsdb" # SQLite数据库文件名或MySQL/PostgreSQL数据库名
  sslmode: "disable"
  max_idle_conns: 10
  max_open_conns: 100
  conn_max_lifetime: 3600 # 连接最大生存时间(秒)

nats:
  url: "nats://localhost:4222"
  subject: "cmdb.events"

log:
  level: "debug" # 日志级别: debug, info, warn, error
  format: "text" # 日志格式: json, text
  output: "stdout" # 日志输出: stdout, stderr, file

# 用户中心服务配置
ucenter_service:
  # 用户中心服务地址
  url: "http://ucenter-service:9982"
  # 请求超时时间(秒)
  timeout: 5
  # 是否跳过认证(开发测试环境可设为true)
  skip_authenticate: false
