package config

import (
	"fmt"
	"log"
	"os"
	"path/filepath"

	"github.com/devops-microservices/cmdb-service/models"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
	"gorm.io/driver/mysql"
	"gorm.io/driver/postgres"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// Config 应用配置结构
type Config struct {
	Server         ServerConfig         `mapstructure:"server"`
	Database       DatabaseConfig       `mapstructure:"database"`
	Encrypt        EncryptConfig        `mapstructure:"encrypt"`
	Harbor         HarborConfig         `mapstructure:"harbor"`
	Gitlab         GitlabConfig         `mapstructure:"gitlab"`
	Nats           NatsConfig           `mapstructure:"nats"`
	Log            LogConfig            `mapstructure:"log"`
	UCenterService UCenterServiceConfig `mapstructure:"ucenter_service"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Host         string `mapstructure:"host"`
	Port         int    `mapstructure:"port"`
	ReadTimeout  int    `mapstructure:"read_timeout"`
	WriteTimeout int    `mapstructure:"write_timeout"`
	IdleTimeout  int    `mapstructure:"idle_timeout"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Type           string `mapstructure:"type"`
	Host           string `mapstructure:"host"`
	Port           int    `mapstructure:"port"`
	Username       string `mapstructure:"username"`
	Password       string `mapstructure:"password"`
	DBName         string `mapstructure:"dbname"`
	SSLMode        string `mapstructure:"sslmode"`
	TimeZone       string `mapstructure:"timezone"`
	MaxConnections int    `mapstructure:"max_connections"`
	IdleTime       int    `mapstructure:"idle_time"`
}

// EncryptConfig 加密配置
type EncryptConfig struct {
	SecretKey string `mapstructure:"secret_key"`
	Salt      string `mapstructure:"salt"`
	Info      string `mapstructure:"info"`
}

// HarborConfig 保存Harbor镜像仓库相关配置
type HarborConfig struct {
	URL                string `mapstructure:"url"`
	Username           string `mapstructure:"username"`
	Password           string `mapstructure:"password"`
	PullUsernameSecret string `mapstructure:"pull_username_secret"`
	PullUsername       string `mapstructure:"pull_username"`
	PullPassword       string `mapstructure:"pull_password"`
}

// GitlabConfig 保存GitLab相关配置
type GitlabConfig struct {
	URL      string `mapstructure:"url"`
	Username string `mapstructure:"username"`
	Token    string `mapstructure:"token"`
}

// NatsConfig NATS配置
type NatsConfig struct {
	URL     string `mapstructure:"url"`
	Subject string `mapstructure:"subject"`
}

// LogConfig 日志配置
type LogConfig struct {
	Level  string `mapstructure:"level"`
	Format string `mapstructure:"format"`
	Output string `mapstructure:"output"`
}

// UCenterServiceConfig 保存用户中心服务相关配置
type UCenterServiceConfig struct {
	URL              string `mapstructure:"url"`
	Timeout          int    `mapstructure:"timeout"`
	SkipAuthenticate bool   `mapstructure:"skip_authenticate"`
}

// LoadConfig 加载配置文件
func LoadConfig(configPath string) (*Config, error) {
	var cfg Config

	// 设置配置文件搜索路径
	if configPath != "" {
		viper.SetConfigFile(configPath)
	} else {
		// 获取当前工作目录
		pwd, _ := os.Getwd()

		// 搜索多个可能的配置文件路径
		viper.AddConfigPath(pwd)
		viper.AddConfigPath(filepath.Join(pwd, "config"))
		viper.AddConfigPath(filepath.Join(pwd, "cmdb-service"))
		viper.AddConfigPath(filepath.Join(pwd, "cmdb-service/config"))
		viper.SetConfigName("config")
		viper.SetConfigType("yaml")
	}

	// 设置环境变量前缀
	viper.SetEnvPrefix("CMDB")
	viper.AutomaticEnv()

	// 设置默认值
	setDefaults()

	// 读取配置文件
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); ok {
			log.Println("配置文件未找到，使用默认配置")
		} else {
			return nil, fmt.Errorf("读取配置文件失败: %w", err)
		}
	}

	// 解析配置到结构体
	if err := viper.Unmarshal(&cfg); err != nil {
		return nil, fmt.Errorf("解析配置失败: %w", err)
	}

	return &cfg, nil
}

// setDefaults 设置默认配置值
func setDefaults() {
	// 服务器默认配置
	viper.SetDefault("server.host", "0.0.0.0")
	viper.SetDefault("server.port", 9981)
	viper.SetDefault("server.read_timeout", 60)
	viper.SetDefault("server.write_timeout", 60)
	viper.SetDefault("server.idle_timeout", 60)

	// 数据库默认配置 - 使用PostgreSQL
	viper.SetDefault("database.type", "postgresql")
	viper.SetDefault("database.host", "localhost")
	viper.SetDefault("database.port", 5432)
	viper.SetDefault("database.username", "postgres")
	viper.SetDefault("database.password", "")
	viper.SetDefault("database.dbname", "opsdb")
	viper.SetDefault("database.sslmode", "disable")
	viper.SetDefault("database.timezone", "Asia/Shanghai")
	viper.SetDefault("database.max_connections", 100)
	viper.SetDefault("database.idle_time", 60)

	// 加密默认配置
	viper.SetDefault("encrypt.secret_key", "7f=5@e+a=b(ghm-l*mtc_ile60xuvxqi(l5y$3&gfpk1!)3_4v")
	viper.SetDefault("encrypt.salt", "django-fernet-fields-hkdf-salt")
	viper.SetDefault("encrypt.info", "django-fernet-fields")

	// NATS默认配置
	viper.SetDefault("nats.url", "nats://localhost:4222")
	viper.SetDefault("nats.subject", "cmdb.events")

	// 日志默认配置
	viper.SetDefault("log.level", "info")
	viper.SetDefault("log.format", "json")
	viper.SetDefault("log.output", "stdout")

	// UCenterService默认配置
	viper.SetDefault("ucenter_service.url", "http://ucenter-service:9982")
	viper.SetDefault("ucenter_service.timeout", 5)
	viper.SetDefault("ucenter_service.skip_authenticate", false)
}

// ValidateConfig 验证配置
func ValidateConfig(cfg *Config) error {
	if cfg.Server.Port <= 0 || cfg.Server.Port > 65535 {
		return fmt.Errorf("无效的服务端口: %d", cfg.Server.Port)
	}

	if cfg.Database.Type == "" {
		return fmt.Errorf("数据库类型不能为空")
	}

	supportedTypes := []string{"mysql", "postgres", "postgresql", "sqlite"}
	supported := false
	for _, t := range supportedTypes {
		if cfg.Database.Type == t {
			supported = true
			break
		}
	}
	if !supported {
		return fmt.Errorf("不支持的数据库类型: %s", cfg.Database.Type)
	}

	return nil
}

// InitDatabase 初始化数据库连接
func InitDatabase(cfg *DatabaseConfig, log *logrus.Logger) (*gorm.DB, error) {
	var dsn string
	var dialector gorm.Dialector

	switch cfg.Type {
	case "mysql":
		dsn = fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
			cfg.Username, cfg.Password, cfg.Host, cfg.Port, cfg.DBName)
		dialector = mysql.Open(dsn)
	case "postgres", "postgresql":
		dsn = fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%d sslmode=%s TimeZone=%s",
			cfg.Host, cfg.Username, cfg.Password, cfg.DBName, cfg.Port, cfg.SSLMode, cfg.TimeZone)
		dialector = postgres.Open(dsn)
	case "sqlite":
		dsn = cfg.DBName
		dialector = sqlite.Open(dsn)
	default:
		return nil, fmt.Errorf("不支持的数据库类型: %s", cfg.Type)
	}

	// 配置GORM日志
	var gormLogger logger.Interface
	if log.Level == logrus.DebugLevel {
		gormLogger = logger.Default.LogMode(logger.Info)
	} else {
		gormLogger = logger.Default.LogMode(logger.Silent)
	}

	// 连接数据库
	db, err := gorm.Open(dialector, &gorm.Config{
		Logger: gormLogger,
	})
	if err != nil {
		return nil, fmt.Errorf("连接数据库失败: %w", err)
	}

	// 配置连接池
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("获取数据库连接池失败: %w", err)
	}

	// 设置连接池参数
	if cfg.MaxConnections > 0 {
		sqlDB.SetMaxOpenConns(cfg.MaxConnections)
		sqlDB.SetMaxIdleConns(cfg.MaxConnections / 2)
	}

	log.Infof("数据库连接成功: %s", cfg.Type)
	return db, nil
}

// MigrateDatabase 数据库迁移
func MigrateDatabase(db *gorm.DB, log *logrus.Logger) error {
	log.Info("开始数据库迁移...")

	// 定义需要迁移的模型（按依赖顺序排列）
	migrationModels := []interface{}{
		// 基础模型（无外键依赖）
		&models.Region{},
		&models.Environment{},
		&models.Pipeline{},
		&models.DevLanguage{},
		&models.SystemConfig{},
		&models.UserProfile{},

		// 有外键依赖的模型
		&models.DataCenter{},
		&models.Product{},           // 依赖 Region
		&models.Project{},           // 依赖 Product
		&models.KubernetesCluster{}, // 依赖 DataCenter

		// 复杂关联模型
		&models.MicroApp{},              // 依赖 Project
		&models.AppInfo{},               // 依赖 MicroApp, Environment
		&models.MicroAppDeployHistory{}, // 依赖 MicroApp

		// 关联配置模型
		&models.ProjectConfig{},           // 依赖 Project, Environment
		&models.ProductEnvReleaseConfig{}, // 依赖 Product, Environment
		&models.ProjectEnvReleaseConfig{}, // 依赖 Project, Environment
		&models.AppClusterDeploy{},        // 依赖 AppInfo, KubernetesCluster
	}

	// 执行迁移
	for _, model := range migrationModels {
		if err := db.AutoMigrate(model); err != nil {
			return fmt.Errorf("迁移模型 %T 失败: %w", model, err)
		}
		log.Debugf("成功迁移模型: %T", model)
	}

	// 创建多对多关联表的索引
	// if err := createIndexes(db, log); err != nil {
	// 	return fmt.Errorf("创建索引失败: %w", err)
	// }

	log.Info("数据库迁移完成")
	return nil
}

// createIndexes 创建额外的数据库索引
// func createIndexes(db *gorm.DB, log *logrus.Logger) error {
// 	indexes := []string{
// 		// 为常用查询字段创建索引
// 		"CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_cmdb_microapp_project_id ON cmdb_microapp(project_id);",
// 		"CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_cmdb_microapp_language ON cmdb_microapp(language);",
// 		"CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_cmdb_microapp_category ON cmdb_microapp(category);",
// 		"CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_cmdb_appinfo_app_id ON cmdb_appinfo(app_id);",
// 		"CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_cmdb_appinfo_environment_id ON cmdb_appinfo(environment_id);",
// 		"CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_cmdb_project_product_id ON cmdb_project(product_id);",
// 		"CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_cmdb_microappdeployhistory_app_id ON cmdb_microappdeployhistory(app_id);",
// 		"CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_cmdb_microappdeployhistory_environment ON cmdb_microappdeployhistory(environment);",
// 		"CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_cmdb_microapptag_app_id ON cmdb_microapptag(app_id);",
// 	}

// 	for _, sql := range indexes {
// 		if err := db.Exec(sql).Error; err != nil {
// 			// 忽略索引已存在的错误
// 			log.Warnf("创建索引失败（可能已存在）: %v", err)
// 		}
// 	}

// 	log.Info("数据库索引创建完成")
// 	return nil
// }
