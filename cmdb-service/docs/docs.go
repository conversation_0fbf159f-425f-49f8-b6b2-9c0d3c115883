// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "termsOfService": "http://swagger.io/terms/",
        "contact": {
            "name": "API Support",
            "url": "http://www.swagger.io/support",
            "email": "<EMAIL>"
        },
        "license": {
            "name": "Apache 2.0",
            "url": "http://www.apache.org/licenses/LICENSE-2.0.html"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/api/cmdb/app": {
            "get": {
                "description": "获取应用列表，支持分页和搜索",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "MicroApp"
                ],
                "summary": "获取应用列表",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "页码",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "每页数量",
                        "name": "page_size",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "搜索关键词",
                        "name": "search",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "项目ID",
                        "name": "project_id",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.DTOListResponse"
                        }
                    }
                }
            },
            "post": {
                "description": "创建新应用",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "MicroApp"
                ],
                "summary": "创建应用",
                "parameters": [
                    {
                        "description": "应用信息",
                        "name": "app",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.MicroAppCreateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.DTOResponse"
                        }
                    }
                }
            }
        },
        "/api/cmdb/app/language": {
            "get": {
                "description": "获取开发语言列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "DevLanguage"
                ],
                "summary": "获取开发语言列表",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "页码",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "每页数量",
                        "name": "page_size",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "搜索关键词",
                        "name": "search",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.DTOListResponse"
                        }
                    }
                }
            }
        },
        "/api/cmdb/app/service": {
            "get": {
                "description": "获取应用服务列表，支持分页和搜索",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AppInfo"
                ],
                "summary": "获取应用服务列表",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "页码",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "每页数量",
                        "name": "page_size",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "搜索关键词",
                        "name": "search",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "应用ID",
                        "name": "app_id",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "环境ID",
                        "name": "env_id",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.DTOListResponse"
                        }
                    }
                }
            },
            "post": {
                "description": "创建新应用服务",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AppInfo"
                ],
                "summary": "创建应用服务",
                "parameters": [
                    {
                        "description": "应用服务信息",
                        "name": "appInfo",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.AppInfoCreateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.DTOResponse"
                        }
                    }
                }
            }
        },
        "/api/cmdb/app/service/{id}": {
            "get": {
                "description": "根据ID获取应用服务详情",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AppInfo"
                ],
                "summary": "获取应用服务详情",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "应用服务ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.DTOResponse"
                        }
                    }
                }
            }
        },
        "/api/cmdb/app/{id}": {
            "get": {
                "description": "根据ID获取应用详情",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "MicroApp"
                ],
                "summary": "获取应用详情",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "应用ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.DTOResponse"
                        }
                    }
                }
            }
        },
        "/api/cmdb/asset/idc": {
            "get": {
                "description": "获取IDC列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "IDC"
                ],
                "summary": "获取IDC列表",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "页码",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "每页数量",
                        "name": "page_size",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "搜索关键词",
                        "name": "search",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.DTOListResponse"
                        }
                    }
                }
            }
        },
        "/api/cmdb/environment": {
            "get": {
                "description": "获取环境列表，支持分页和搜索",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Environment"
                ],
                "summary": "获取环境列表",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "页码",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "每页数量",
                        "name": "page_size",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "搜索关键词",
                        "name": "search",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.DTOListResponse"
                        }
                    }
                }
            },
            "post": {
                "description": "创建新环境",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Environment"
                ],
                "summary": "创建环境",
                "parameters": [
                    {
                        "description": "环境信息",
                        "name": "environment",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.EnvironmentCreateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.DTOResponse"
                        }
                    }
                }
            }
        },
        "/api/cmdb/environment/{id}": {
            "get": {
                "description": "根据ID获取环境详情",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Environment"
                ],
                "summary": "获取环境详情",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "环境ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.DTOResponse"
                        }
                    }
                }
            }
        },
        "/api/cmdb/kubernetes": {
            "get": {
                "description": "获取K8s集群列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Kubernetes"
                ],
                "summary": "获取K8s集群列表",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "页码",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "每页数量",
                        "name": "page_size",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "搜索关键词",
                        "name": "search",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.DTOListResponse"
                        }
                    }
                }
            }
        },
        "/api/cmdb/product": {
            "get": {
                "description": "获取产品列表，支持分页和搜索",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Product"
                ],
                "summary": "获取产品列表",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "页码",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "每页数量",
                        "name": "page_size",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "搜索关键词",
                        "name": "search",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.DTOListResponse"
                        }
                    }
                }
            },
            "post": {
                "description": "创建新产品",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Product"
                ],
                "summary": "创建产品",
                "parameters": [
                    {
                        "description": "产品信息",
                        "name": "product",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.ProductCreateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.DTOResponse"
                        }
                    }
                }
            }
        },
        "/api/cmdb/product/{id}": {
            "get": {
                "description": "根据ID获取产品详情",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Product"
                ],
                "summary": "获取产品详情",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "产品ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.DTOResponse"
                        }
                    }
                }
            },
            "put": {
                "description": "更新产品信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Product"
                ],
                "summary": "更新产品",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "产品ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "产品信息",
                        "name": "product",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.ProductCreateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.DTOResponse"
                        }
                    }
                }
            },
            "delete": {
                "description": "删除产品",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Product"
                ],
                "summary": "删除产品",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "产品ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.DTOResponse"
                        }
                    }
                }
            }
        },
        "/api/cmdb/project": {
            "get": {
                "description": "获取项目列表，支持分页和搜索",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Project"
                ],
                "summary": "获取项目列表",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "页码",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "每页数量",
                        "name": "page_size",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "搜索关键词",
                        "name": "search",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "产品ID",
                        "name": "product_id",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.DTOListResponse"
                        }
                    }
                }
            },
            "post": {
                "description": "创建新项目",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Project"
                ],
                "summary": "创建项目",
                "parameters": [
                    {
                        "description": "项目信息",
                        "name": "project",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.ProjectCreateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.DTOResponse"
                        }
                    }
                }
            }
        },
        "/api/cmdb/project/{id}": {
            "get": {
                "description": "根据ID获取项目详情",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Project"
                ],
                "summary": "获取项目详情",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "项目ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.DTOResponse"
                        }
                    }
                }
            }
        },
        "/api/cmdb/region": {
            "get": {
                "description": "获取区域列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Region"
                ],
                "summary": "获取区域列表",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "页码",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "每页数量",
                        "name": "page_size",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "搜索关键词",
                        "name": "search",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.DTOListResponse"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "models.AppInfoCreateRequest": {
            "type": "object",
            "properties": {
                "allow_cd_branch": {
                    "$ref": "#/definitions/models.JSONField"
                },
                "allow_ci_branch": {
                    "$ref": "#/definitions/models.JSONField"
                },
                "app_id": {
                    "type": "integer"
                },
                "branch": {
                    "type": "string",
                    "maxLength": 64
                },
                "build_command": {
                    "type": "string",
                    "maxLength": 250
                },
                "can_edit": {
                    "$ref": "#/definitions/models.JSONField"
                },
                "desc": {
                    "type": "string"
                },
                "environment_id": {
                    "type": "integer"
                },
                "is_enable": {
                    "type": "integer",
                    "enum": [
                        0,
                        1
                    ]
                },
                "ports": {
                    "$ref": "#/definitions/models.JSONField"
                },
                "template": {
                    "$ref": "#/definitions/models.JSONField"
                }
            }
        },
        "models.DTOListResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {},
                "message": {
                    "type": "string"
                },
                "page": {
                    "type": "integer"
                },
                "page_size": {
                    "type": "integer"
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "models.DTOResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {},
                "message": {
                    "type": "string"
                }
            }
        },
        "models.EnvironmentCreateRequest": {
            "type": "object",
            "required": [
                "name"
            ],
            "properties": {
                "alias": {
                    "type": "string",
                    "maxLength": 128
                },
                "allow_cd_branch": {
                    "$ref": "#/definitions/models.JSONField"
                },
                "allow_ci_branch": {
                    "$ref": "#/definitions/models.JSONField"
                },
                "desc": {
                    "type": "string"
                },
                "extra": {
                    "$ref": "#/definitions/models.JSONField"
                },
                "merge_on": {
                    "type": "integer",
                    "enum": [
                        0,
                        1
                    ]
                },
                "name": {
                    "type": "string",
                    "maxLength": 100
                },
                "sort": {
                    "type": "integer"
                },
                "template": {
                    "$ref": "#/definitions/models.JSONField"
                },
                "ticket_on": {
                    "type": "integer",
                    "enum": [
                        0,
                        1
                    ]
                }
            }
        },
        "models.JSONField": {
            "type": "object",
            "additionalProperties": true
        },
        "models.MicroAppCreateRequest": {
            "type": "object",
            "required": [
                "name"
            ],
            "properties": {
                "alias": {
                    "type": "string",
                    "maxLength": 128
                },
                "build_command": {
                    "type": "string",
                    "maxLength": 250
                },
                "can_edit": {
                    "$ref": "#/definitions/models.JSONField"
                },
                "category": {
                    "type": "string",
                    "maxLength": 128
                },
                "desc": {
                    "type": "string"
                },
                "dockerfile": {
                    "$ref": "#/definitions/models.JSONField"
                },
                "is_k8s": {
                    "type": "string",
                    "enum": [
                        "k8s",
                        "docker"
                    ]
                },
                "language": {
                    "type": "string",
                    "maxLength": 32
                },
                "modules": {
                    "$ref": "#/definitions/models.JSONField"
                },
                "multiple_app": {
                    "type": "boolean"
                },
                "multiple_ids": {
                    "$ref": "#/definitions/models.JSONField"
                },
                "name": {
                    "type": "string",
                    "maxLength": 128
                },
                "notify": {
                    "$ref": "#/definitions/models.JSONField"
                },
                "online": {
                    "type": "boolean"
                },
                "ports": {
                    "$ref": "#/definitions/models.JSONField"
                },
                "project_id": {
                    "type": "integer"
                },
                "repo": {
                    "$ref": "#/definitions/models.JSONField"
                },
                "scan_branch": {
                    "type": "string",
                    "maxLength": 64
                },
                "target": {
                    "$ref": "#/definitions/models.JSONField"
                },
                "team_members": {
                    "$ref": "#/definitions/models.JSONField"
                },
                "template": {
                    "$ref": "#/definitions/models.JSONField"
                }
            }
        },
        "models.ProductCreateRequest": {
            "type": "object",
            "required": [
                "name"
            ],
            "properties": {
                "alias": {
                    "type": "string",
                    "maxLength": 128
                },
                "desc": {
                    "type": "string"
                },
                "managers": {
                    "$ref": "#/definitions/models.JSONField"
                },
                "name": {
                    "type": "string",
                    "maxLength": 100
                },
                "prefix": {
                    "type": "string",
                    "maxLength": 100
                },
                "region_id": {
                    "type": "integer"
                }
            }
        },
        "models.ProjectCreateRequest": {
            "type": "object",
            "required": [
                "name"
            ],
            "properties": {
                "alias": {
                    "type": "string",
                    "maxLength": 128
                },
                "desc": {
                    "type": "string"
                },
                "developer": {
                    "type": "integer"
                },
                "manager": {
                    "type": "integer"
                },
                "name": {
                    "type": "string",
                    "maxLength": 100
                },
                "notify": {
                    "$ref": "#/definitions/models.JSONField"
                },
                "product_id": {
                    "type": "integer"
                },
                "tester": {
                    "type": "integer"
                }
            }
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1.0",
	Host:             "localhost:8081",
	BasePath:         "/api/cmdb",
	Schemes:          []string{},
	Title:            "CMDB Service API",
	Description:      "DevOps微服务平台的CMDB管理服务",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
