basePath: /api/cmdb
definitions:
  models.AppInfoCreateRequest:
    properties:
      allow_cd_branch:
        $ref: '#/definitions/models.JSONField'
      allow_ci_branch:
        $ref: '#/definitions/models.JSONField'
      app_id:
        type: integer
      branch:
        maxLength: 64
        type: string
      build_command:
        maxLength: 250
        type: string
      can_edit:
        $ref: '#/definitions/models.JSONField'
      desc:
        type: string
      environment_id:
        type: integer
      is_enable:
        enum:
        - 0
        - 1
        type: integer
      ports:
        $ref: '#/definitions/models.JSONField'
      template:
        $ref: '#/definitions/models.JSONField'
    type: object
  models.DTOListResponse:
    properties:
      code:
        type: integer
      data: {}
      message:
        type: string
      page:
        type: integer
      page_size:
        type: integer
      total:
        type: integer
    type: object
  models.DTOResponse:
    properties:
      code:
        type: integer
      data: {}
      message:
        type: string
    type: object
  models.EnvironmentCreateRequest:
    properties:
      alias:
        maxLength: 128
        type: string
      allow_cd_branch:
        $ref: '#/definitions/models.JSONField'
      allow_ci_branch:
        $ref: '#/definitions/models.JSONField'
      desc:
        type: string
      extra:
        $ref: '#/definitions/models.JSONField'
      merge_on:
        enum:
        - 0
        - 1
        type: integer
      name:
        maxLength: 100
        type: string
      sort:
        type: integer
      template:
        $ref: '#/definitions/models.JSONField'
      ticket_on:
        enum:
        - 0
        - 1
        type: integer
    required:
    - name
    type: object
  models.JSONField:
    additionalProperties: true
    type: object
  models.MicroAppCreateRequest:
    properties:
      alias:
        maxLength: 128
        type: string
      build_command:
        maxLength: 250
        type: string
      can_edit:
        $ref: '#/definitions/models.JSONField'
      category:
        maxLength: 128
        type: string
      desc:
        type: string
      dockerfile:
        $ref: '#/definitions/models.JSONField'
      is_k8s:
        enum:
        - k8s
        - docker
        type: string
      language:
        maxLength: 32
        type: string
      modules:
        $ref: '#/definitions/models.JSONField'
      multiple_app:
        type: boolean
      multiple_ids:
        $ref: '#/definitions/models.JSONField'
      name:
        maxLength: 128
        type: string
      notify:
        $ref: '#/definitions/models.JSONField'
      online:
        type: boolean
      ports:
        $ref: '#/definitions/models.JSONField'
      project_id:
        type: integer
      repo:
        $ref: '#/definitions/models.JSONField'
      scan_branch:
        maxLength: 64
        type: string
      target:
        $ref: '#/definitions/models.JSONField'
      team_members:
        $ref: '#/definitions/models.JSONField'
      template:
        $ref: '#/definitions/models.JSONField'
    required:
    - name
    type: object
  models.ProductCreateRequest:
    properties:
      alias:
        maxLength: 128
        type: string
      desc:
        type: string
      managers:
        $ref: '#/definitions/models.JSONField'
      name:
        maxLength: 100
        type: string
      prefix:
        maxLength: 100
        type: string
      region_id:
        type: integer
    required:
    - name
    type: object
  models.ProjectCreateRequest:
    properties:
      alias:
        maxLength: 128
        type: string
      desc:
        type: string
      developer:
        type: integer
      manager:
        type: integer
      name:
        maxLength: 100
        type: string
      notify:
        $ref: '#/definitions/models.JSONField'
      product_id:
        type: integer
      tester:
        type: integer
    required:
    - name
    type: object
host: localhost:8081
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: http://www.swagger.io/support
  description: DevOps微服务平台的CMDB管理服务
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  termsOfService: http://swagger.io/terms/
  title: CMDB Service API
  version: "1.0"
paths:
  /api/cmdb/app:
    get:
      consumes:
      - application/json
      description: 获取应用列表，支持分页和搜索
      parameters:
      - default: 1
        description: 页码
        in: query
        name: page
        type: integer
      - default: 10
        description: 每页数量
        in: query
        name: page_size
        type: integer
      - description: 搜索关键词
        in: query
        name: search
        type: string
      - description: 项目ID
        in: query
        name: project_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.DTOListResponse'
      summary: 获取应用列表
      tags:
      - MicroApp
    post:
      consumes:
      - application/json
      description: 创建新应用
      parameters:
      - description: 应用信息
        in: body
        name: app
        required: true
        schema:
          $ref: '#/definitions/models.MicroAppCreateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.DTOResponse'
      summary: 创建应用
      tags:
      - MicroApp
  /api/cmdb/app/{id}:
    get:
      consumes:
      - application/json
      description: 根据ID获取应用详情
      parameters:
      - description: 应用ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.DTOResponse'
      summary: 获取应用详情
      tags:
      - MicroApp
  /api/cmdb/app/language:
    get:
      consumes:
      - application/json
      description: 获取开发语言列表
      parameters:
      - default: 1
        description: 页码
        in: query
        name: page
        type: integer
      - default: 10
        description: 每页数量
        in: query
        name: page_size
        type: integer
      - description: 搜索关键词
        in: query
        name: search
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.DTOListResponse'
      summary: 获取开发语言列表
      tags:
      - DevLanguage
  /api/cmdb/app/service:
    get:
      consumes:
      - application/json
      description: 获取应用服务列表，支持分页和搜索
      parameters:
      - default: 1
        description: 页码
        in: query
        name: page
        type: integer
      - default: 10
        description: 每页数量
        in: query
        name: page_size
        type: integer
      - description: 搜索关键词
        in: query
        name: search
        type: string
      - description: 应用ID
        in: query
        name: app_id
        type: integer
      - description: 环境ID
        in: query
        name: env_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.DTOListResponse'
      summary: 获取应用服务列表
      tags:
      - AppInfo
    post:
      consumes:
      - application/json
      description: 创建新应用服务
      parameters:
      - description: 应用服务信息
        in: body
        name: appInfo
        required: true
        schema:
          $ref: '#/definitions/models.AppInfoCreateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.DTOResponse'
      summary: 创建应用服务
      tags:
      - AppInfo
  /api/cmdb/app/service/{id}:
    get:
      consumes:
      - application/json
      description: 根据ID获取应用服务详情
      parameters:
      - description: 应用服务ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.DTOResponse'
      summary: 获取应用服务详情
      tags:
      - AppInfo
  /api/cmdb/asset/idc:
    get:
      consumes:
      - application/json
      description: 获取IDC列表
      parameters:
      - default: 1
        description: 页码
        in: query
        name: page
        type: integer
      - default: 10
        description: 每页数量
        in: query
        name: page_size
        type: integer
      - description: 搜索关键词
        in: query
        name: search
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.DTOListResponse'
      summary: 获取IDC列表
      tags:
      - IDC
  /api/cmdb/environment:
    get:
      consumes:
      - application/json
      description: 获取环境列表，支持分页和搜索
      parameters:
      - default: 1
        description: 页码
        in: query
        name: page
        type: integer
      - default: 10
        description: 每页数量
        in: query
        name: page_size
        type: integer
      - description: 搜索关键词
        in: query
        name: search
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.DTOListResponse'
      summary: 获取环境列表
      tags:
      - Environment
    post:
      consumes:
      - application/json
      description: 创建新环境
      parameters:
      - description: 环境信息
        in: body
        name: environment
        required: true
        schema:
          $ref: '#/definitions/models.EnvironmentCreateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.DTOResponse'
      summary: 创建环境
      tags:
      - Environment
  /api/cmdb/environment/{id}:
    get:
      consumes:
      - application/json
      description: 根据ID获取环境详情
      parameters:
      - description: 环境ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.DTOResponse'
      summary: 获取环境详情
      tags:
      - Environment
  /api/cmdb/kubernetes:
    get:
      consumes:
      - application/json
      description: 获取K8s集群列表
      parameters:
      - default: 1
        description: 页码
        in: query
        name: page
        type: integer
      - default: 10
        description: 每页数量
        in: query
        name: page_size
        type: integer
      - description: 搜索关键词
        in: query
        name: search
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.DTOListResponse'
      summary: 获取K8s集群列表
      tags:
      - Kubernetes
  /api/cmdb/product:
    get:
      consumes:
      - application/json
      description: 获取产品列表，支持分页和搜索
      parameters:
      - default: 1
        description: 页码
        in: query
        name: page
        type: integer
      - default: 10
        description: 每页数量
        in: query
        name: page_size
        type: integer
      - description: 搜索关键词
        in: query
        name: search
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.DTOListResponse'
      summary: 获取产品列表
      tags:
      - Product
    post:
      consumes:
      - application/json
      description: 创建新产品
      parameters:
      - description: 产品信息
        in: body
        name: product
        required: true
        schema:
          $ref: '#/definitions/models.ProductCreateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.DTOResponse'
      summary: 创建产品
      tags:
      - Product
  /api/cmdb/product/{id}:
    delete:
      consumes:
      - application/json
      description: 删除产品
      parameters:
      - description: 产品ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.DTOResponse'
      summary: 删除产品
      tags:
      - Product
    get:
      consumes:
      - application/json
      description: 根据ID获取产品详情
      parameters:
      - description: 产品ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.DTOResponse'
      summary: 获取产品详情
      tags:
      - Product
    put:
      consumes:
      - application/json
      description: 更新产品信息
      parameters:
      - description: 产品ID
        in: path
        name: id
        required: true
        type: integer
      - description: 产品信息
        in: body
        name: product
        required: true
        schema:
          $ref: '#/definitions/models.ProductCreateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.DTOResponse'
      summary: 更新产品
      tags:
      - Product
  /api/cmdb/project:
    get:
      consumes:
      - application/json
      description: 获取项目列表，支持分页和搜索
      parameters:
      - default: 1
        description: 页码
        in: query
        name: page
        type: integer
      - default: 10
        description: 每页数量
        in: query
        name: page_size
        type: integer
      - description: 搜索关键词
        in: query
        name: search
        type: string
      - description: 产品ID
        in: query
        name: product_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.DTOListResponse'
      summary: 获取项目列表
      tags:
      - Project
    post:
      consumes:
      - application/json
      description: 创建新项目
      parameters:
      - description: 项目信息
        in: body
        name: project
        required: true
        schema:
          $ref: '#/definitions/models.ProjectCreateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.DTOResponse'
      summary: 创建项目
      tags:
      - Project
  /api/cmdb/project/{id}:
    get:
      consumes:
      - application/json
      description: 根据ID获取项目详情
      parameters:
      - description: 项目ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.DTOResponse'
      summary: 获取项目详情
      tags:
      - Project
  /api/cmdb/region:
    get:
      consumes:
      - application/json
      description: 获取区域列表
      parameters:
      - default: 1
        description: 页码
        in: query
        name: page
        type: integer
      - default: 10
        description: 每页数量
        in: query
        name: page_size
        type: integer
      - description: 搜索关键词
        in: query
        name: search
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.DTOListResponse'
      summary: 获取区域列表
      tags:
      - Region
swagger: "2.0"
