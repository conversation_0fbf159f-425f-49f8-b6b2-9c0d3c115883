package utils

import (
	"crypto/sha256"
	"encoding/base64"
	"errors"
	"fmt"
	"log"

	"github.com/fernet/fernet-go"
	"golang.org/x/crypto/hkdf"
)

// EncryptedField 表示一个加密字段
type EncryptedField struct {
	keys       [][]byte
	fernetKeys []*fernet.Key
	salt       []byte
	info       []byte
}

// NewEncryptedField 创建一个新的加密字段
func NewEncryptedField(keys []string, salt, info string) (*EncryptedField, error) {
	ef := &EncryptedField{
		salt: []byte(salt),
		info: []byte(info),
	}

	// 转换密钥
	for _, key := range keys {
		ef.keys = append(ef.keys, []byte(key))
	}

	// 使用 HKDF 派生密钥
	for _, key := range ef.keys {
		derivedKey := ef.deriveFernetKey(key)
		fernetKey, err := fernet.DecodeKey(derivedKey)
		if err != nil {
			return nil, fmt.Errorf("failed to decode fernet key: %v", err)
		}
		ef.fernetKeys = append(ef.fernetKeys, fernetKey)
	}

	return ef, nil
}

// deriveFernetKey 使用 HKDF 派生密钥
func (ef *EncryptedField) deriveFernetKey(inputKey []byte) string {
	// 使用 HKDF 派生密钥
	hash := sha256.New
	kdf := hkdf.New(hash, inputKey, ef.salt, ef.info)
	derivedKey := make([]byte, 32)
	if _, err := kdf.Read(derivedKey); err != nil {
		log.Fatal(err)
	}

	// 将派生密钥编码为 URL 安全的 Base64
	return base64.URLEncoding.EncodeToString(derivedKey)
}

// Encrypt 加密数据
func (ef *EncryptedField) Encrypt(data []byte) ([]byte, error) {
	if len(ef.fernetKeys) == 0 {
		return nil, errors.New("no fernet keys available")
	}

	token, err := fernet.EncryptAndSign(data, ef.fernetKeys[0])
	if err != nil {
		return nil, fmt.Errorf("failed to encrypt data: %v", err)
	}

	return token, nil
}

// Decrypt 解密数据
func (ef *EncryptedField) Decrypt(token []byte) ([]byte, error) {
	if len(ef.fernetKeys) == 0 {
		return nil, errors.New("no fernet keys available")
	}

	data := fernet.VerifyAndDecrypt(token, 0, ef.fernetKeys)
	if data == nil {
		return nil, errors.New("failed to decrypt data")
	}

	return data, nil
}
