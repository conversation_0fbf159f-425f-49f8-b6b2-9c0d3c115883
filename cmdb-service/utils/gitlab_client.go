package utils

import (
	"fmt"
	"log"
	"strconv"

	gitlab "gitlab.com/gitlab-org/api/client-go"
)

// GetGitLabClient 创建 GitLab 客户端
func GetGitLabClient(url, token string) *gitlab.Client {
	client, err := gitlab.NewClient(token, gitlab.WithBaseURL(url))
	if err != nil {
		log.Fatalf("Failed to create GitLab client: %v", err)
	}
	return client
}

// GetGitLabProject 获取单个项目信息
func GetGitLabProject(client *gitlab.Client, projectID interface{}) (*gitlab.Project, error) {
	project, _, err := client.Projects.GetProject(projectID, nil)
	if err != nil {
		return nil, err
	}
	return project, nil
}

// GetGitLabProjects 获取项目列表
func GetGitLabProjects(client *gitlab.Client, options *gitlab.ListProjectsOptions) ([]*gitlab.Project, error) {
	if options == nil {
		options = &gitlab.ListProjectsOptions{}
	}
	projects, _, err := client.Projects.ListProjects(options)
	if err != nil {
		return nil, err
	}
	return projects, nil
}

// GetGitLabProtectedBranches 获取受保护的分支
func GetGitLabProtectedBranches(client *gitlab.Client, projectID interface{}) ([]*gitlab.ProtectedBranch, error) {
	branches, _, err := client.ProtectedBranches.ListProtectedBranches(projectID, &gitlab.ListProtectedBranchesOptions{})
	if err != nil {
		return nil, err
	}
	return branches, nil
}

// GetGitLabBranches 获取分支列表
func GetGitLabBranches(client *gitlab.Client, projectID interface{}) ([]*gitlab.Branch, error) {
	branches, _, err := client.Branches.ListBranches(projectID, &gitlab.ListBranchesOptions{})
	if err != nil {
		return nil, err
	}
	return branches, nil
}

// GetGitLabTags 获取标签列表
func GetGitLabTags(client *gitlab.Client, projectID interface{}) ([]*gitlab.Tag, error) {
	tags, _, err := client.Tags.ListTags(projectID, &gitlab.ListTagsOptions{})
	if err != nil {
		return nil, err
	}
	return tags, nil
}

// CreateGitLabProject 创建项目
func CreateGitLabProject(client *gitlab.Client, projectName string) (*gitlab.Project, error) {
	options := &gitlab.CreateProjectOptions{
		Name: gitlab.Ptr(projectName),
	}
	project, _, err := client.Projects.CreateProject(options)
	if err != nil {
		return nil, err
	}
	return project, nil
}

// CreateBranch 创建分支
func CreateBranch(client *gitlab.Client, projectID interface{}, branchName, ref string) (*gitlab.Branch, error) {
	options := &gitlab.CreateBranchOptions{
		Branch: gitlab.Ptr(branchName),
		Ref:    gitlab.Ptr(ref),
	}
	branch, _, err := client.Branches.CreateBranch(projectID, options)
	if err != nil {
		return nil, err
	}
	return branch, nil
}

// RemoveGitLabBranch 删除分支
func RemoveGitLabBranch(client *gitlab.Client, projectID interface{}, branchName string) error {
	_, err := client.Branches.DeleteBranch(projectID, branchName)
	return err
}

// CreateGitLabTag 创建标签
func CreateGitLabTag(client *gitlab.Client, projectID interface{}, tagName, ref string) (*gitlab.Tag, error) {
	options := &gitlab.CreateTagOptions{
		TagName: gitlab.Ptr(tagName),
		Ref:     gitlab.Ptr(ref),
	}
	tag, _, err := client.Tags.CreateTag(projectID, options)
	if err != nil {
		return nil, err
	}
	return tag, nil
}

// RemoveGitLabTag 删除标签
func RemoveGitLabTag(client *gitlab.Client, projectID interface{}, tagName string) error {
	_, err := client.Tags.DeleteTag(projectID, tagName)
	return err
}

// CreateGitLabGroup 创建组
func CreateGitLabGroup(client *gitlab.Client, groupName string) (*gitlab.Group, error) {
	options := &gitlab.CreateGroupOptions{
		Name: gitlab.Ptr(groupName),
		Path: gitlab.Ptr(groupName),
	}
	group, _, err := client.Groups.CreateGroup(options)
	if err != nil {
		return nil, err
	}
	return group, nil
}

// GetGitLabGroups 获取组列表
func GetGitLabGroups(client *gitlab.Client, options *gitlab.ListGroupsOptions) ([]*gitlab.Group, error) {
	if options == nil {
		options = &gitlab.ListGroupsOptions{}
	}
	groups, _, err := client.Groups.ListGroups(options)
	if err != nil {
		return nil, err
	}
	return groups, nil
}

// GetGitLabUsers 获取用户列表
func GetGitLabUsers(client *gitlab.Client, search string) ([]*gitlab.User, error) {
	options := &gitlab.ListUsersOptions{}
	if search != "" {
		options.Search = gitlab.Ptr(search)
	}
	users, _, err := client.Users.ListUsers(options)
	if err != nil {
		return nil, err
	}
	return users, nil
}

// GetGitLabUser 获取单个用户信息
func GetGitLabUser(client *gitlab.Client, userID interface{}) (*gitlab.User, error) {
	// 将 userID 转换为 int
	var uid int
	switch v := userID.(type) {
	case int:
		uid = v
	case string:
		var err error
		uid, err = strconv.Atoi(v)
		if err != nil {
			return nil, fmt.Errorf("invalid user ID: %v", userID)
		}
	default:
		return nil, fmt.Errorf("unsupported user ID type: %T", userID)
	}

	user, _, err := client.Users.GetUser(uid, gitlab.GetUsersOptions{})
	if err != nil {
		return nil, err
	}
	return user, nil
}

// GetGitLabGroupMembers 获取组成员
func GetGitLabGroupMembers(client *gitlab.Client, groupID interface{}) ([]*gitlab.GroupMember, error) {
	members, _, err := client.Groups.ListGroupMembers(groupID, &gitlab.ListGroupMembersOptions{})
	if err != nil {
		return nil, err
	}
	return members, nil
}

// GetGitLabGroupProjects 获取组项目
func GetGitLabGroupProjects(client *gitlab.Client, groupID interface{}) ([]*gitlab.Project, error) {
	projects, _, err := client.Groups.ListGroupProjects(groupID, &gitlab.ListGroupProjectsOptions{})
	if err != nil {
		return nil, err
	}
	return projects, nil
}

// AddGitLabGroupMember 添加组成员
func AddGitLabGroupMember(client *gitlab.Client, groupID interface{}, userID int, accessLevel gitlab.AccessLevelValue) (*gitlab.GroupMember, error) {
	options := &gitlab.AddGroupMemberOptions{
		UserID:      gitlab.Ptr(userID),
		AccessLevel: gitlab.Ptr(accessLevel),
	}
	member, _, err := client.GroupMembers.AddGroupMember(groupID, options)
	if err != nil {
		return nil, err
	}
	return member, nil
}

// RemoveGitLabGroupMember 移除组成员
func RemoveGitLabGroupMember(client *gitlab.Client, groupID interface{}, userID int) error {
	_, err := client.GroupMembers.RemoveGroupMember(groupID, userID, &gitlab.RemoveGroupMemberOptions{})
	return err
}

// ********************** 添加项目成员
func **********************(client *gitlab.Client, projectID interface{}, userID int, accessLevel gitlab.AccessLevelValue) (*gitlab.ProjectMember, error) {
	options := &gitlab.AddProjectMemberOptions{
		UserID:      gitlab.Ptr(userID),
		AccessLevel: gitlab.Ptr(accessLevel),
	}
	member, _, err := client.ProjectMembers.AddProjectMember(projectID, options)
	if err != nil {
		return nil, err
	}
	return member, nil
}

// RemoveGitLabProjectMember 移除项目成员
func RemoveGitLabProjectMember(client *gitlab.Client, projectID interface{}, userID int) error {
	_, err := client.ProjectMembers.DeleteProjectMember(projectID, userID)
	return err
}

// CreateGitLabMergeRequest 创建合并请求
func CreateGitLabMergeRequest(client *gitlab.Client, projectID interface{}, sourceBranch, targetBranch, title string) (*gitlab.MergeRequest, error) {
	options := &gitlab.CreateMergeRequestOptions{
		SourceBranch: gitlab.Ptr(sourceBranch),
		TargetBranch: gitlab.Ptr(targetBranch),
		Title:        gitlab.Ptr(title),
	}
	mergeRequest, _, err := client.MergeRequests.CreateMergeRequest(projectID, options)
	if err != nil {
		return nil, err
	}
	return mergeRequest, nil
}

// GetGitLabMergeRequests 获取合并请求列表
func GetGitLabMergeRequests(client *gitlab.Client, projectID interface{}) ([]*gitlab.BasicMergeRequest, error) {
	mergeRequests, _, err := client.MergeRequests.ListProjectMergeRequests(projectID, &gitlab.ListProjectMergeRequestsOptions{})
	if err != nil {
		return nil, err
	}
	return mergeRequests, nil
}

// GetGitLabCommits 获取提交列表
func GetGitLabCommits(client *gitlab.Client, projectID interface{}, options *gitlab.ListCommitsOptions) ([]*gitlab.Commit, error) {
	if options == nil {
		options = &gitlab.ListCommitsOptions{}
	}
	commits, _, err := client.Commits.ListCommits(projectID, options)
	if err != nil {
		return nil, err
	}
	return commits, nil
}

// GetGitLabIssues 获取问题列表
func GetGitLabIssues(client *gitlab.Client, projectID interface{}, options *gitlab.ListProjectIssuesOptions) ([]*gitlab.Issue, error) {
	if options == nil {
		options = &gitlab.ListProjectIssuesOptions{}
	}
	issues, _, err := client.Issues.ListProjectIssues(projectID, options)
	if err != nil {
		return nil, err
	}
	return issues, nil
}

// CreateGitLabIssue 创建问题
func CreateGitLabIssue(client *gitlab.Client, projectID interface{}, title, description string) (*gitlab.Issue, error) {
	options := &gitlab.CreateIssueOptions{
		Title:       gitlab.Ptr(title),
		Description: gitlab.Ptr(description),
	}
	issue, _, err := client.Issues.CreateIssue(projectID, options)
	if err != nil {
		return nil, err
	}
	return issue, nil
}

// GetGitLabPipelines 获取流水线列表
func GetGitLabPipelines(client *gitlab.Client, projectID interface{}, options *gitlab.ListProjectPipelinesOptions) ([]*gitlab.PipelineInfo, error) {
	if options == nil {
		options = &gitlab.ListProjectPipelinesOptions{}
	}
	pipelines, _, err := client.Pipelines.ListProjectPipelines(projectID, options)
	if err != nil {
		return nil, err
	}
	return pipelines, nil
}

// CreateGitLabPipeline 创建流水线
func CreateGitLabPipeline(client *gitlab.Client, projectID interface{}, ref string) (*gitlab.Pipeline, error) {
	options := &gitlab.CreatePipelineOptions{
		Ref: gitlab.Ptr(ref),
	}
	pipeline, _, err := client.Pipelines.CreatePipeline(projectID, options)
	if err != nil {
		return nil, err
	}
	return pipeline, nil
}

// GetGitLabJobs 获取作业列表
func GetGitLabJobs(client *gitlab.Client, projectID interface{}, options *gitlab.ListJobsOptions) ([]*gitlab.Job, error) {
	if options == nil {
		options = &gitlab.ListJobsOptions{}
	}
	jobs, _, err := client.Jobs.ListProjectJobs(projectID, options)
	if err != nil {
		return nil, err
	}
	return jobs, nil
}

// GetGitLabRepositoryTree 获取仓库文件树
func GetGitLabRepositoryTree(client *gitlab.Client, projectID interface{}, options *gitlab.ListTreeOptions) ([]*gitlab.TreeNode, error) {
	if options == nil {
		options = &gitlab.ListTreeOptions{}
	}
	tree, _, err := client.Repositories.ListTree(projectID, options)
	if err != nil {
		return nil, err
	}
	return tree, nil
}

// GetGitLabFile 获取文件内容
func GetGitLabFile(client *gitlab.Client, projectID interface{}, filePath, ref string) (*gitlab.File, error) {
	options := &gitlab.GetFileOptions{
		Ref: gitlab.Ptr(ref),
	}
	file, _, err := client.RepositoryFiles.GetFile(projectID, filePath, options)
	if err != nil {
		return nil, err
	}
	return file, nil
}

// CreateGitLabFile 创建文件
func CreateGitLabFile(client *gitlab.Client, projectID interface{}, filePath, content, commitMessage, branch string) (*gitlab.FileInfo, error) {
	options := &gitlab.CreateFileOptions{
		Branch:        gitlab.Ptr(branch),
		Content:       gitlab.Ptr(content),
		CommitMessage: gitlab.Ptr(commitMessage),
	}
	file, _, err := client.RepositoryFiles.CreateFile(projectID, filePath, options)
	if err != nil {
		return nil, err
	}
	return file, nil
}

// UpdateGitLabFile 更新文件
func UpdateGitLabFile(client *gitlab.Client, projectID interface{}, filePath, content, commitMessage, branch string) (*gitlab.FileInfo, error) {
	options := &gitlab.UpdateFileOptions{
		Branch:        gitlab.Ptr(branch),
		Content:       gitlab.Ptr(content),
		CommitMessage: gitlab.Ptr(commitMessage),
	}
	file, _, err := client.RepositoryFiles.UpdateFile(projectID, filePath, options)
	if err != nil {
		return nil, err
	}
	return file, nil
}

// DeleteGitLabFile 删除文件
func DeleteGitLabFile(client *gitlab.Client, projectID interface{}, filePath, commitMessage, branch string) error {
	options := &gitlab.DeleteFileOptions{
		Branch:        gitlab.Ptr(branch),
		CommitMessage: gitlab.Ptr(commitMessage),
	}
	_, err := client.RepositoryFiles.DeleteFile(projectID, filePath, options)
	return err
}
