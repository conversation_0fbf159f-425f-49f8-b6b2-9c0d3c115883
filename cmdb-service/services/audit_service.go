package services

import (
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/devops-microservices/cmdb-service/models"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// AuditService 审计日志服务接口
type AuditService interface {
	CreateAuditLog(req *models.AuditLogCreateRequest) error
	GetAuditLogs(page, pageSize int, filters map[string]interface{}) ([]*models.AuditLog, int64, error)
	LogKubernetesOperation(ctx *gin.Context, clusterID uint, clusterName, action, resource, resourceID, namespace, description string, details map[string]interface{}, status string, errorMsg string)
}

// AuditServiceImpl 审计日志服务实现
type AuditServiceImpl struct {
	db     *gorm.DB
	logger *logrus.Logger
}

// NewAuditService 创建审计日志服务
func NewAuditService(db *gorm.DB, logger *logrus.Logger) AuditService {
	return &AuditServiceImpl{
		db:     db,
		logger: logger,
	}
}

// CreateAuditLog 创建审计日志
func (s *AuditServiceImpl) CreateAuditLog(req *models.AuditLogCreateRequest) error {
	detailsJSON, _ := json.Marshal(req.Details)

	auditLog := &models.AuditLog{
		UserID:      req.UserID,
		Username:    req.Username,
		Action:      req.Action,
		Resource:    req.Resource,
		ResourceID:  req.ResourceID,
		ClusterID:   req.ClusterID,
		ClusterName: req.ClusterName,
		Namespace:   req.Namespace,
		Description: req.Description,
		Details:     detailsJSON,
		Status:      req.Status,
		ErrorMsg:    req.ErrorMsg,
		ClientIP:    req.ClientIP,
		UserAgent:   req.UserAgent,
		RequestID:   req.RequestID,
	}

	if err := s.db.Create(auditLog).Error; err != nil {
		s.logger.Errorf("创建审计日志失败: %v", err)
		return err
	}

	s.logger.Infof("审计日志创建成功: 用户=%s, 操作=%s, 资源=%s, 状态=%s",
		req.Username, req.Action, req.Resource, req.Status)

	return nil
}

// GetAuditLogs 获取审计日志列表
func (s *AuditServiceImpl) GetAuditLogs(page, pageSize int, filters map[string]interface{}) ([]*models.AuditLog, int64, error) {
	var logs []*models.AuditLog
	var total int64

	query := s.db.Model(&models.AuditLog{})

	// 应用过滤条件
	if userID, ok := filters["user_id"]; ok {
		query = query.Where("user_id = ?", userID)
	}
	if username, ok := filters["username"]; ok {
		query = query.Where("username LIKE ?", "%"+username.(string)+"%")
	}
	if action, ok := filters["action"]; ok {
		query = query.Where("action = ?", action)
	}
	if resource, ok := filters["resource"]; ok {
		query = query.Where("resource = ?", resource)
	}
	if clusterID, ok := filters["cluster_id"]; ok {
		query = query.Where("cluster_id = ?", clusterID)
	}
	if namespace, ok := filters["namespace"]; ok {
		query = query.Where("namespace = ?", namespace)
	}
	if status, ok := filters["status"]; ok {
		query = query.Where("status = ?", status)
	}
	if startTime, ok := filters["start_time"]; ok {
		query = query.Where("created_at >= ?", startTime)
	}
	if endTime, ok := filters["end_time"]; ok {
		query = query.Where("created_at <= ?", endTime)
	}

	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Order("created_at DESC").Offset(offset).Limit(pageSize).Find(&logs).Error; err != nil {
		return nil, 0, err
	}

	return logs, total, nil
}

// LogKubernetesOperation 记录Kubernetes操作日志
func (s *AuditServiceImpl) LogKubernetesOperation(ctx *gin.Context, clusterID uint, clusterName, action, resource, resourceID, namespace, description string, details map[string]interface{}, status string, errorMsg string) {
	// 从上下文获取用户信息
	userID := uint(1) // 默认值
	username := "system"

	if userIDValue, exists := ctx.Get("user_id"); exists {
		if id, ok := userIDValue.(uint); ok {
			userID = id
		} else if idStr, ok := userIDValue.(string); ok {
			if id, err := strconv.ParseUint(idStr, 10, 32); err == nil {
				userID = uint(id)
			}
		}
	}

	if usernameValue, exists := ctx.Get("username"); exists {
		if name, ok := usernameValue.(string); ok {
			username = name
		}
	}

	// 获取请求信息
	clientIP := ctx.ClientIP()
	userAgent := ctx.GetHeader("User-Agent")
	requestID := ctx.GetHeader("X-Request-ID")
	if requestID == "" {
		requestID = fmt.Sprintf("req_%d", time.Now().UnixNano())
	}

	// 创建审计日志请求
	req := &models.AuditLogCreateRequest{
		UserID:      userID,
		Username:    username,
		Action:      action,
		Resource:    resource,
		ResourceID:  resourceID,
		ClusterID:   clusterID,
		ClusterName: clusterName,
		Namespace:   namespace,
		Description: description,
		Details:     details,
		Status:      status,
		ErrorMsg:    errorMsg,
		ClientIP:    clientIP,
		UserAgent:   userAgent,
		RequestID:   requestID,
	}

	// 异步创建审计日志，避免影响主要业务逻辑
	go func() {
		if err := s.CreateAuditLog(req); err != nil {
			s.logger.Errorf("异步创建审计日志失败: %v", err)
		}
	}()
}
