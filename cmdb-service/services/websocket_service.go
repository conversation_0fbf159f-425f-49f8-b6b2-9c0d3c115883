package services

import (
	"encoding/json"
	"fmt"
	"net/http"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"github.com/sirupsen/logrus"
)

// WebSocketMessage WebSocket消息结构
type WebSocketMessage struct {
	Type      string      `json:"type"`
	ClusterID string      `json:"cluster_id,omitempty"`
	Data      interface{} `json:"data"`
	Timestamp int64       `json:"timestamp"`
}

// WebSocketClient WebSocket客户端
type WebSocketClient struct {
	ID         string
	Conn       *websocket.Conn
	Send       chan WebSocketMessage
	ClusterIDs []string // 订阅的集群ID列表
}

// WebSocketHub WebSocket集线器
type WebSocketHub struct {
	clients    map[*WebSocketClient]bool
	broadcast  chan WebSocketMessage
	register   chan *WebSocketClient
	unregister chan *WebSocketClient
	mutex      sync.RWMutex
	logger     *logrus.Logger
}

// WebSocketService WebSocket服务接口
type WebSocketService interface {
	GetHub() *WebSocketHub
	HandleWebSocket(c *gin.Context)
	BroadcastClusterUpdate(clusterID string, data interface{})
	BroadcastToCluster(clusterID string, messageType string, data interface{})
}

// WebSocketServiceImpl WebSocket服务实现
type WebSocketServiceImpl struct {
	hub    *WebSocketHub
	logger *logrus.Logger
}

var upgrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool {
		return true // 允许所有来源，生产环境应该限制
	},
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
}

// NewWebSocketService 创建WebSocket服务
func NewWebSocketService(logger *logrus.Logger) WebSocketService {
	hub := &WebSocketHub{
		clients:    make(map[*WebSocketClient]bool),
		broadcast:  make(chan WebSocketMessage),
		register:   make(chan *WebSocketClient),
		unregister: make(chan *WebSocketClient),
		logger:     logger,
	}

	service := &WebSocketServiceImpl{
		hub:    hub,
		logger: logger,
	}

	// 启动集线器
	go hub.run()

	return service
}

// GetHub 获取WebSocket集线器
func (s *WebSocketServiceImpl) GetHub() *WebSocketHub {
	return s.hub
}

// HandleWebSocket 处理WebSocket连接
func (s *WebSocketServiceImpl) HandleWebSocket(c *gin.Context) {
	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		s.logger.Errorf("WebSocket升级失败: %v", err)
		return
	}

	clientID := c.Query("client_id")
	if clientID == "" {
		clientID = fmt.Sprintf("client_%d", time.Now().UnixNano())
	}

	client := &WebSocketClient{
		ID:         clientID,
		Conn:       conn,
		Send:       make(chan WebSocketMessage, 256),
		ClusterIDs: []string{},
	}

	s.hub.register <- client

	// 启动客户端的读写协程
	go s.writePump(client)
	go s.readPump(client)
}

// BroadcastClusterUpdate 广播集群更新
func (s *WebSocketServiceImpl) BroadcastClusterUpdate(clusterID string, data interface{}) {
	message := WebSocketMessage{
		Type:      "cluster_update",
		ClusterID: clusterID,
		Data:      data,
		Timestamp: time.Now().Unix(),
	}
	s.hub.broadcast <- message
}

// BroadcastToCluster 向特定集群广播消息
func (s *WebSocketServiceImpl) BroadcastToCluster(clusterID string, messageType string, data interface{}) {
	message := WebSocketMessage{
		Type:      messageType,
		ClusterID: clusterID,
		Data:      data,
		Timestamp: time.Now().Unix(),
	}
	s.hub.broadcast <- message
}

// run 运行WebSocket集线器
func (h *WebSocketHub) run() {
	for {
		select {
		case client := <-h.register:
			h.mutex.Lock()
			h.clients[client] = true
			h.mutex.Unlock()
			h.logger.Infof("WebSocket客户端连接: %s", client.ID)

			// 发送欢迎消息
			welcome := WebSocketMessage{
				Type:      "welcome",
				Data:      map[string]string{"message": "连接成功"},
				Timestamp: time.Now().Unix(),
			}
			select {
			case client.Send <- welcome:
			default:
				close(client.Send)
				h.mutex.Lock()
				delete(h.clients, client)
				h.mutex.Unlock()
			}

		case client := <-h.unregister:
			h.mutex.Lock()
			if _, ok := h.clients[client]; ok {
				delete(h.clients, client)
				close(client.Send)
				h.logger.Infof("WebSocket客户端断开: %s", client.ID)
			}
			h.mutex.Unlock()

		case message := <-h.broadcast:
			h.mutex.RLock()
			for client := range h.clients {
				// 检查客户端是否订阅了该集群
				if message.ClusterID != "" && !h.isClientSubscribed(client, message.ClusterID) {
					continue
				}

				select {
				case client.Send <- message:
				default:
					close(client.Send)
					delete(h.clients, client)
				}
			}
			h.mutex.RUnlock()
		}
	}
}

// isClientSubscribed 检查客户端是否订阅了指定集群
func (h *WebSocketHub) isClientSubscribed(client *WebSocketClient, clusterID string) bool {
	if len(client.ClusterIDs) == 0 {
		return true // 如果没有指定集群，则接收所有消息
	}

	for _, id := range client.ClusterIDs {
		if id == clusterID {
			return true
		}
	}
	return false
}

// readPump 读取客户端消息
func (s *WebSocketServiceImpl) readPump(client *WebSocketClient) {
	defer func() {
		s.hub.unregister <- client
		client.Conn.Close()
	}()

	client.Conn.SetReadLimit(512)
	client.Conn.SetReadDeadline(time.Now().Add(60 * time.Second))
	client.Conn.SetPongHandler(func(string) error {
		client.Conn.SetReadDeadline(time.Now().Add(60 * time.Second))
		return nil
	})

	for {
		_, message, err := client.Conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				s.logger.Errorf("WebSocket读取错误: %v", err)
			}
			break
		}

		// 处理客户端消息
		var msg map[string]interface{}
		if err := json.Unmarshal(message, &msg); err != nil {
			s.logger.Errorf("解析WebSocket消息失败: %v", err)
			continue
		}

		// 处理订阅消息
		if msgType, ok := msg["type"].(string); ok && msgType == "subscribe" {
			if clusterIDs, ok := msg["cluster_ids"].([]interface{}); ok {
				client.ClusterIDs = make([]string, len(clusterIDs))
				for i, id := range clusterIDs {
					if strID, ok := id.(string); ok {
						client.ClusterIDs[i] = strID
					}
				}
				s.logger.Infof("客户端 %s 订阅集群: %v", client.ID, client.ClusterIDs)
			}
		}
	}
}

// writePump 向客户端发送消息
func (s *WebSocketServiceImpl) writePump(client *WebSocketClient) {
	ticker := time.NewTicker(54 * time.Second)
	defer func() {
		ticker.Stop()
		client.Conn.Close()
	}()

	for {
		select {
		case message, ok := <-client.Send:
			client.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if !ok {
				client.Conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			if err := client.Conn.WriteJSON(message); err != nil {
				s.logger.Errorf("WebSocket写入错误: %v", err)
				return
			}

		case <-ticker.C:
			client.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := client.Conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}
