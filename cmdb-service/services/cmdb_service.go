package services

import (
	"fmt"

	"github.com/devops-microservices/cmdb-service/models"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// CMDBService CMDB服务接口
type CMDBService interface {
	// Pipeline相关方法
	GetPipelineList(query *models.PipelineListQuery) ([]*models.Pipeline, int64, error)
	GetPipelineByID(id uint) (*models.Pipeline, error)
	GetPipelineByName(name string) (*models.Pipeline, error)
	CreatePipeline(req *models.PipelineCreateRequest) (*models.Pipeline, error)
	UpdatePipeline(id uint, req *models.PipelineUpdateRequest) (*models.Pipeline, error)
	DeletePipeline(id uint) error
	DeletePipelineByName(name string) error
	ClonePipeline(sourceID uint, cloneInfo map[string]interface{}) (*models.Pipeline, error)
	GetPipelineInheritance(sourceType string, sourceID int) (*models.PipelineInheritanceInfo, error)
	GetEffectivePipeline(sourceType string, sourceID int) (*models.Pipeline, error)

	// Product相关方法
	GetProducts(page, pageSize int, search string) ([]*models.Product, int64, error)
	GetProductByID(id uint) (*models.Product, error)
	CreateProduct(req *models.ProductCreateRequest, creatorID uint) (*models.Product, error)
	UpdateProduct(id uint, updates map[string]interface{}) (*models.Product, error)
	DeleteProduct(id uint) error

	// Environment相关方法
	GetEnvironments(page, pageSize int, search string) ([]*models.Environment, int64, error)
	GetEnvironmentByID(id uint) (*models.Environment, error)
	CreateEnvironment(req *models.EnvironmentCreateRequest) (*models.Environment, error)
	UpdateEnvironment(id uint, updates map[string]interface{}) (*models.Environment, error)
	DeleteEnvironment(id uint) error

	// Project相关方法
	GetProjects(page, pageSize int, search string, productID uint) ([]*models.Project, int64, error)
	GetProjectByID(id uint) (*models.Project, error)
	CreateProject(req *models.ProjectCreateRequest, creatorID uint) (*models.Project, error)
	UpdateProject(id uint, updates map[string]interface{}) (*models.Project, error)
	DeleteProject(id uint) error

	// MicroApp相关方法
	GetMicroApps(page, pageSize int, isFavorite bool, search string, projectID uint, productID uint) ([]*models.MicroApp, int64, error)
	GetMicroAppByID(id uint) (*models.MicroApp, error)
	GetMicroAppByName(name string) (*models.MicroApp, error)
	CreateMicroApp(req *models.MicroAppCreateRequest, creatorID uint) (*models.MicroApp, error)
	UpdateMicroApp(id uint, updates map[string]interface{}) (*models.MicroApp, error)
	DeleteMicroApp(id uint) error

	// AppInfo相关方法
	GetAppInfos(page, pageSize int, search string, appID, envID uint) ([]*models.AppInfo, int64, error)
	GetAppInfoByID(id uint) (*models.AppInfo, error)
	GetAppInfoByName(name string) (*models.AppInfo, error)
	CreateAppInfo(req *models.AppInfoCreateRequest) (*models.AppInfo, error)
	UpdateAppInfo(id uint, updates map[string]interface{}) (*models.AppInfo, error)
	DeleteAppInfo(id uint) error

	// DevLanguage相关方法
	GetDevLanguages(page, pageSize int, search string) ([]*models.DevLanguage, int64, error)
	GetDevLanguageByID(id uint) (*models.DevLanguage, error)
	GetLanguageByName(name string) (*models.DevLanguage, error)
	UpdateLanguage(language *models.DevLanguage) error

	// Region相关方法
	GetRegions(page, pageSize int, search string) ([]*models.Region, int64, error)
	GetRegionByID(id uint) (*models.Region, error)
	GetRegionByName(name string) (*models.Region, error)
	CreateRegion(req *models.RegionCreateRequest) (*models.Region, error)
	UpdateRegion(id uint, updates map[string]interface{}) (*models.Region, error)
	DeleteRegion(id uint) error

	// DataCenter相关方法
	GetDataCenters(page, pageSize int, search string) ([]*models.DataCenter, int64, error)
	GetDataCenterByID(id uint) (*models.DataCenter, error)
	GetDataCenterByName(name string) (*models.DataCenter, error)
	CreateDataCenter(req *models.DataCenterCreateRequest) (*models.DataCenter, error)
	UpdateDataCenter(id uint, updates map[string]interface{}) (*models.DataCenter, error)
	DeleteDataCenter(id uint) error

	// Kubernetes相关方法
	GetKubernetesClusters(page, pageSize int, search string) ([]*models.KubernetesCluster, int64, error)
	GetKubernetesClusterByID(id uint) (*models.KubernetesCluster, error)
	GetKubernetesClusterByName(name string) (*models.KubernetesCluster, error)
	CreateKubernetesCluster(req *models.KubernetesClusterCreateRequest) (*models.KubernetesCluster, error)
	UpdateKubernetesCluster(id uint, updates map[string]interface{}) (*models.KubernetesCluster, error)
	DeleteKubernetesCluster(id uint) error
}

// CMDBServiceImpl CMDB服务实现
type CMDBServiceImpl struct {
	db  *gorm.DB
	log *logrus.Logger
}

type ProjectBasic struct {
	ID   uint   `json:"id"`
	Name string `json:"name"`
}

type ProductBasic struct {
	ID   uint   `json:"id"`
	Name string `json:"name"`
}

// NewCMDBService 创建CMDB服务实例
func NewCMDBService(db *gorm.DB, log *logrus.Logger) CMDBService {
	return &CMDBServiceImpl{
		db:  db,
		log: log,
	}
}

// GenerateProjectID 生成项目ID
func (s *CMDBServiceImpl) GenerateProjectID(product *models.Product, projectName string) string {
	if product != nil && product.NamePrefix != "" {
		return fmt.Sprintf("%s.%s", product.NamePrefix, projectName)
	}
	return projectName
}

// GenerateAppID 生成应用ID
func (s *CMDBServiceImpl) GenerateAppID(project *models.Project, appName string) string {
	if project != nil {
		return fmt.Sprintf("%s.%s", project.ProjectCode, appName)
	}
	return appName
}

// GenerateUniqTag 生成应用信息唯一标识
func (s *CMDBServiceImpl) GenerateUniqTag(app *models.MicroApp, env *models.Environment) string {
	if app != nil && env != nil {
		return fmt.Sprintf("%s.%s", app.AppCode, env.Name)
	}
	return ""
}

// Pipeline相关方法实现
func (s *CMDBServiceImpl) GetPipelineList(query *models.PipelineListQuery) ([]*models.Pipeline, int64, error) {
	var pipelines []*models.Pipeline
	var total int64

	db := s.db.Model(&models.Pipeline{})

	// 添加搜索条件
	if query.Name != "" {
		db = db.Where("name ILIKE ?", "%"+query.Name+"%")
	}
	if query.SourceType != "" {
		db = db.Where("source_type = ?", query.SourceType)
	}
	if query.SourceID > 0 {
		db = db.Where("source_id = ?", query.SourceID)
	}
	if query.IsActive != nil {
		db = db.Where("is_active = ?", *query.IsActive)
	}

	// 获取总数
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (query.Page - 1) * query.PageSize
	if err := db.Offset(offset).Limit(query.PageSize).Order("created_at DESC").Find(&pipelines).Error; err != nil {
		return nil, 0, err
	}

	return pipelines, total, nil
}

// GetPipelineByName 获取流水线配置
func (s *CMDBServiceImpl) GetPipelineByName(name string) (*models.Pipeline, error) {
	var pipeline models.Pipeline
	if err := s.db.Where("name = ?", name).First(&pipeline).Error; err != nil {
		return nil, err
	}
	return &pipeline, nil
}

func (s *CMDBServiceImpl) GetPipelineByID(id uint) (*models.Pipeline, error) {
	var pipeline models.Pipeline
	if err := s.db.First(&pipeline, id).Error; err != nil {
		return nil, err
	}
	return &pipeline, nil
}

func (s *CMDBServiceImpl) CreatePipeline(req *models.PipelineCreateRequest) (*models.Pipeline, error) {
	pipeline := &models.Pipeline{
		Name:           req.Name,
		PipelineType:   req.PipelineType,
		SourceID:       req.SourceID,
		SourceType:     req.SourceType,
		PipelineConfig: req.PipelineConfig,
		Description:    req.Description,
		IsDefault:      req.IsDefault,
		IsActive:       true,
		Version:        req.Version,
	}

	if pipeline.Version == "" {
		pipeline.Version = "1.0.0"
	}

	if err := s.db.Create(pipeline).Error; err != nil {
		return nil, err
	}

	switch req.SourceType {
	case "language":
		s.db.Model(&models.DevLanguage{}).Where("id = ?", req.SourceID).Update("pipeline_id", pipeline.ID)
	case "microapp":
		s.db.Model(&models.MicroApp{}).Where("id = ?", req.SourceID).Update("pipeline_id", pipeline.ID)
	case "appinfo":
		s.db.Model(&models.AppInfo{}).Where("id = ?", req.SourceID).Update("pipeline_id", pipeline.ID)
	default:
		return nil, fmt.Errorf("不支持的源类型: %s", req.SourceType)
	}

	return s.GetPipelineByID(pipeline.ID)
}

func (s *CMDBServiceImpl) UpdatePipeline(id uint, req *models.PipelineUpdateRequest) (*models.Pipeline, error) {
	updates := make(map[string]interface{})

	if req.Name != "" {
		updates["name"] = req.Name
	}
	if req.PipelineConfig.Data != nil {
		updates["pipeline_config"] = req.PipelineConfig
	}
	if req.Description != "" {
		updates["description"] = req.Description
	}
	if req.IsActive != nil {
		updates["is_active"] = *req.IsActive
	}
	if req.Version != "" {
		updates["version"] = req.Version
	}

	if err := s.db.Model(&models.Pipeline{}).Where("id = ?", id).Updates(updates).Error; err != nil {
		return nil, err
	}
	return s.GetPipelineByID(id)
}

func (s *CMDBServiceImpl) DeletePipelineByName(name string) error {
	return s.db.Delete(&models.Pipeline{}, "name = ?", name).Error
}

func (s *CMDBServiceImpl) DeletePipeline(id uint) error {
	return s.db.Delete(&models.Pipeline{}, id).Error
}

// Product相关方法实现
func (s *CMDBServiceImpl) GetProducts(page, pageSize int, search string) ([]*models.Product, int64, error) {
	var products []*models.Product
	var total int64

	query := s.db.Model(&models.Product{}).Preload("Region")

	if search != "" {
		query = query.Where("name LIKE ? OR alias LIKE ?", "%"+search+"%", "%"+search+"%")
	}

	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Find(&products).Error; err != nil {
		return nil, 0, err
	}

	// 收集所有管理员ID
	var allUserIDs []uint
	for _, product := range products {
		if product.Managers.Data != nil {
			managersData, ok := product.Managers.Data.(map[string]interface{})
			if ok {
				// 从managers字段中提取用户ID
				if managerID, exists := managersData["manager"]; exists {
					if id, ok := managerID.(float64); ok && id > 0 {
						allUserIDs = append(allUserIDs, uint(id))
					}
				}
				if techManagerID, exists := managersData["tech_manager"]; exists {
					if id, ok := techManagerID.(float64); ok && id > 0 {
						allUserIDs = append(allUserIDs, uint(id))
					}
				}
			}
		}
	}

	// 批量查询用户信息
	var users []models.UserProfile
	userMap := make(map[uint]models.UserProfile)
	if len(allUserIDs) > 0 {
		s.db.Where("id IN ?", allUserIDs).Find(&users)
		for _, user := range users {
			userMap[user.ID] = user
		}
	}

	// 为每个产品设置完整的管理员信息
	for i := range products {
		if products[i].Managers.Data != nil {
			managersData, ok := products[i].Managers.Data.(map[string]interface{})
			if ok {
				// 创建新的managers数据，包含用户详细信息
				enhancedManagers := make(map[string]interface{})

				// 处理manager
				if managerID, exists := managersData["manager"]; exists {
					if id, ok := managerID.(float64); ok && id > 0 {
						userID := uint(id)
						if user, found := userMap[userID]; found {
							enhancedManagers["manager"] = map[string]interface{}{
								"id":           user.ID,
								"username":     user.Username,
								"display_name": user.FirstName,
								"email":        user.Email,
							}
						} else {
							enhancedManagers["manager"] = map[string]interface{}{
								"id":           userID,
								"username":     fmt.Sprintf("user_%d", userID),
								"display_name": fmt.Sprintf("用户%d", userID),
								"email":        "",
							}
						}
					}
				}

				// 处理developer
				if techManagerID, exists := managersData["tech_manager"]; exists {
					if id, ok := techManagerID.(float64); ok && id > 0 {
						userID := uint(id)
						if user, found := userMap[userID]; found {
							enhancedManagers["tech_manager"] = map[string]interface{}{
								"id":           user.ID,
								"username":     user.Username,
								"display_name": user.FirstName,
								"email":        user.Email,
							}
						} else {
							enhancedManagers["tech_manager"] = map[string]interface{}{
								"id":           userID,
								"username":     fmt.Sprintf("user_%d", userID),
								"display_name": fmt.Sprintf("用户%d", userID),
								"email":        "",
							}
						}
					}
				}
				// 更新产品的managers字段
				products[i].Managers.Data = enhancedManagers
			}
		}
	}

	return products, total, nil
}

func (s *CMDBServiceImpl) GetProductByID(id uint) (*models.Product, error) {
	var product models.Product
	if err := s.db.Preload("Region").First(&product, id).Error; err != nil {
		return nil, err
	}
	return &product, nil
}

func (s *CMDBServiceImpl) CreateProduct(req *models.ProductCreateRequest, creatorID uint) (*models.Product, error) {
	product := &models.Product{
		Name:        req.Name,
		ProductCode: req.ProductCode,
		RegionID:    req.RegionID,
		Description: req.Description,
		NamePrefix:  req.NamePrefix,
		Managers:    req.Managers,
	}

	if err := s.db.Create(product).Error; err != nil {
		return nil, err
	}

	return s.GetProductByID(product.ID)
}

func (s *CMDBServiceImpl) UpdateProduct(id uint, updates map[string]interface{}) (*models.Product, error) {
	if err := s.db.Model(&models.Product{}).Where("id = ?", id).Updates(updates).Error; err != nil {
		return nil, err
	}
	return s.GetProductByID(id)
}

func (s *CMDBServiceImpl) DeleteProduct(id uint) error {
	return s.db.Delete(&models.Product{}, id).Error
}

// Environment相关方法实现
func (s *CMDBServiceImpl) GetEnvironments(page, pageSize int, search string) ([]*models.Environment, int64, error) {
	var environments []*models.Environment
	var total int64

	query := s.db.Model(&models.Environment{})

	if search != "" {
		query = query.Where("name LIKE ? OR alias LIKE ?", "%"+search+"%", "%"+search+"%")
	}

	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	offset := (page - 1) * pageSize
	if err := query.Order("sort_order ASC").Offset(offset).Limit(pageSize).Find(&environments).Error; err != nil {
		return nil, 0, err
	}

	return environments, total, nil
}

func (s *CMDBServiceImpl) GetEnvironmentByID(id uint) (*models.Environment, error) {
	var environment models.Environment
	if err := s.db.First(&environment, id).Error; err != nil {
		return nil, err
	}
	return &environment, nil
}

func (s *CMDBServiceImpl) CreateEnvironment(req *models.EnvironmentCreateRequest) (*models.Environment, error) {
	environment := &models.Environment{
		Name:             req.Name,
		EnvironmentCode:  req.EnvironmentCode,
		TicketEnabled:    req.TicketEnabled,
		MergeEnabled:     req.MergeEnabled,
		TemplateSettings: req.TemplateSettings,
		BranchSettings:   req.BranchSettings,
		ExtraData:        req.ExtraData,
		Description:      req.Description,
		SortOrder:        req.SortOrder,
	}

	if err := s.db.Create(environment).Error; err != nil {
		return nil, err
	}

	return s.GetEnvironmentByID(environment.ID)
}

func (s *CMDBServiceImpl) UpdateEnvironment(id uint, updates map[string]interface{}) (*models.Environment, error) {
	if err := s.db.Model(&models.Environment{}).Where("id = ?", id).Updates(updates).Error; err != nil {
		return nil, err
	}
	return s.GetEnvironmentByID(id)
}

func (s *CMDBServiceImpl) DeleteEnvironment(id uint) error {
	return s.db.Delete(&models.Environment{}, id).Error
}

// Project相关方法实现
func (s *CMDBServiceImpl) GetProjects(page, pageSize int, search string, productID uint) ([]*models.Project, int64, error) {
	var projects []*models.Project
	var total int64

	// 不使用Preload，避免JSONField编码问题
	query := s.db.Model(&models.Project{})

	// 基本条件过滤
	if search != "" {
		query = query.Where("name LIKE ? OR project_code LIKE ?", "%"+search+"%", "%"+search+"%", "%"+search+"%")
	}

	if productID > 0 {
		query = query.Where("product_id = ?", productID)
	}

	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Find(&projects).Error; err != nil {
		return nil, 0, err
	}

	// 收集关联ID
	var productIDs []uint
	var creatorIDs []uint

	for _, project := range projects {
		if project.ProductID != nil {
			productIDs = append(productIDs, *project.ProductID)
		}
		if project.CreatorID != nil {
			creatorIDs = append(creatorIDs, *project.CreatorID)
		}
	}

	// 批量查询产品信息
	var products []models.Product
	productMap := make(map[uint]models.Product)
	if len(productIDs) > 0 {
		s.db.Where("id IN ?", productIDs).Find(&products)
		for _, product := range products {
			productMap[product.ID] = product
		}
	}

	// 收集所有管理员ID
	var allUserIDs []uint
	allUserIDs = append(allUserIDs, creatorIDs...)

	// 从项目的managers字段中提取用户ID
	for _, project := range projects {
		if project.Managers.Data != nil {
			managersData, ok := project.Managers.Data.(map[string]interface{})
			if ok {
				// 从managers字段中提取用户ID
				if managerID, exists := managersData["manager"]; exists {
					if id, ok := managerID.(float64); ok && id > 0 {
						allUserIDs = append(allUserIDs, uint(id))
					}
				}
				if developerID, exists := managersData["developer"]; exists {
					if id, ok := developerID.(float64); ok && id > 0 {
						allUserIDs = append(allUserIDs, uint(id))
					}
				}
				if testerID, exists := managersData["tester"]; exists {
					if id, ok := testerID.(float64); ok && id > 0 {
						allUserIDs = append(allUserIDs, uint(id))
					}
				}
			}
		}
	}

	// 批量查询用户信息
	var users []models.UserProfile
	userMap := make(map[uint]models.UserProfile)
	if len(allUserIDs) > 0 {
		s.db.Where("id IN ?", allUserIDs).Find(&users)
		for _, user := range users {
			userMap[user.ID] = user
		}
	}

	// 为每个项目设置关联信息
	for i := range projects {
		// 设置产品信息
		if projects[i].ProductID != nil {
			if product, exists := productMap[*projects[i].ProductID]; exists {
				projects[i].Product = &product
			}
		}

		// 设置完整的管理员信息
		if projects[i].Managers.Data != nil {
			managersData, ok := projects[i].Managers.Data.(map[string]interface{})
			if ok {
				// 创建新的managers数据，包含用户详细信息
				enhancedManagers := make(map[string]interface{})

				// 处理manager
				if managerID, exists := managersData["manager"]; exists {
					if id, ok := managerID.(float64); ok && id > 0 {
						userID := uint(id)
						if user, found := userMap[userID]; found {
							enhancedManagers["manager"] = map[string]interface{}{
								"id":           user.ID,
								"username":     user.Username,
								"display_name": user.FirstName,
								"email":        user.Email,
							}
						} else {
							enhancedManagers["manager"] = map[string]interface{}{
								"id":           userID,
								"username":     fmt.Sprintf("user_%d", userID),
								"display_name": fmt.Sprintf("用户%d", userID),
								"email":        "",
							}
						}
					}
				}

				// 处理developer
				if developerID, exists := managersData["developer"]; exists {
					if id, ok := developerID.(float64); ok && id > 0 {
						userID := uint(id)
						if user, found := userMap[userID]; found {
							enhancedManagers["developer"] = map[string]interface{}{
								"id":           user.ID,
								"username":     user.Username,
								"display_name": user.FirstName,
								"email":        user.Email,
							}
						} else {
							enhancedManagers["developer"] = map[string]interface{}{
								"id":           userID,
								"username":     fmt.Sprintf("user_%d", userID),
								"display_name": fmt.Sprintf("用户%d", userID),
								"email":        "",
							}
						}
					}
				}

				// 处理tester
				if testerID, exists := managersData["tester"]; exists {
					if id, ok := testerID.(float64); ok && id > 0 {
						userID := uint(id)
						if user, found := userMap[userID]; found {
							enhancedManagers["tester"] = map[string]interface{}{
								"id":           user.ID,
								"username":     user.Username,
								"display_name": user.FirstName,
								"email":        user.Email,
							}
						} else {
							enhancedManagers["tester"] = map[string]interface{}{
								"id":           userID,
								"username":     fmt.Sprintf("user_%d", userID),
								"display_name": fmt.Sprintf("用户%d", userID),
								"email":        "",
							}
						}
					}
				}

				// 更新项目的managers字段
				projects[i].Managers.Data = enhancedManagers
			}
		}
	}

	return projects, total, nil
}

func (s *CMDBServiceImpl) GetProjectByID(id uint) (*models.Project, error) {
	var project models.Project
	if err := s.db.Preload("Product").Preload("Product.Region").First(&project, id).Error; err != nil {
		return nil, err
	}
	return &project, nil
}

func (s *CMDBServiceImpl) CreateProject(req *models.ProjectCreateRequest, creatorID uint) (*models.Project, error) {
	// 获取产品信息用于生成项目ID
	var product *models.Product
	if req.ProductID != nil {
		if err := s.db.First(&product, *req.ProductID).Error; err != nil {
			return nil, fmt.Errorf("产品不存在")
		}
	}

	project := &models.Project{
		ProjectCode:    req.ProjectCode,
		Name:           req.Name,
		ProductID:      req.ProductID,
		CreatorID:      &creatorID,
		Managers:       req.Managers,
		Description:    req.Description,
		NotifySettings: req.NotifySettings,
	}

	if err := s.db.Create(project).Error; err != nil {
		return nil, err
	}

	return s.GetProjectByID(project.ID)
}

func (s *CMDBServiceImpl) UpdateProject(id uint, updates map[string]interface{}) (*models.Project, error) {
	if err := s.db.Model(&models.Project{}).Where("id = ?", id).Updates(updates).Error; err != nil {
		return nil, err
	}
	return s.GetProjectByID(id)
}

func (s *CMDBServiceImpl) DeleteProject(id uint) error {
	return s.db.Delete(&models.Project{}, id).Error
}

// MicroApp相关方法实现
func (s *CMDBServiceImpl) GetMicroApps(page, pageSize int, isFavorite bool, search string, projectID uint, productID uint) ([]*models.MicroApp, int64, error) {
	var apps []*models.MicroApp
	var total int64

	query := s.db.Model(&models.MicroApp{})
	if search != "" {
		query = query.Where("name LIKE ? OR app_code LIKE ?", "%"+search+"%", "%"+search+"%")
	}
	if projectID > 0 {
		query = query.Where("project_id = ?", projectID)
	}
	if productID > 0 {
		query = query.Where("product_id = ?", productID)
	}
	if isFavorite {
		query = query.Where("is_favorite = ?", true)
	}
	if err := query.Count(&total).Error; err != nil {
		s.log.Errorf("统计微应用数量失败: %v", err)
		return nil, 0, err
	}
	// 分页
	offset := (page - 1) * pageSize
	if err := query.Order("created_at DESC").Offset(offset).Limit(pageSize).Find(&apps).Error; err != nil {
		s.log.Errorf("查询微应用列表失败: %v", err)
		return nil, 0, err
	}
	// 关联加载 - 只查询必要字段
	for _, app := range apps {
		if app.ProjectID != nil && *app.ProjectID > 0 {
			var project ProjectBasic
			if err := s.db.Model(&models.Project{}).Select("id, name").Where("id = ?", *app.ProjectID).First(&project).Error; err == nil {
				app.Project = &models.Project{
					BaseModel: models.BaseModel{
						ID: project.ID,
					},
					Name: project.Name,
				}
			}
		}
		if app.ProductID != nil && *app.ProductID > 0 {
			var product ProductBasic
			if err := s.db.Model(&models.Product{}).Select("id, name").Where("id = ?", *app.ProductID).First(&product).Error; err == nil {
				app.Product = &models.Product{
					BaseModel: models.BaseModel{
						ID: product.ID,
					},
					Name: product.Name,
				}
			}
		}
	}
	return apps, total, nil
}

func (s *CMDBServiceImpl) GetMicroAppByName(name string) (*models.MicroApp, error) {
	var app models.MicroApp
	if err := s.db.Where("name = ?", name).First(&app).Error; err != nil {
		return nil, err
	}
	return &app, nil
}

func (s *CMDBServiceImpl) GetMicroAppByID(id uint) (*models.MicroApp, error) {
	var app models.MicroApp
	if err := s.db.First(&app, id).Error; err != nil {
		return nil, err
	}
	// 关联加载 - 只查询必要字段
	if app.ProjectID != nil && *app.ProjectID > 0 {
		var project ProjectBasic
		if err := s.db.Model(&models.Project{}).Select("id, name").Where("id = ?", *app.ProjectID).First(&project).Error; err == nil {
			app.Project = &models.Project{
				BaseModel: models.BaseModel{
					ID: project.ID,
				},
				Name: project.Name,
			}
		}
	}
	if app.ProductID != nil && *app.ProductID > 0 {
		var product ProductBasic
		if err := s.db.Model(&models.Product{}).Select("id, name").Where("id = ?", *app.ProductID).First(&product).Error; err == nil {
			app.Product = &models.Product{
				BaseModel: models.BaseModel{
					ID: product.ID,
				},
				Name: product.Name,
			}
		}
	}
	return &app, nil
}

func (s *CMDBServiceImpl) CreateMicroApp(req *models.MicroAppCreateRequest, creatorID uint) (*models.MicroApp, error) {
	app := &models.MicroApp{
		AppCode:        req.AppCode,
		Name:           req.Name,
		ProductID:      req.ProductID,
		ProjectID:      req.ProjectID,
		CreatorID:      &creatorID,
		RepoSettings:   req.RepoSettings,
		TargetSettings: req.TargetSettings,
		TeamMembers:    req.TeamMembers,
		CategoryName:   req.CategoryName,
		TemplateData:   req.TemplateData,
		LanguageCode:   req.LanguageCode,
		BuildCommand:   req.BuildCommand,
		IsMultiApp:     req.IsMultiApp,
		MultiAppIDs:    req.MultiAppIDs,
		DockerSettings: req.DockerSettings,
		Enabled:        req.Enabled,
		Description:    req.Description,
		NotifySettings: req.NotifySettings,
		EditPermission: req.EditPermission,
		DeploymentType: req.DeploymentType,
		ModuleSettings: req.ModuleSettings,
		ScanBranch:     req.ScanBranch,
		PipelineID:     req.PipelineID,
	}
	if err := s.db.Create(app).Error; err != nil {
		return nil, err
	}
	return s.GetMicroAppByID(app.ID)
}

func (s *CMDBServiceImpl) UpdateMicroApp(id uint, updates map[string]interface{}) (*models.MicroApp, error) {
	if err := s.db.Model(&models.MicroApp{}).Where("id = ?", id).Updates(updates).Error; err != nil {
		return nil, err
	}
	return s.GetMicroAppByID(id)
}

func (s *CMDBServiceImpl) DeleteMicroApp(id uint) error {
	return s.db.Delete(&models.MicroApp{}, id).Error
}

// AppInfo相关方法实现
func (s *CMDBServiceImpl) GetAppInfos(page, pageSize int, search string, appID, envID uint) ([]*models.AppInfo, int64, error) {
	var infos []*models.AppInfo
	var total int64
	query := s.db.Model(&models.AppInfo{})
	if search != "" {
		query = query.Where("unique_tag LIKE ?", "%"+search+"%")
	}
	if appID > 0 {
		query = query.Where("app_id = ?", appID)
	}
	if envID > 0 {
		query = query.Where("environment_id = ?", envID)
	}
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}
	// 分页
	offset := (page - 1) * pageSize
	if err := query.Order("created_at DESC").Offset(offset).Limit(pageSize).Find(&infos).Error; err != nil {
		return nil, 0, err
	}
	// 关联加载
	for _, info := range infos {
		if info.AppID != nil && *info.AppID > 0 {
			var app models.MicroApp
			if err := s.db.First(&app, *info.AppID).Error; err == nil {
				info.App = &app
			}
		}
		if info.EnvironmentID != nil && *info.EnvironmentID > 0 {
			var env models.Environment
			if err := s.db.First(&env, *info.EnvironmentID).Error; err == nil {
				info.Environment = &env
			}
		}
	}
	return infos, total, nil
}

func (s *CMDBServiceImpl) GetAppInfoByName(name string) (*models.AppInfo, error) {
	var appInfo models.AppInfo
	if err := s.db.Where("uniq_tag = ?", name).First(&appInfo).Error; err != nil {
		return nil, err
	}
	return &appInfo, nil
}

func (s *CMDBServiceImpl) GetAppInfoByID(id uint) (*models.AppInfo, error) {
	var info models.AppInfo
	if err := s.db.First(&info, id).Error; err != nil {
		return nil, err
	}
	if info.AppID != nil && *info.AppID > 0 {
		var app models.MicroApp
		if err := s.db.First(&app, *info.AppID).Error; err == nil {
			info.App = &app
		}
	}
	if info.EnvironmentID != nil && *info.EnvironmentID > 0 {
		var env models.Environment
		if err := s.db.First(&env, *info.EnvironmentID).Error; err == nil {
			info.Environment = &env
		}
	}
	return &info, nil
}

func (s *CMDBServiceImpl) CreateAppInfo(req *models.AppInfoCreateRequest) (*models.AppInfo, error) {
	info := &models.AppInfo{
		UniqueTag:            req.UniqueTag,
		AppID:                req.AppID,
		EnvironmentID:        req.EnvironmentID,
		BranchSettings:       req.BranchSettings,
		BuildCommand:         req.BuildCommand,
		VersionNumber:        req.VersionNumber,
		TemplateData:         req.TemplateData,
		PipelineID:           req.PipelineID,
		IsEnabled:            req.IsEnabled,
		Description:          req.Description,
		EditPermission:       req.EditPermission,
		OnlineStatus:         req.OnlineStatus,
		PortSettings:         req.PortSettings,
		KubernetesClusterIDs: req.KubernetesClusterIDs,
	}
	if err := s.db.Create(info).Error; err != nil {
		return nil, err
	}
	return s.GetAppInfoByID(info.ID)
}

func (s *CMDBServiceImpl) UpdateAppInfo(id uint, updates map[string]interface{}) (*models.AppInfo, error) {
	if err := s.db.Model(&models.AppInfo{}).Where("id = ?", id).Updates(updates).Error; err != nil {
		return nil, err
	}
	return s.GetAppInfoByID(id)
}

func (s *CMDBServiceImpl) DeleteAppInfo(id uint) error {
	return s.db.Delete(&models.AppInfo{}, id).Error
}

// KubernetesCluster相关方法实现
func (s *CMDBServiceImpl) GetKubernetesClusters(page, pageSize int, search string) ([]*models.KubernetesCluster, int64, error) {
	var clusters []*models.KubernetesCluster
	var total int64

	query := s.db.Model(&models.KubernetesCluster{}).Preload("DataCenter").Preload("DataCenter.Region")

	if search != "" {
		query = query.Where("name LIKE ?", "%"+search+"%")
	}

	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	offset := (page - 1) * pageSize
	if err := query.Order("sort_order ASC").Offset(offset).Limit(pageSize).Find(&clusters).Error; err != nil {
		return nil, 0, err
	}

	return clusters, total, nil
}

func (s *CMDBServiceImpl) GetKubernetesClusterByID(id uint) (*models.KubernetesCluster, error) {
	var cluster models.KubernetesCluster
	if err := s.db.Preload("DataCenter").Preload("DataCenter.Region").
		Preload("Environments").Preload("Products").First(&cluster, id).Error; err != nil {
		return nil, err
	}
	return &cluster, nil
}

func (s *CMDBServiceImpl) GetKubernetesClusterByName(name string) (*models.KubernetesCluster, error) {
	var cluster models.KubernetesCluster
	if err := s.db.Where("name = ?", name).First(&cluster).Error; err != nil {
		return nil, err
	}
	return &cluster, nil
}

// DevLanguage相关方法实现
func (s *CMDBServiceImpl) GetDevLanguages(page, pageSize int, search string) ([]*models.DevLanguage, int64, error) {
	var languages []*models.DevLanguage
	var total int64

	query := s.db.Model(&models.DevLanguage{})

	if search != "" {
		query = query.Where("name LIKE ? OR language_code LIKE ?", "%"+search+"%", "%"+search+"%")
	}

	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Find(&languages).Error; err != nil {
		return nil, 0, err
	}

	return languages, total, nil
}

func (s *CMDBServiceImpl) GetDevLanguageByID(id uint) (*models.DevLanguage, error) {
	var language models.DevLanguage
	if err := s.db.First(&language, id).Error; err != nil {
		return nil, err
	}
	return &language, nil
}

func (s *CMDBServiceImpl) GetLanguageByName(name string) (*models.DevLanguage, error) {
	var language models.DevLanguage
	if err := s.db.Where("name = ?", name).First(&language).Error; err != nil {
		return nil, err
	}
	return &language, nil
}

func (s *CMDBServiceImpl) UpdateLanguage(language *models.DevLanguage) error {
	return s.db.Model(&models.DevLanguage{}).Where("id = ?", language.ID).Updates(language).Error
}

// Region相关方法实现
func (s *CMDBServiceImpl) GetRegions(page, pageSize int, search string) ([]*models.Region, int64, error) {
	var regions []*models.Region
	var total int64

	query := s.db.Model(&models.Region{})

	if search != "" {
		query = query.Where("name LIKE ?", "%"+search+"%")
	}

	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Find(&regions).Error; err != nil {
		return nil, 0, err
	}

	return regions, total, nil
}

func (s *CMDBServiceImpl) GetRegionByID(id uint) (*models.Region, error) {
	var region models.Region
	if err := s.db.First(&region, id).Error; err != nil {
		return nil, err
	}
	return &region, nil
}

func (s *CMDBServiceImpl) GetRegionByName(name string) (*models.Region, error) {
	var region models.Region
	if err := s.db.Where("name = ?", name).First(&region).Error; err != nil {
		return nil, err
	}
	return &region, nil
}

// DataCenter相关方法实现
func (s *CMDBServiceImpl) GetDataCenters(page, pageSize int, search string) ([]*models.DataCenter, int64, error) {
	var dataCenters []*models.DataCenter
	var total int64

	query := s.db.Model(&models.DataCenter{}).Preload("Region")

	if search != "" {
		query = query.Where("name LIKE ? OR data_center_code LIKE ?", "%"+search+"%", "%"+search+"%")
	}

	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Find(&dataCenters).Error; err != nil {
		return nil, 0, err
	}

	return dataCenters, total, nil
}

func (s *CMDBServiceImpl) GetDataCenterByID(id uint) (*models.DataCenter, error) {
	var dataCenter models.DataCenter
	if err := s.db.Preload("Region").First(&dataCenter, id).Error; err != nil {
		return nil, err
	}
	return &dataCenter, nil
}

func (s *CMDBServiceImpl) GetDataCenterByName(name string) (*models.DataCenter, error) {
	var dataCenter models.DataCenter
	if err := s.db.Where("name = ?", name).First(&dataCenter).Error; err != nil {
		return nil, err
	}
	return &dataCenter, nil
}

func (s *CMDBServiceImpl) CreateDataCenter(req *models.DataCenterCreateRequest) (*models.DataCenter, error) {
	dataCenter := &models.DataCenter{
		Name:           req.Name,
		DataCenterCode: req.DataCenterCode,
		RegionID:       req.RegionID,
		Address:        req.Address,
		Description:    req.Description,
	}
	if err := s.db.Create(dataCenter).Error; err != nil {
		return nil, err
	}
	return s.GetDataCenterByID(dataCenter.ID)
}

func (s *CMDBServiceImpl) UpdateDataCenter(id uint, updates map[string]interface{}) (*models.DataCenter, error) {
	if err := s.db.Model(&models.DataCenter{}).Where("id = ?", id).Updates(updates).Error; err != nil {
		return nil, err
	}
	return s.GetDataCenterByID(id)
}

func (s *CMDBServiceImpl) DeleteDataCenter(id uint) error {
	return s.db.Delete(&models.DataCenter{}, id).Error
}

func (s *CMDBServiceImpl) CreateRegion(req *models.RegionCreateRequest) (*models.Region, error) {
	region := &models.Region{
		Name:        req.Name,
		RegionCode:  req.RegionCode,
		Description: req.Description,
	}
	if err := s.db.Create(region).Error; err != nil {
		return nil, err
	}
	return region, nil
}

func (s *CMDBServiceImpl) UpdateRegion(id uint, updates map[string]interface{}) (*models.Region, error) {
	if err := s.db.Model(&models.Region{}).Where("id = ?", id).Updates(updates).Error; err != nil {
		return nil, err
	}
	return s.GetRegionByID(id)
}

func (s *CMDBServiceImpl) DeleteRegion(id uint) error {
	return s.db.Delete(&models.Region{}, id).Error
}

func (s *CMDBServiceImpl) CreateKubernetesCluster(req *models.KubernetesClusterCreateRequest) (*models.KubernetesCluster, error) {
	kubernetesCluster := &models.KubernetesCluster{
		Name:         req.Name,
		ClusterCode:  req.ClusterCode,
		DataCenterID: req.DataCenterID,
		Description:  req.Description,
	}
	if err := s.db.Create(kubernetesCluster).Error; err != nil {
		return nil, err
	}
	return kubernetesCluster, nil
}

func (s *CMDBServiceImpl) UpdateKubernetesCluster(id uint, updates map[string]interface{}) (*models.KubernetesCluster, error) {
	if err := s.db.Model(&models.KubernetesCluster{}).Where("id = ?", id).Updates(updates).Error; err != nil {
		return nil, err
	}
	return s.GetKubernetesClusterByID(id)
}

func (s *CMDBServiceImpl) DeleteKubernetesCluster(id uint) error {
	return s.db.Delete(&models.KubernetesCluster{}, id).Error
}

// 新增的Pipeline方法实现
func (s *CMDBServiceImpl) ClonePipeline(sourceID uint, cloneInfo map[string]interface{}) (*models.Pipeline, error) {
	// 获取源Pipeline
	sourcePipeline, err := s.GetPipelineByID(sourceID)
	if err != nil {
		return nil, err
	}

	// 创建新的Pipeline
	newPipeline := &models.Pipeline{
		Name:           fmt.Sprintf("%s-copy", sourcePipeline.Name),
		PipelineType:   sourcePipeline.PipelineType,
		SourceType:     sourcePipeline.SourceType,
		PipelineConfig: sourcePipeline.PipelineConfig,
		Description:    sourcePipeline.Description,
		IsDefault:      false,
		IsActive:       true,
		Version:        "1.0.0",
	}

	// 应用克隆信息
	if name, ok := cloneInfo["name"].(string); ok && name != "" {
		newPipeline.Name = name
	}
	if sourceID, ok := cloneInfo["source_id"].(float64); ok {
		newPipeline.SourceID = int(sourceID)
	}
	if description, ok := cloneInfo["description"].(string); ok {
		newPipeline.Description = description
	}

	if err := s.db.Create(newPipeline).Error; err != nil {
		return nil, err
	}

	return s.GetPipelineByID(newPipeline.ID)
}

func (s *CMDBServiceImpl) GetPipelineInheritance(sourceType string, sourceID int) (*models.PipelineInheritanceInfo, error) {
	info := &models.PipelineInheritanceInfo{
		InheritanceChain: []string{},
	}

	switch sourceType {
	case "appinfo":
		appInfo, err := s.GetAppInfoByID(uint(sourceID))
		if err != nil {
			return nil, err
		}

		// 检查AppInfo级别的Pipeline
		if appInfo.PipelineID != nil {
			pipeline, err := s.GetPipelineByID(*appInfo.PipelineID)
			if err == nil {
				info.ModulePipeline = pipeline
				info.EffectivePipeline = pipeline
				info.HasCustomPipeline = true
				info.InheritanceChain = append(info.InheritanceChain, "module")
				return info, nil
			}
		}

		// 检查MicroApp级别的Pipeline
		if appInfo.App != nil && appInfo.App.PipelineID != nil {
			pipeline, err := s.GetPipelineByID(*appInfo.App.PipelineID)
			if err == nil {
				info.AppPipeline = pipeline
				info.EffectivePipeline = pipeline
				info.InheritanceChain = append(info.InheritanceChain, "app")
			}
		}

		// 检查Language级别的Pipeline
		if appInfo.App != nil && appInfo.App.Language != nil && appInfo.App.Language.PipelineID != nil {
			pipeline, err := s.GetPipelineByID(*appInfo.App.Language.PipelineID)
			if err == nil {
				info.LanguagePipeline = pipeline
				if info.EffectivePipeline == nil {
					info.EffectivePipeline = pipeline
				}
				info.InheritanceChain = append(info.InheritanceChain, "language")
			}
		}

	case "microapp":
		microApp, err := s.GetMicroAppByID(uint(sourceID))
		if err != nil {
			return nil, err
		}

		// 检查MicroApp级别的Pipeline
		if microApp.PipelineID != nil {
			pipeline, err := s.GetPipelineByID(*microApp.PipelineID)
			if err == nil {
				info.AppPipeline = pipeline
				info.EffectivePipeline = pipeline
				info.HasCustomPipeline = true
				info.InheritanceChain = append(info.InheritanceChain, "app")
				return info, nil
			}
		}

		// 检查Language级别的Pipeline
		if microApp.Language != nil && microApp.Language.PipelineID != nil {
			pipeline, err := s.GetPipelineByID(*microApp.Language.PipelineID)
			if err == nil {
				info.LanguagePipeline = pipeline
				info.EffectivePipeline = pipeline
				info.InheritanceChain = append(info.InheritanceChain, "language")
			}
		}

	case "language":
		language, err := s.GetDevLanguageByID(uint(sourceID))
		if err != nil {
			return nil, err
		}

		// 检查Language级别的Pipeline
		if language.PipelineID != nil {
			pipeline, err := s.GetPipelineByID(*language.PipelineID)
			if err == nil {
				info.LanguagePipeline = pipeline
				info.EffectivePipeline = pipeline
				info.HasCustomPipeline = true
				info.InheritanceChain = append(info.InheritanceChain, "language")
			}
		}
	}

	return info, nil
}

func (s *CMDBServiceImpl) GetEffectivePipeline(sourceType string, sourceID int) (*models.Pipeline, error) {
	info, err := s.GetPipelineInheritance(sourceType, sourceID)
	if err != nil {
		return nil, err
	}
	return info.EffectivePipeline, nil
}
