#!/bin/bash

# CMDB服务启动脚本

set -e

echo "🚀 启动CMDB服务..."

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo "❌ Go未安装，请先安装Go 1.20+"
    exit 1
fi

# 检查Go版本
GO_VERSION=$(go version | grep -o "go[0-9]*\.[0-9]*" | cut -c 3-)
REQUIRED_VERSION="1.20"

if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$GO_VERSION" | sort -V | head -n1)" != "$REQUIRED_VERSION" ]; then
    echo "❌ Go版本过低，当前版本: $GO_VERSION，要求版本: $REQUIRED_VERSION+"
    exit 1
fi

# 进入项目目录
cd "$(dirname "$0")"

echo "📦 下载依赖..."
go mod download

# 检查配置文件
if [ ! -f "config/config.yaml" ]; then
    echo "⚠️ 配置文件不存在，使用默认配置"
    mkdir -p config
    cat > config/config.yaml << EOF
server:
  host: "0.0.0.0"
  port: 8081
  read_timeout: 60
  write_timeout: 60
  idle_timeout: 60

database:
  type: "sqlite"
  dbname: "cmdb.db"

nats:
  url: "nats://localhost:4222"
  subject: "cmdb.events"

log:
  level: "info"
  format: "json"
  output: "stdout"
EOF
fi

# 构建应用
echo "🔨 构建应用..."
go build -ldflags="-w -s" -o cmdb-service .

# 启动服务
echo "🌐 启动CMDB服务..."
./cmdb-service

echo "✅ CMDB服务已启动" 