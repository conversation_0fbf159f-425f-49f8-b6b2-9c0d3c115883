package routes

import (
	"sort"
	"strings"

	"github.com/devops-microservices/cmdb-service/config"
	"github.com/devops-microservices/cmdb-service/controllers"
	"github.com/devops-microservices/cmdb-service/middlewares"
	"github.com/devops-microservices/cmdb-service/services"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
	"gorm.io/gorm"
)

// SetupRoutes 设置所有路由
func SetupRoutes(router *gin.Engine, db *gorm.DB, cfg *config.Config, log *logrus.Logger) {
	// 初始化服务
	cmdbService := services.NewCMDBService(db, log)
	auditService := services.NewAuditService(db, log)
	wsService := services.NewWebSocketService(log)

	// 初始化控制器
	cmdbController := controllers.NewCMDBController(cmdbService, log, db)
	appController := controllers.NewApplicationController(db, cfg)
	regionController := controllers.NewRegionController(db)
	devLanguageController := controllers.NewDevLanguageController(db)
	kubernetesController := controllers.NewKubernetesController(db, cfg, auditService, wsService)
	pipelineController := controllers.NewPipelineController(cmdbService)

	// 创建认证中间件
	authMiddleware := middlewares.JWTAuthMiddleware(cfg, log)

	// ============== 公共路由（无需认证） ==============
	// 健康检查端点
	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "ok",
			"service": "cmdb-service",
			"version": "v1.0.0",
		})
	})

	// Swagger文档路由
	router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	// ============== API v1 路由（需要认证） ==============
	v1 := router.Group("/api/v1/cmdb")
	v1.Use(authMiddleware)
	{
		// Git相关路由
		gitGroup := v1.Group("/git")
		{
			repoGroup := gitGroup.Group("/repo")
			{
				repoGroup.GET("", appController.GetRepos)
				repoGroup.GET("/branches", appController.GetBranches)
			}
		}

		// Harbor相关路由
		v1.GET("/harbor", appController.GetHarbor)

		// 流水线相关路由
		pipelineGroup := v1.Group("/pipeline")
		{
			pipelineGroup.GET("", pipelineController.GetPipelineList)
			pipelineGroup.GET("/:id", pipelineController.GetPipeline)
			pipelineGroup.POST("", pipelineController.CreatePipeline)
			pipelineGroup.PUT("/:id", pipelineController.UpdatePipeline)
			pipelineGroup.DELETE("/:id", pipelineController.DeletePipeline)
			pipelineGroup.GET("/inherit/:source_type/:source_id", pipelineController.GetPipelineInheritance)
			pipelineGroup.POST("/:id/clone", pipelineController.ClonePipeline)
			pipelineGroup.GET("/name/:name", pipelineController.GetPipelineByName)
		}

		// 产品相关路由
		productGroup := v1.Group("/product")
		{
			productGroup.GET("", cmdbController.GetProducts)
			productGroup.GET("/:id/projects", cmdbController.GetProductProjects)
			productGroup.GET("/:id", cmdbController.GetProduct)
			productGroup.POST("", cmdbController.CreateProduct)
			productGroup.PUT("/:id", cmdbController.UpdateProduct)
			productGroup.DELETE("/:id", cmdbController.DeleteProduct)
		}

		// 环境相关路由
		environmentGroup := v1.Group("/environment")
		{
			environmentGroup.GET("", cmdbController.GetEnvironments)
			environmentGroup.GET("/:id", cmdbController.GetEnvironment)
			environmentGroup.POST("", cmdbController.CreateEnvironment)
			environmentGroup.PUT("/:id", cmdbController.UpdateEnvironment)
			environmentGroup.DELETE("/:id", cmdbController.DeleteEnvironment)
		}

		// 区域相关路由
		regionGroup := v1.Group("/region")
		{
			regionGroup.GET("", regionController.GetRegions)
			regionGroup.GET("/:id", regionController.GetRegion)
			regionGroup.POST("", regionController.CreateRegion)
			regionGroup.PUT("/:id", regionController.UpdateRegion)
			regionGroup.DELETE("/:id", regionController.DeleteRegion)
			regionGroup.GET("/product", regionController.GetRegionProducts)
		}

		// 项目相关路由
		projectGroup := v1.Group("/project")
		{
			projectGroup.GET("", cmdbController.GetProjects)
			projectGroup.GET("/:id", cmdbController.GetProject)
			projectGroup.GET("/robot", appController.GetRobot)
			projectGroup.POST("", cmdbController.CreateProject)
			projectGroup.PUT("/:id", cmdbController.UpdateProject)
			projectGroup.DELETE("/:id", cmdbController.DeleteProject)
			// 项目配置相关路由
			configGroup := projectGroup.Group("/config")
			{
				configGroup.GET("", appController.GetProjectConfigs)
				configGroup.GET("/template/inherit", appController.GetProjectConfigInheritTemplate)
				configGroup.GET("/:id", appController.GetProjectConfig)
				configGroup.POST("", appController.CreateProjectConfig)
				configGroup.PUT("/:id", appController.UpdateProjectConfig)
				configGroup.DELETE("/:id", appController.DeleteProjectConfig)
			}
		}

		// 应用相关路由
		appGroup := v1.Group("/microapp")
		{
			// 保留原有路由作为备用
			appGroup.GET("", cmdbController.GetMicroApps)
			appGroup.POST(":id/favorite", cmdbController.FavoriteMicroApp)
			appGroup.GET("/:id", cmdbController.GetMicroApp)
			appGroup.POST("", cmdbController.CreateMicroApp)
			appGroup.PUT("/:id", cmdbController.UpdateMicroApp)
			appGroup.DELETE("/:id", cmdbController.DeleteMicroApp)

			// 微应用扩展API
			appGroup.GET("/:id/health", cmdbController.GetMicroAppHealth)
			appGroup.GET("/:id/resources", cmdbController.GetMicroAppResources)
			appGroup.POST("/:id/deploy", cmdbController.DeployMicroApp)
			appGroup.GET("/:id/deploy/history", cmdbController.GetDeployHistory)
		}

		// 应用模块路由
		serviceGroup := v1.Group("/service")
		{
			// 应用基本路由
			// serviceGroup.GET("", appController.GetApplications)

			serviceGroup.GET("", appController.GetAppInfos)
			serviceGroup.GET("/template/inherit", appController.GetInheritTemplate)
			serviceGroup.GET("/:id/preview", appController.GetAppInfoPreview)
			serviceGroup.GET("/:id", cmdbController.GetAppInfo)
			serviceGroup.GET("/:id/editor", appController.AppEditor)
			serviceGroup.PUT("/:id/editor", appController.AppEditor)
			serviceGroup.POST("", cmdbController.CreateAppInfo)
			serviceGroup.PUT("/:id", cmdbController.UpdateAppInfo)
			serviceGroup.DELETE("/:id", cmdbController.DeleteAppInfo)
		}

		// Language相关路由
		languageGroup := v1.Group("/language")
		{
			languageGroup.GET("", devLanguageController.GetDevLanguages)
			languageGroup.GET("/:id", devLanguageController.GetDevLanguage)
			languageGroup.GET("/code/:language_code", devLanguageController.GetDevLanguageByCode)
			languageGroup.POST("", devLanguageController.CreateDevLanguage)
			languageGroup.PUT("/:id", devLanguageController.UpdateDevLanguage)
			languageGroup.DELETE("/:id", devLanguageController.DeleteDevLanguage)
			languageGroup.GET("/:id/dockerfile", devLanguageController.GetDockerfile)
			languageGroup.GET("/:id/pipeline", devLanguageController.GetPipeline)
			languageGroup.PUT("/:id/pipeline", devLanguageController.SavePipeline)
		}

		// K8s集群相关路由
		kubernetesGroup := v1.Group("/kubernetes")
		{
			kubernetesGroup.GET("", kubernetesController.GetKubernetesClusters)
			kubernetesGroup.GET("/:id", kubernetesController.GetKubernetesCluster)
			kubernetesGroup.POST("", kubernetesController.CreateKubernetesCluster)
			kubernetesGroup.PUT("/:id", kubernetesController.UpdateKubernetesCluster)
			kubernetesGroup.DELETE("/:id", kubernetesController.DeleteKubernetesCluster)
			kubernetesGroup.GET("/:id/config", kubernetesController.GetKubernetesConfig)
			kubernetesGroup.GET("/:id/info", kubernetesController.GetKubernetesInfo)
			kubernetesGroup.GET("/:id/stats", kubernetesController.GetKubernetesStat)
			kubernetesGroup.GET("/:id/pods", kubernetesController.GetKubernetesPods)
			kubernetesGroup.GET("/:id/events", kubernetesController.GetKubernetesEvents)

			// 命名空间操作路由
			kubernetesGroup.POST("/:id/namespaces", kubernetesController.CreateKubernetesNamespace)

			// Pod 详细操作路由
			kubernetesGroup.GET("/:id/namespaces/:namespace/pods/:name", kubernetesController.GetKubernetesPod)
			kubernetesGroup.GET("/:id/namespaces/:namespace/pods/:name/logs", kubernetesController.GetKubernetesPodLogs)
			kubernetesGroup.DELETE("/:id/namespaces/:namespace/pods/:name", kubernetesController.DeleteKubernetesPod)

			// 批量操作路由
			kubernetesGroup.DELETE("/:id/namespaces/:namespace/pods/batch", kubernetesController.BatchDeleteKubernetesPods)
			kubernetesGroup.POST("/:id/resources/:type/batch", kubernetesController.BatchCreateKubernetesResources)

			// 服务操作路由
			kubernetesGroup.POST("/:id/namespaces/:namespace/services", kubernetesController.CreateKubernetesService)

			// ConfigMap操作路由
			kubernetesGroup.POST("/:id/namespaces/:namespace/configmaps", kubernetesController.CreateKubernetesConfigMap)
		}

		// IDC相关路由
		datacenterGroup := v1.Group("/datacenter")
		{
			datacenterGroup.GET("", cmdbController.GetDataCenters)
			datacenterGroup.GET("/:id", cmdbController.GetDataCenter)
			datacenterGroup.POST("", cmdbController.CreateDataCenter)
			datacenterGroup.PUT("/:id", cmdbController.UpdateDataCenter)
			datacenterGroup.DELETE("/:id", cmdbController.DeleteDataCenter)
		}

		// WebSocket路由
		router.GET("/ws", func(c *gin.Context) {
			// 这里需要在main.go中注入WebSocket服务
			wsService.HandleWebSocket(c)
		})

		// 审计日志路由
		auditGroup := v1.Group("/audit")
		{
			auditGroup.GET("/logs", func(c *gin.Context) {
				// 这里需要在main.go中注入审计服务
				auditService.GetAuditLogs(1, 10, map[string]interface{}{})
			})
		}
	}

	// 打印注册的路由
	printRegisteredRoutes(router, log)
}

// printRegisteredRoutes 打印注册的路由
func printRegisteredRoutes(r *gin.Engine, logger *logrus.Logger) {
	routes := r.Routes()
	if len(routes) == 0 {
		logger.Info("没有注册任何路由")
		return
	}

	// 按路径排序
	sort.Slice(routes, func(i, j int) bool {
		return routes[i].Path < routes[j].Path
	})

	logger.Info("================ 已注册的路由 ================")
	logger.Info("方法    | 路径                                    | 处理器")
	logger.Info("--------|----------------------------------------|------------------")

	for _, route := range routes {
		method := padString(route.Method, 7)
		path := padString(route.Path, 39)
		handler := getHandlerName(route.Handler)
		logger.Infof("%s | %s | %s", method, path, handler)
	}

	logger.Info("=============================================")
	logger.Infof("总计: %d 个路由", len(routes))
	logger.Info("=============================================")

	// 按版本分组统计
	v1Count := 0
	publicCount := 0

	for _, route := range routes {
		if strings.HasPrefix(route.Path, "/api/v1/") {
			v1Count++
		} else {
			publicCount++
		}
	}

	logger.Infof("API v1: %d 个路由", v1Count)
	logger.Infof("公共路由: %d 个路由", publicCount)
}

// padString 字符串填充到指定长度
func padString(s string, length int) string {
	if len(s) >= length {
		return s
	}
	return s + strings.Repeat(" ", length-len(s))
}

// getHandlerName 获取处理器名称
func getHandlerName(handler string) string {
	parts := strings.Split(handler, "/")
	if len(parts) > 0 {
		lastPart := parts[len(parts)-1]
		// 移除包名前缀
		if idx := strings.LastIndex(lastPart, "."); idx != -1 {
			return lastPart[idx+1:]
		}
		return lastPart
	}
	return handler
}
