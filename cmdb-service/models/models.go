package models

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"
)

// 响应码常量
const (
	CodeSuccess            = 200
	CodeCreated            = 201
	CodeBadRequest         = 400
	CodeUnauthorized       = 401
	CodeForbidden          = 403
	CodeNotFound           = 404
	CodeInternalError      = 500
	CodeServiceError       = 503
	CodeServiceUnavailable = 504
	CodeValidationError    = 422
)

// 分页常量
const (
	DefaultPageSize = 20
	MaxPageSize     = 100
)

// BaseModel 基础模型结构
type BaseModel struct {
	ID        uint      `json:"id" gorm:"primarykey"`
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}

// JSONField 定义JSON字段类型，可以存储任意JSON值
type JSONField struct {
	Data interface{}
}

// 实现json.Marshaler接口
func (j JSONField) MarshalJSON() ([]byte, error) {
	if j.Data == nil {
		return []byte("null"), nil
	}
	return json.Marshal(j.Data)
}

// 实现json.Unmarshaler接口
func (j *JSONField) UnmarshalJSON(data []byte) error {
	return json.Unmarshal(data, &j.Data)
}

// 实现driver.Valuer接口，用于数据库写入
func (j JSONField) Value() (driver.Value, error) {
	if j.Data == nil {
		return nil, nil
	}

	// 如果已经是string类型，直接返回
	if str, ok := j.Data.(string); ok {
		// 检查是否是有效的JSON
		var temp interface{}
		if json.Unmarshal([]byte(str), &temp) == nil {
			return str, nil
		}
		// 如果不是有效JSON，则序列化为JSON字符串
		bytes, err := json.Marshal(str)
		if err != nil {
			return nil, nil
		}
		return string(bytes), nil
	}

	// 序列化为JSON字符串
	bytes, err := json.Marshal(j.Data)
	if err != nil {
		return nil, nil
	}

	return string(bytes), nil
}

// 实现sql.Scanner接口，用于数据库读取
func (j *JSONField) Scan(value interface{}) error {
	if value == nil {
		j.Data = nil
		return nil
	}

	var bytes []byte
	switch v := value.(type) {
	case []byte:
		if len(v) == 0 {
			j.Data = nil
			return nil
		}
		bytes = v
	case string:
		if v == "" || v == "null" {
			j.Data = nil
			return nil
		}
		bytes = []byte(v)
	default:
		return fmt.Errorf("cannot scan %T into JSONField", value)
	}

	// 尝试解析JSON
	var temp interface{}
	if err := json.Unmarshal(bytes, &temp); err != nil {
		// 如果解析失败，直接存储原始字符串
		j.Data = string(bytes)
		return nil
	}

	j.Data = temp
	return nil
}

// =====通用响应结构=====

// APIResponse 统一API响应结构
type APIResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// PaginatedResponse 分页响应结构（统一格式：{code, message, data: {items: []}, total, page, page_size}）
type PaginatedResponse struct {
	Code     int         `json:"code"`
	Message  string      `json:"message"`
	Data     interface{} `json:"data"`
	Total    int64       `json:"total"`
	Page     int         `json:"page"`
	PageSize int         `json:"page_size"`
}

// ListDataResponse 列表数据包装结构
type ListDataResponse struct {
	Items interface{} `json:"items"`
}

// DTOResponse 通用DTO响应结构（兼容旧版本）
type DTOResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// DTOListResponse 列表DTO响应结构（兼容旧版本）
type DTOListResponse struct {
	Code     int         `json:"code"`
	Message  string      `json:"message"`
	Data     interface{} `json:"data"`
	Total    int64       `json:"total"`
	Page     int         `json:"page"`
	PageSize int         `json:"page_size"`
}

// DTORequest 通用请求DTO
type DTORequest struct {
	Page     int `json:"page" form:"page" validate:"min=1"`
	PageSize int `json:"page_size" form:"page_size" validate:"min=1,max=100"`
}

// =====CMDB业务模型=====

// DevLanguage 开发语言模型
type DevLanguage struct {
	BaseModel
	LanguageCode   string    `json:"language_code" gorm:"size:100;uniqueIndex;not null;comment:语言代码"`
	Name           string    `json:"name" gorm:"size:100;uniqueIndex;not null;comment:语言名称"`
	BaseImage      JSONField `json:"base_image" gorm:"type:jsonb;comment:基础镜像配置"`
	BuildSettings  JSONField `json:"build_settings" gorm:"type:jsonb;comment:构建配置"`
	PipelineID     *uint     `json:"pipeline_id" gorm:"comment:流水线ID"`
	Pipeline       *Pipeline `json:"pipeline,omitempty" gorm:"foreignKey:PipelineID;constraint:OnDelete:SET NULL"`
	DockerTemplate string    `json:"docker_template" gorm:"type:text;comment:Dockerfile模板"`
	K8sTemplate    string    `json:"k8s_template" gorm:"type:text;comment:K8s模板"`
	Labels         JSONField `json:"labels" gorm:"type:jsonb;comment:标签"`
	Description    string    `json:"description" gorm:"type:text;comment:描述"`
	SortOrder      int       `json:"sort_order" gorm:"default:999;comment:排序"`
	// 反向关联
	// MicroApps []*MicroApp `json:"micro_apps,omitempty" gorm:"foreignKey:LanguageCode;references:LanguageCode"`
}

// TableName 指定表名
func (DevLanguage) TableName() string {
	return "cmdb_dev_languages"
}

// Region 区域模型
type Region struct {
	BaseModel
	RegionCode  string    `json:"region_code" gorm:"size:100;uniqueIndex;not null;comment:区域代码"`
	Name        string    `json:"name" gorm:"size:100;uniqueIndex;not null;comment:区域名称"`
	Description string    `json:"description" gorm:"type:text;comment:描述"`
	ExtraData   JSONField `json:"extra_data" gorm:"type:jsonb;comment:扩展信息"`
}

// TableName 指定表名
func (Region) TableName() string {
	return "cmdb_regions"
}

// DataCenter 数据中心模型
type DataCenter struct {
	BaseModel
	DataCenterCode string `json:"data_center_code" gorm:"size:100;uniqueIndex;not null;comment:数据中心代码"`
	Name           string `json:"name" gorm:"size:100;uniqueIndex;not null;comment:数据中心名称"`
	RegionID       uint   `json:"region_id" gorm:"comment:所属区域ID"`
	Region         Region `json:"region" gorm:"foreignKey:RegionID;constraint:OnDelete:RESTRICT"`
	Address        string `json:"address" gorm:"type:text;comment:地址"`
	Description    string `json:"description" gorm:"type:text;comment:描述"`
}

// TableName 指定表名
func (DataCenter) TableName() string {
	return "cmdb_data_centers"
}

// Product 产品模型
type Product struct {
	BaseModel
	ProductCode string    `json:"product_code" gorm:"size:100;uniqueIndex;not null;comment:产品代码"`
	Name        string    `json:"name" gorm:"size:100;uniqueIndex;not null;comment:产品名称"`
	RegionID    *uint     `json:"region_id" gorm:"comment:所属区域ID"`
	Region      *Region   `json:"region,omitempty" gorm:"foreignKey:RegionID;constraint:OnDelete:SET NULL"`
	Description string    `json:"description" gorm:"type:text;comment:描述"`
	NamePrefix  string    `json:"name_prefix" gorm:"size:100;comment:名称前缀"`
	Managers    JSONField `json:"managers" gorm:"type:jsonb;comment:负责人信息"`
}

// TableName 指定表名
func (Product) TableName() string {
	return "cmdb_products"
}

// Environment 环境模型
type Environment struct {
	BaseModel
	EnvironmentCode  string    `json:"environment_code" gorm:"size:100;uniqueIndex;not null;comment:环境代码"`
	Name             string    `json:"name" gorm:"size:100;uniqueIndex;not null;comment:环境名称"`
	TicketEnabled    int       `json:"ticket_enabled" gorm:"default:0;comment:是否启用工单(0-不启用，1-启用)"`
	MergeEnabled     int       `json:"merge_enabled" gorm:"default:0;comment:是否启用合并(0-不启用，1-启用)"`
	TemplateSettings JSONField `json:"template_settings" gorm:"type:jsonb;comment:模板设置"`
	BranchSettings   JSONField `json:"branch_settings" gorm:"type:jsonb;comment:分支设置"` // {allow_ci: [], allow_cd: [], default: ''}
	ExtraData        JSONField `json:"extra_data" gorm:"type:jsonb;comment:扩展信息"`
	Description      string    `json:"description" gorm:"type:text;comment:描述"`
	SortOrder        int       `json:"sort_order" gorm:"default:999;comment:排序"` // 排序
}

// TableName 指定表名
func (Environment) TableName() string {
	return "cmdb_environments"
}

// KubernetesCluster Kubernetes集群模型
type KubernetesCluster struct {
	BaseModel
	ClusterCode  string    `json:"cluster_code" gorm:"size:100;uniqueIndex;not null;comment:集群代码"`
	Name         string    `json:"name" gorm:"size:100;uniqueIndex;not null;comment:集群名称"`
	VersionInfo  JSONField `json:"version_info" gorm:"type:jsonb;comment:版本信息"`
	Description  string    `json:"description" gorm:"type:text;comment:描述"`
	ConfigData   JSONField `json:"config_data" gorm:"type:jsonb;comment:集群配置(加密存储)"`
	DataCenterID *uint     `json:"data_center_id" gorm:"comment:所属数据中心ID"` // 关联数据中心表
	ClusterType  string    `json:"cluster_type" gorm:"size:100;default:'normal';comment:集群类型"`
	ExtraData    JSONField `json:"extra_data" gorm:"type:jsonb;comment:扩展信息"`
	SortOrder    int       `json:"sort_order" gorm:"default:999;comment:排序"`
	// 多对多关系字段
	Environments []*Environment `json:"environments,omitempty" gorm:"many2many:cmdb_cluster_environments;foreignKey:ID;joinForeignKey:cluster_id;References:ID;joinReferences:environment_id"`
	Products     []*Product     `json:"products,omitempty" gorm:"many2many:cmdb_cluster_products;foreignKey:ID;joinForeignKey:cluster_id;References:ID;joinReferences:product_id"`
}

// TableName 指定表名
func (KubernetesCluster) TableName() string {
	return "cmdb_kubernetes_clusters"
}

// Project 项目模型
type Project struct {
	BaseModel
	ProjectCode    string    `json:"project_code" gorm:"size:128;uniqueIndex;not null;comment:项目代码"`
	Name           string    `json:"name" gorm:"size:100;not null;comment:项目名称"`
	ProductID      *uint     `json:"product_id" gorm:"comment:所属产品ID"`
	Product        *Product  `json:"product,omitempty" gorm:"foreignKey:ProductID;constraint:OnDelete:SET NULL"`
	CreatorID      *uint     `json:"creator_id" gorm:"comment:创建人ID"`
	Managers       JSONField `json:"managers" gorm:"type:jsonb;comment:负责人信息"` // {manager: '', developer: '', tester: ''}
	Description    string    `json:"description" gorm:"type:text;comment:描述"`
	NotifySettings JSONField `json:"notify_settings" gorm:"type:jsonb;comment:通知配置"`
}

// TableName 指定表名
func (Project) TableName() string {
	return "cmdb_projects"
}

// MicroApp 微服务应用模型
type MicroApp struct {
	BaseModel
	AppCode        string       `json:"app_code" gorm:"size:250;uniqueIndex;not null;comment:应用代码"`
	Name           string       `json:"name" gorm:"size:128;not null;comment:应用名称"`
	ProductID      *uint        `json:"product_id" gorm:"comment:所属产品ID"`
	Product        *Product     `json:"product,omitempty" gorm:"foreignKey:ProductID;constraint:OnDelete:SET NULL"`
	ProjectID      *uint        `json:"project_id" gorm:"comment:所属项目ID"`
	Project        *Project     `json:"project,omitempty" gorm:"foreignKey:ProjectID;constraint:OnDelete:SET NULL"`
	CreatorID      *uint        `json:"creator_id" gorm:"comment:创建人ID"`
	RepoSettings   JSONField    `json:"repo_settings" gorm:"type:jsonb;comment:代码仓库配置"`
	TargetSettings JSONField    `json:"target_settings" gorm:"type:jsonb;comment:目标配置"`
	TeamMembers    JSONField    `json:"team_members" gorm:"type:jsonb;comment:团队成员"`
	CategoryName   string       `json:"category_name" gorm:"size:128;comment:应用分类"`
	TemplateData   JSONField    `json:"template_data" gorm:"type:jsonb;comment:应用模板"`
	LanguageCode   string       `json:"language_code" gorm:"size:32;default:'java';comment:开发语言;index"`
	Language       *DevLanguage `json:"language,omitempty" gorm:"foreignKey:LanguageCode;references:LanguageCode;constraint:OnDelete:SET NULL"`
	BuildCommand   string       `json:"build_command" gorm:"size:250;comment:构建命令"`
	IsMultiApp     bool         `json:"is_multi_app" gorm:"default:false;comment:是否多应用"`
	MultiAppIDs    JSONField    `json:"multi_app_ids" gorm:"type:jsonb;comment:多应用ID列表"`
	DockerSettings JSONField    `json:"docker_settings" gorm:"type:jsonb;comment:Docker配置"`
	Enabled        bool         `json:"enabled" gorm:"default:true;comment:是否启用"`
	Description    string       `json:"description" gorm:"type:text;comment:描述"`
	NotifySettings JSONField    `json:"notify_settings" gorm:"type:jsonb;comment:通知配置"`
	EditPermission JSONField    `json:"edit_permission" gorm:"type:jsonb;comment:编辑权限"`
	DeploymentType string       `json:"deployment_type" gorm:"size:8;default:'k8s';comment:部署类型"`
	ModuleSettings JSONField    `json:"module_settings" gorm:"type:jsonb;comment:模块配置"`
	ScanBranch     string       `json:"scan_branch" gorm:"size:64;default:'test';comment:扫描分支"`
	PipelineID     *uint        `json:"pipeline_id" gorm:"comment:流水线ID"`
	Pipeline       *Pipeline    `json:"pipeline,omitempty" gorm:"foreignKey:PipelineID;constraint:OnDelete:SET NULL"`
	Tags           []string     `json:"tags" gorm:"type:jsonb;comment:标签"`
	IsFavorite     bool         `json:"is_favorite" gorm:"default:false;comment:是否收藏"`
	// 反向关联
	AppInfos []*AppInfo `json:"app_infos,omitempty" gorm:"foreignKey:AppID"`
}

// TableName 指定表名
func (MicroApp) TableName() string {
	return "cmdb_micro_apps"
}

// AppInfo 应用信息模型
type AppInfo struct {
	BaseModel
	UniqueTag      string       `json:"unique_tag" gorm:"size:128;uniqueIndex;not null;comment:唯一标识"`
	AppID          *uint        `json:"app_id" gorm:"comment:应用ID"`
	App            *MicroApp    `json:"app,omitempty" gorm:"foreignKey:AppID;constraint:OnDelete:SET NULL"`
	EnvironmentID  *uint        `json:"environment_id" gorm:"comment:环境ID"`
	Environment    *Environment `json:"environment,omitempty" gorm:"foreignKey:EnvironmentID;constraint:OnDelete:SET NULL"`
	BranchSettings JSONField    `json:"branch_settings" gorm:"type:jsonb;comment:分支设置"` // {allow_ci: [], allow_cd: [], default: ''}
	BuildCommand   string       `json:"build_command" gorm:"size:250;comment:构建命令"`
	VersionNumber  string       `json:"version_number" gorm:"size:250;comment:版本号"`
	TemplateData   JSONField    `json:"template_data" gorm:"type:jsonb;comment:模板配置"`
	PipelineID     *uint        `json:"pipeline_id" gorm:"comment:流水线ID"`
	Pipeline       *Pipeline    `json:"pipeline,omitempty" gorm:"foreignKey:PipelineID;constraint:OnDelete:SET NULL"`
	IsEnabled      int          `json:"is_enabled" gorm:"default:1;comment:是否启用"`
	Description    string       `json:"description" gorm:"type:text;comment:描述"`
	EditPermission JSONField    `json:"edit_permission" gorm:"type:jsonb;comment:编辑权限"`
	OnlineStatus   int          `json:"online_status" gorm:"default:0;comment:在线状态"`
	PortSettings   JSONField    `json:"port_settings" gorm:"type:jsonb;comment:端口配置"`
	// 多对多关系
	KubernetesClusters    []*KubernetesCluster `json:"kubernetes_clusters,omitempty" gorm:"many2many:cmdb_app_cluster_deploys;"`
	KubernetesClusterInfo []*KubernetesCluster `json:"kubernetes_cluster_info,omitempty" gorm:"-"`
	KubernetesClusterIDs  []uint               `json:"kubernetes_cluster_ids" gorm:"-"`
}

// TableName 指定表名
func (AppInfo) TableName() string {
	return "cmdb_app_infos"
}

// AppClusterDeploy 应用集群部署关联模型
type AppClusterDeploy struct {
	BaseModel
	AppInfoID     uint              `json:"app_info_id" gorm:"index;uniqueIndex:idx_app_cluster;comment:应用信息ID"`
	AppInfo       AppInfo           `json:"app_info" gorm:"foreignKey:AppInfoID;constraint:OnDelete:CASCADE"`
	ClusterID     uint              `json:"cluster_id" gorm:"index;uniqueIndex:idx_app_cluster;comment:集群ID"`
	Cluster       KubernetesCluster `json:"cluster" gorm:"foreignKey:ClusterID;constraint:OnDelete:CASCADE"`
	OnlineStatus  int               `json:"online_status" gorm:"default:0;comment:在线状态"`
	VersionNumber string            `json:"version_number" gorm:"size:250;comment:版本号"`
}

// TableName 指定表名
func (AppClusterDeploy) TableName() string {
	return "cmdb_app_cluster_deploys"
}

// MicroAppDeployHistory 应用部署历史模型
type MicroAppDeployHistory struct {
	BaseModel
	TaskID          string     `json:"task_id" gorm:"size:128;uniqueIndex;not null;comment:任务ID"`
	AppID           uint       `json:"app_id" gorm:"index;comment:应用ID"`
	App             *MicroApp  `json:"app,omitempty" gorm:"foreignKey:AppID;constraint:OnDelete:SET NULL"`
	EnvironmentName string     `json:"environment_name" gorm:"size:100;index;comment:环境名称"`
	BranchName      string     `json:"branch_name" gorm:"size:100;comment:分支名称"`
	VersionNumber   string     `json:"version_number" gorm:"size:100;comment:版本号"`
	DeployStatus    string     `json:"deploy_status" gorm:"size:50;default:'pending';comment:部署状态"`
	OperatorName    string     `json:"operator_name" gorm:"size:100;comment:操作人姓名"`
	OperatorID      uint       `json:"operator_id" gorm:"comment:操作人ID"`
	CompletedAt     *time.Time `json:"completed_at" gorm:"comment:完成时间"`
	DeployLogs      string     `json:"deploy_logs" gorm:"type:text;comment:部署日志"`
}

// TableName 指定表名
func (MicroAppDeployHistory) TableName() string {
	return "cmdb_micro_app_deploy_histories"
}

// Pipeline 流水线模型
type Pipeline struct {
	BaseModel
	Name           string    `json:"name" gorm:"size:128;uniqueIndex;not null;comment:流水线名称"`                                                    // 流水线名称，唯一
	PipelineType   string    `json:"pipeline_type" gorm:"size:32;not null;comment:流水线类型;index:idx_pipeline_type_source"`                         // 流水线类型（如 build/deploy/full/custom）
	SourceID       int       `json:"source_id" gorm:"not null;index:idx_source_type_id,unique;comment:源ID;"`                                     // 绑定对象ID（如语言ID、应用ID、模块ID）
	SourceType     string    `json:"source_type" gorm:"size:32;not null;index:idx_source_type_id,unique;comment:源类型(language/microapp/appinfo)"` // 绑定对象类型（language=开发语言，microapp=应用，appinfo=应用模块）
	PipelineConfig JSONField `json:"pipeline_config" gorm:"type:jsonb;comment:流水线配置"`                                                            // 流水线详细配置（JSON）
	Description    string    `json:"description" gorm:"type:text;comment:描述"`                                                                    // 流水线描述
	IsDefault      bool      `json:"is_default" gorm:"default:false;comment:是否为默认模板"`                                                            // 是否为默认模板
	IsActive       bool      `json:"is_active" gorm:"default:true;comment:是否激活"`                                                                 // 是否激活
	Version        string    `json:"version" gorm:"size:32;default:'1.0.0';comment:版本号"`                                                         // 配置版本号
	CreatorID      *uint     `json:"creator_id" gorm:"comment:创建人ID"`                                                                            // 创建人ID
}

// TableName 指定表名
func (Pipeline) TableName() string {
	return "cmdb_pipelines"
}

// ProjectConfig 项目配置模型
type ProjectConfig struct {
	BaseModel
	ProjectID     uint        `json:"project_id" gorm:"comment:项目ID"`
	Project       Project     `json:"project" gorm:"foreignKey:ProjectID;constraint:OnDelete:CASCADE"`
	EnvironmentID uint        `json:"environment_id" gorm:"comment:环境ID"`
	Environment   Environment `json:"environment" gorm:"foreignKey:EnvironmentID;constraint:OnDelete:CASCADE"`
	TemplateData  JSONField   `json:"template_data" gorm:"type:jsonb;comment:模板配置"`
}

// TableName 指定表名
func (ProjectConfig) TableName() string {
	return "cmdb_project_configs"
}

// ProductEnvReleaseConfig 产品环境发布配置模型
type ProductEnvReleaseConfig struct {
	BaseModel
	ProductID     uint        `json:"product_id" gorm:"comment:产品ID"`
	Product       Product     `json:"product" gorm:"foreignKey:ProductID;constraint:OnDelete:CASCADE"`
	EnvironmentID uint        `json:"environment_id" gorm:"comment:环境ID"`
	Environment   Environment `json:"environment" gorm:"foreignKey:EnvironmentID;constraint:OnDelete:CASCADE"`
	ConfigData    JSONField   `json:"config_data" gorm:"type:jsonb;comment:配置信息"`
}

// TableName 指定表名
func (ProductEnvReleaseConfig) TableName() string {
	return "cmdb_product_env_release_configs"
}

// ProjectEnvReleaseConfig 项目环境发布配置模型
type ProjectEnvReleaseConfig struct {
	BaseModel
	ProjectID     uint        `json:"project_id" gorm:"comment:项目ID"`
	Project       Project     `json:"project" gorm:"foreignKey:ProjectID;constraint:OnDelete:CASCADE"`
	EnvironmentID uint        `json:"environment_id" gorm:"comment:环境ID"`
	Environment   Environment `json:"environment" gorm:"foreignKey:EnvironmentID;constraint:OnDelete:CASCADE"`
	ConfigData    JSONField   `json:"config_data" gorm:"type:jsonb;comment:配置信息"`
}

// TableName 指定表名
func (ProjectEnvReleaseConfig) TableName() string {
	return "cmdb_project_env_release_configs"
}

// SystemConfig 系统配置模型
type SystemConfig struct {
	BaseModel
	Name   string `json:"name" gorm:"size:64;uniqueIndex;not null"`
	Config string `json:"config" gorm:"type:text"` // 存储原始JSON或加密数据，不再使用GORM序列化器
	Status bool   `json:"status" gorm:"default:false"`
	Type   string `json:"type" gorm:"size:64"`
}

// TableName 指定表名
func (SystemConfig) TableName() string {
	return "ucenter_system_configs"
}

// UserProfile 用户信息（与ucenter-service同步）
type UserProfile struct {
	BaseModel
	Username    string `json:"username" gorm:"size:150;uniqueIndex;not null;comment:用户名"`
	FirstName   string `json:"first_name" gorm:"size:150;comment:名"`
	LastName    string `json:"last_name" gorm:"size:150;comment:姓"`
	Email       string `json:"email" gorm:"size:254;comment:邮箱"`
	Position    string `json:"position" gorm:"size:100;comment:职位"`
	JobTitle    string `json:"job_title" gorm:"size:100;comment:职称"`
	Mobile      string `json:"mobile" gorm:"size:20;comment:手机号"`
	IsActive    bool   `json:"is_active" gorm:"default:true;comment:是否激活"`
	IsSuperuser bool   `json:"is_superuser" gorm:"default:false;comment:是否超级用户"`
}

// TableName 指定表名
func (UserProfile) TableName() string {
	return "ucenter_users"
}

// =====请求响应模型=====

// DevLanguageCreateRequest 创建开发语言请求
type DevLanguageCreateRequest struct {
	LanguageCode   string    `json:"language_code" binding:"required,max=100"`
	Name           string    `json:"name" binding:"required,max=100"`
	BaseImage      JSONField `json:"base_image"`
	BuildSettings  JSONField `json:"build_settings"`
	PipelineConfig JSONField `json:"pipeline_config"`
	DockerTemplate string    `json:"docker_template"`
	PipelineScript string    `json:"pipeline_script"`
	K8sTemplate    string    `json:"k8s_template"`
	Labels         JSONField `json:"labels"`
	Description    string    `json:"description"`
}

// RegionCreateRequest 创建区域请求
type RegionCreateRequest struct {
	RegionCode  string `json:"region_code" binding:"required,max=100"`
	Name        string `json:"name" binding:"required,max=100"`
	Description string `json:"description"`
}

// DataCenterCreateRequest 创建数据中心请求
type DataCenterCreateRequest struct {
	DataCenterCode string `json:"data_center_code" binding:"required,max=100"`
	Name           string `json:"name" binding:"required,max=100"`
	RegionID       uint   `json:"region_id" binding:"required"`
	Address        string `json:"address"`
	Description    string `json:"description"`
}

// ProductCreateRequest 创建产品请求
type ProductCreateRequest struct {
	ProductCode string    `json:"product_code" binding:"required,max=100"`
	Name        string    `json:"name" binding:"required,max=100"`
	RegionID    *uint     `json:"region_id"`
	Description string    `json:"description"`
	NamePrefix  string    `json:"name_prefix" validate:"max=100"`
	Managers    JSONField `json:"managers"`
}

// EnvironmentCreateRequest 创建环境请求
type EnvironmentCreateRequest struct {
	EnvironmentCode  string    `json:"environment_code" binding:"required,max=100"`
	Name             string    `json:"name" binding:"required,max=100"`
	TicketEnabled    int       `json:"ticket_enabled" validate:"oneof=0 1"`
	MergeEnabled     int       `json:"merge_enabled" validate:"oneof=0 1"`
	TemplateSettings JSONField `json:"template_settings"`
	BranchSettings   JSONField `json:"branch_settings"` // {allow_ci: [], allow_cd: []}
	ExtraData        JSONField `json:"extra_data"`
	Description      string    `json:"description"`
	SortOrder        int       `json:"sort_order"`
}

// ProjectCreateRequest 创建项目请求
type ProjectCreateRequest struct {
	ProjectCode    string    `json:"project_code" binding:"required,max=128"`
	Name           string    `json:"name" binding:"required,max=100"`
	ProductID      *uint     `json:"product_id"`
	Managers       JSONField `json:"managers"`
	Description    string    `json:"description"`
	NotifySettings JSONField `json:"notify_settings"`
}

// MicroAppCreateRequest 创建微服务应用请求
type MicroAppCreateRequest struct {
	AppCode        string    `json:"app_code" binding:"required,max=250"`
	Name           string    `json:"name" binding:"required,max=128"`
	ProductID      *uint     `json:"product_id"`
	ProjectID      *uint     `json:"project_id"`
	RepoSettings   JSONField `json:"repo_settings"`
	TargetSettings JSONField `json:"target_settings"`
	TeamMembers    JSONField `json:"team_members"`
	CategoryName   string    `json:"category_name" validate:"max=128"`
	TemplateData   JSONField `json:"template_data"`
	LanguageCode   string    `json:"language_code" validate:"max=32"`
	BuildCommand   string    `json:"build_command" validate:"max=250"`
	IsMultiApp     bool      `json:"is_multi_app"`
	MultiAppIDs    JSONField `json:"multi_app_ids"`
	DockerSettings JSONField `json:"docker_settings"`
	Enabled        bool      `json:"enabled"`
	Description    string    `json:"description"`
	NotifySettings JSONField `json:"notify_settings"`
	EditPermission JSONField `json:"edit_permission"`
	DeploymentType string    `json:"deployment_type"`
	ModuleSettings JSONField `json:"module_settings"`
	ScanBranch     string    `json:"scan_branch"`
	PipelineID     *uint     `json:"pipeline_id"`
}

// AppInfoCreateRequest 创建应用信息请求
type AppInfoCreateRequest struct {
	UniqueTag            string    `json:"unique_tag" binding:"required,max=128"`
	AppID                *uint     `json:"app_id"`
	EnvironmentID        *uint     `json:"environment_id"`
	BranchSettings       JSONField `json:"branch_settings"` // {allow_ci: [], allow_cd: [], default: ''}
	BuildCommand         string    `json:"build_command" validate:"max=250"`
	TemplateData         JSONField `json:"template_data"`
	IsEnabled            int       `json:"is_enabled" validate:"oneof=0 1"`
	Description          string    `json:"description"`
	EditPermission       JSONField `json:"edit_permission"`
	PipelineID           *uint     `json:"pipeline_id"`
	PortSettings         JSONField `json:"port_settings"`
	KubernetesClusterIDs []uint    `json:"kubernetes_cluster_ids"`
	VersionNumber        string    `json:"version_number"`
	OnlineStatus         int       `json:"online_status" validate:"oneof=0 1"`
}

// PipelineCreateRequest 创建流水线请求
type PipelineCreateRequest struct {
	Name           string    `json:"name" binding:"required,max=128"`
	SourceID       int       `json:"source_id" binding:"required"`
	SourceType     string    `json:"source_type" binding:"required,oneof=language microapp appinfo"`
	PipelineType   string    `json:"pipeline_type" binding:"required"`
	PipelineConfig JSONField `json:"pipeline_config"`
	Description    string    `json:"description"`
	IsDefault      bool      `json:"is_default"`
	Version        string    `json:"version"`
}

// KubernetesClusterCreateRequest 创建K8s集群请求
type KubernetesClusterCreateRequest struct {
	Name         string    `json:"name" binding:"required,max=100"`
	ClusterCode  string    `json:"cluster_code" binding:"required,max=100"`
	VersionInfo  JSONField `json:"version_info"`
	Description  string    `json:"description"`
	ConfigData   JSONField `json:"config_data"`
	DataCenterID *uint     `json:"data_center_id"`
	ClusterType  string    `json:"cluster_type" validate:"max=100"`
	ExtraData    JSONField `json:"extra_data"`
	SortOrder    int       `json:"sort_order"`
}

// 查询参数结构

// DevLanguageListQuery 开发语言列表查询参数
type DevLanguageListQuery struct {
	Page     int    `form:"page,default=1"`
	PageSize int    `form:"page_size,default=20"`
	Name     string `form:"name"`
	Language string `form:"language"`
}

// RegionListQuery 区域列表查询参数
type RegionListQuery struct {
	Page     int    `form:"page,default=1"`
	PageSize int    `form:"page_size,default=20"`
	Name     string `form:"name"`
}

// ProductListQuery 产品列表查询参数
type ProductListQuery struct {
	Page     int    `form:"page,default=1"`
	PageSize int    `form:"page_size,default=20"`
	Name     string `form:"name"`
	RegionID *uint  `form:"region_id"`
}

// EnvironmentListQuery 环境列表查询参数
type EnvironmentListQuery struct {
	Page     int    `form:"page,default=1"`
	PageSize int    `form:"page_size,default=20"`
	Name     string `form:"name"`
}

// ProjectListQuery 项目列表查询参数
type ProjectListQuery struct {
	Page        int    `form:"page,default=1"`
	PageSize    int    `form:"page_size,default=20"`
	Name        string `form:"name"`
	ProjectCode string `form:"project_code"`
	ProductID   *uint  `form:"product_id"`
}

// MicroAppListQuery 微服务应用列表查询参数
type MicroAppListQuery struct {
	Page         int    `form:"page,default=1"`
	PageSize     int    `form:"page_size,default=20"`
	Name         string `form:"name"`
	AppCode      string `form:"app_code"`
	ProjectID    *uint  `form:"project_id"`
	Language     string `form:"language"`
	CategoryName string `form:"category_name"`
}

// AppInfoListQuery 应用信息列表查询参数
type AppInfoListQuery struct {
	Page          int    `form:"page,default=1"`
	PageSize      int    `form:"page_size,default=20"`
	UniqueTag     string `form:"unique_tag"`
	AppID         *uint  `form:"app_id"`
	EnvironmentID *uint  `form:"environment_id"`
}

// KubernetesClusterListQuery K8s集群列表查询参数
type KubernetesClusterListQuery struct {
	Page         int    `form:"page,default=1"`
	PageSize     int    `form:"page_size,default=20"`
	Name         string `form:"name"`
	ClusterType  string `form:"cluster_type"`
	DataCenterID *uint  `form:"data_center_id"`
}

// PipelineListQuery Pipeline查询参数
type PipelineListQuery struct {
	Page       int    `form:"page,default=1"`
	PageSize   int    `form:"page_size,default=20"`
	Name       string `form:"name"`
	SourceType string `form:"source_type"`
	SourceID   int    `form:"source_id"`
	IsActive   *bool  `form:"is_active"`
}

// PipelineUpdateRequest Pipeline更新请求
type PipelineUpdateRequest struct {
	Name           string    `json:"name" validate:"max=128"`
	PipelineConfig JSONField `json:"pipeline_config"`
	Description    string    `json:"description"`
	IsActive       *bool     `json:"is_active"`
	Version        string    `json:"version" validate:"max=32"`
}

// PipelineInheritanceInfo Pipeline继承信息
type PipelineInheritanceInfo struct {
	EffectivePipeline *Pipeline `json:"effective_pipeline"`          // 实际生效的Pipeline
	InheritanceChain  []string  `json:"inheritance_chain"`           // 继承链路
	HasCustomPipeline bool      `json:"has_custom_pipeline"`         // 是否有自定义Pipeline
	LanguagePipeline  *Pipeline `json:"language_pipeline,omitempty"` // 语言级Pipeline
	AppPipeline       *Pipeline `json:"app_pipeline,omitempty"`      // 应用级Pipeline
	ModulePipeline    *Pipeline `json:"module_pipeline,omitempty"`   // 模块级Pipeline
}

// DevLanguageWithPipelineInfo 带Pipeline信息的开发语言
type DevLanguageWithPipelineInfo struct {
	DevLanguage
	PipelineInfo *PipelineInheritanceInfo `json:"pipeline_info,omitempty"`
}

// MicroAppWithPipelineInfo 带Pipeline信息的微服务应用
type MicroAppWithPipelineInfo struct {
	MicroApp
	PipelineInfo *PipelineInheritanceInfo `json:"pipeline_info,omitempty"`
}

// AppInfoWithPipelineInfo 带Pipeline信息的应用信息
type AppInfoWithPipelineInfo struct {
	AppInfo
	PipelineInfo *PipelineInheritanceInfo `json:"pipeline_info,omitempty"`
}
