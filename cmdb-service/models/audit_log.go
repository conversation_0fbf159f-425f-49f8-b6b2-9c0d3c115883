package models

import (
	"time"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// AuditLog 审计日志模型
type AuditLog struct {
	ID        uint           `json:"id" gorm:"primarykey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`

	// 基本信息
	UserID     uint   `json:"user_id" gorm:"not null;comment:操作用户ID"`
	Username   string `json:"username" gorm:"size:100;not null;comment:操作用户名"`
	Action     string `json:"action" gorm:"size:50;not null;comment:操作类型"`
	Resource   string `json:"resource" gorm:"size:100;not null;comment:资源类型"`
	ResourceID string `json:"resource_id" gorm:"size:100;comment:资源ID"`

	// Kubernetes相关
	ClusterID   uint   `json:"cluster_id" gorm:"comment:K8s集群ID"`
	ClusterName string `json:"cluster_name" gorm:"size:100;comment:K8s集群名称"`
	Namespace   string `json:"namespace" gorm:"size:100;comment:命名空间"`

	// 操作详情
	Description string         `json:"description" gorm:"type:text;comment:操作描述"`
	Details     datatypes.JSON `json:"details" gorm:"type:json;comment:操作详细信息"`

	// 结果信息
	Status   string `json:"status" gorm:"size:20;not null;comment:操作状态(success/failed)"`
	ErrorMsg string `json:"error_msg" gorm:"type:text;comment:错误信息"`

	// 请求信息
	ClientIP  string `json:"client_ip" gorm:"size:45;comment:客户端IP"`
	UserAgent string `json:"user_agent" gorm:"size:500;comment:用户代理"`
	RequestID string `json:"request_id" gorm:"size:100;comment:请求ID"`
}

// TableName 指定表名
func (AuditLog) TableName() string {
	return "cmdb_audit_logs"
}

// AuditLogCreateRequest 创建审计日志请求
type AuditLogCreateRequest struct {
	UserID      uint                   `json:"user_id"`
	Username    string                 `json:"username"`
	Action      string                 `json:"action"`
	Resource    string                 `json:"resource"`
	ResourceID  string                 `json:"resource_id,omitempty"`
	ClusterID   uint                   `json:"cluster_id,omitempty"`
	ClusterName string                 `json:"cluster_name,omitempty"`
	Namespace   string                 `json:"namespace,omitempty"`
	Description string                 `json:"description"`
	Details     map[string]interface{} `json:"details,omitempty"`
	Status      string                 `json:"status"`
	ErrorMsg    string                 `json:"error_msg,omitempty"`
	ClientIP    string                 `json:"client_ip,omitempty"`
	UserAgent   string                 `json:"user_agent,omitempty"`
	RequestID   string                 `json:"request_id,omitempty"`
}

// 审计日志操作类型常量
const (
	AuditActionCreate = "create"
	AuditActionUpdate = "update"
	AuditActionDelete = "delete"
	AuditActionView   = "view"
	AuditActionBatch  = "batch"
)

// 审计日志资源类型常量
const (
	AuditResourcePod       = "pod"
	AuditResourceNamespace = "namespace"
	AuditResourceService   = "service"
	AuditResourceConfigMap = "configmap"
	AuditResourceSecret    = "secret"
	AuditResourceCluster   = "cluster"
)

// 审计日志状态常量
const (
	AuditStatusSuccess = "success"
	AuditStatusFailed  = "failed"
)
