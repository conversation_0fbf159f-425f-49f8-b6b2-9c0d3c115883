package controllers

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// BaseController 基础控制器
type BaseController struct {
	db        *gorm.DB
	log       *logrus.Logger
	response  *ResponseHelper
	validator *validator.Validate
}

// NewBaseController 创建基础控制器
func NewBaseController(db *gorm.DB, log *logrus.Logger) *BaseController {
	return &BaseController{
		db:        db,
		log:       log,
		response:  NewResponseHelper(),
		validator: validator.New(),
	}
}

// ParseUintParam 解析URL参数为uint
func (c *BaseController) ParseUintParam(ctx *gin.Context, paramName string) (uint, error) {
	param := ctx.Param(paramName)
	id, err := strconv.ParseUint(param, 10, 32)
	if err != nil {
		return 0, err
	}
	return uint(id), nil
}

// ParseIntQuery 解析查询参数为int
func (c *BaseController) ParseIntQuery(ctx *gin.Context, paramName string, defaultValue int) int {
	param := ctx.Query(paramName)
	if param == "" {
		return defaultValue
	}
	value, err := strconv.Atoi(param)
	if err != nil {
		return defaultValue
	}
	return value
}

// ValidateRequest 验证请求数据
func (c *BaseController) ValidateRequest(ctx *gin.Context, req interface{}) bool {
	if err := ctx.ShouldBindJSON(req); err != nil {
		c.response.BadRequest(ctx, "请求参数格式错误: "+err.Error())
		return false
	}

	if err := c.validator.Struct(req); err != nil {
		c.response.ValidationError(ctx, "参数验证失败: "+err.Error())
		return false
	}

	return true
}

// ValidateQuery 验证查询参数
func (c *BaseController) ValidateQuery(ctx *gin.Context, req interface{}) bool {
	if err := ctx.ShouldBindQuery(req); err != nil {
		c.response.BadRequest(ctx, "查询参数格式错误: "+err.Error())
		return false
	}

	if err := c.validator.Struct(req); err != nil {
		c.response.ValidationError(ctx, "查询参数验证失败: "+err.Error())
		return false
	}

	return true
}

// GetPaginationParams 获取分页参数
func (c *BaseController) GetPaginationParams(ctx *gin.Context) (page, pageSize int) {
	page = c.ParseIntQuery(ctx, "page", 1)
	pageSize = c.ParseIntQuery(ctx, "page_size", 20)

	// 限制页大小
	if pageSize > 100 {
		pageSize = 100
	}
	if pageSize < 1 {
		pageSize = 20
	}
	if page < 1 {
		page = 1
	}

	return page, pageSize
}
