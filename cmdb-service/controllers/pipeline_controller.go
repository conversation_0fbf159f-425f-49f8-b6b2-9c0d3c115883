package controllers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"github.com/devops-microservices/cmdb-service/models"
	"github.com/devops-microservices/cmdb-service/services"
)

type PipelineController struct {
	service services.CMDBService
}

func NewPipelineController(service services.CMDBService) *PipelineController {
	return &PipelineController{service: service}
}

// CreatePipeline 创建Pipeline
// @Summary 创建Pipeline
// @Description 创建新的Pipeline配置
// @Tags Pipeline
// @Accept json
// @Produce json
// @Param pipeline body models.PipelineCreateRequest true "Pipeline信息"
// @Success 201 {object} models.APIResponse{data=models.Pipeline}
// @Failure 400 {object} models.APIResponse
// @Failure 500 {object} models.APIResponse
// @Router /api/v1/pipelines [post]
func (pc *PipelineController) CreatePipeline(c *gin.Context) {
	var req models.PipelineCreateRequest
	if err := c.ShouldBindJ<PERSON>(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Code:    models.CodeBadRequest,
			Message: "请求参数错误: " + err.Error(),
		})
		return
	}

	pipeline, err := pc.service.CreatePipeline(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Code:    models.CodeInternalError,
			Message: "创建Pipeline失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, models.APIResponse{
		Code:    models.CodeCreated,
		Message: "Pipeline创建成功",
		Data:    pipeline,
	})
}

// GetPipelineList 获取Pipeline列表
// @Summary 获取Pipeline列表
// @Description 分页获取Pipeline列表
// @Tags Pipeline
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param name query string false "名称搜索"
// @Param source_type query string false "源类型"
// @Param source_id query int false "源ID"
// @Param is_active query bool false "是否激活"
// @Success 200 {object} models.PaginatedResponse{data=models.ListDataResponse{items=[]models.Pipeline}}
// @Failure 400 {object} models.APIResponse
// @Failure 500 {object} models.APIResponse
// @Router /api/v1/pipelines [get]
func (pc *PipelineController) GetPipelineList(c *gin.Context) {
	var query models.PipelineListQuery
	if err := c.ShouldBindQuery(&query); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Code:    models.CodeBadRequest,
			Message: "查询参数错误: " + err.Error(),
		})
		return
	}

	pipelines, total, err := pc.service.GetPipelineList(&query)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Code:    models.CodeInternalError,
			Message: "获取Pipeline列表失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.PaginatedResponse{
		Code:     models.CodeSuccess,
		Message:  "获取Pipeline列表成功",
		Data:     models.ListDataResponse{Items: pipelines},
		Total:    total,
		Page:     query.Page,
		PageSize: query.PageSize,
	})
}

// GetPipeline 获取Pipeline详情
// @Summary 获取Pipeline详情
// @Description 根据ID获取Pipeline详细信息
// @Tags Pipeline
// @Accept json
// @Produce json
// @Param id path int true "Pipeline ID"
// @Success 200 {object} models.APIResponse{data=models.Pipeline}
// @Failure 400 {object} models.APIResponse
// @Failure 404 {object} models.APIResponse
// @Failure 500 {object} models.APIResponse
// @Router /api/v1/pipelines/{id} [get]
func (pc *PipelineController) GetPipeline(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Code:    models.CodeBadRequest,
			Message: "无效的Pipeline ID",
		})
		return
	}

	pipeline, err := pc.service.GetPipelineByID(uint(id))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, models.APIResponse{
				Code:    models.CodeNotFound,
				Message: "Pipeline不存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Code:    models.CodeInternalError,
			Message: "获取Pipeline失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Code:    models.CodeSuccess,
		Message: "获取Pipeline成功",
		Data:    pipeline,
	})
}

// UpdatePipeline 更新Pipeline
// @Summary 更新Pipeline
// @Description 更新Pipeline配置
// @Tags Pipeline
// @Accept json
// @Produce json
// @Param id path int true "Pipeline ID"
// @Param pipeline body models.PipelineUpdateRequest true "Pipeline更新信息"
// @Success 200 {object} models.APIResponse{data=models.Pipeline}
// @Failure 400 {object} models.APIResponse
// @Failure 404 {object} models.APIResponse
// @Failure 500 {object} models.APIResponse
// @Router /api/v1/pipelines/{id} [put]
func (pc *PipelineController) UpdatePipeline(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Code:    models.CodeBadRequest,
			Message: "无效的Pipeline ID",
		})
		return
	}

	var req models.PipelineUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Code:    models.CodeBadRequest,
			Message: "请求参数错误: " + err.Error(),
		})
		return
	}

	pipeline, err := pc.service.UpdatePipeline(uint(id), &req)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, models.APIResponse{
				Code:    models.CodeNotFound,
				Message: "Pipeline不存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Code:    models.CodeInternalError,
			Message: "更新Pipeline失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Code:    models.CodeSuccess,
		Message: "Pipeline更新成功",
		Data:    pipeline,
	})
}

// DeletePipeline 删除Pipeline
// @Summary 删除Pipeline
// @Description 删除Pipeline配置
// @Tags Pipeline
// @Accept json
// @Produce json
// @Param id path int true "Pipeline ID"
// @Success 200 {object} models.APIResponse
// @Failure 400 {object} models.APIResponse
// @Failure 404 {object} models.APIResponse
// @Failure 500 {object} models.APIResponse
// @Router /api/v1/pipelines/{id} [delete]
func (pc *PipelineController) DeletePipeline(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Code:    models.CodeBadRequest,
			Message: "无效的Pipeline ID",
		})
		return
	}

	err = pc.service.DeletePipeline(uint(id))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, models.APIResponse{
				Code:    models.CodeNotFound,
				Message: "Pipeline不存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Code:    models.CodeInternalError,
			Message: "删除Pipeline失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Code:    models.CodeSuccess,
		Message: "Pipeline删除成功",
	})
}

// GetPipelineInheritance 获取Pipeline继承信息
// @Summary 获取Pipeline继承信息
// @Description 获取指定源的Pipeline继承链路信息
// @Tags Pipeline
// @Accept json
// @Produce json
// @Param source_type path string true "源类型" Enums(language,microapp,appinfo)
// @Param source_id path int true "源ID"
// @Success 200 {object} models.APIResponse{data=models.PipelineInheritanceInfo}
// @Failure 400 {object} models.APIResponse
// @Failure 404 {object} models.APIResponse
// @Failure 500 {object} models.APIResponse
// @Router /api/v1/pipelines/inheritance/{source_type}/{source_id} [get]
func (pc *PipelineController) GetPipelineInheritance(c *gin.Context) {
	sourceType := c.Param("source_type")
	sourceIDStr := c.Param("source_id")

	sourceID, err := strconv.Atoi(sourceIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Code:    models.CodeBadRequest,
			Message: "无效的源ID",
		})
		return
	}

	if sourceType != "language" && sourceType != "microapp" && sourceType != "appinfo" {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Code:    models.CodeBadRequest,
			Message: "无效的源类型",
		})
		return
	}

	inheritanceInfo, err := pc.service.GetPipelineInheritance(sourceType, sourceID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Code:    models.CodeInternalError,
			Message: "获取Pipeline继承信息失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Code:    models.CodeSuccess,
		Message: "获取Pipeline继承信息成功",
		Data:    inheritanceInfo,
	})
}

// GetPipelineByName 根据名称获取Pipeline
// @Summary 根据名称获取Pipeline
// @Description 根据名称获取Pipeline详细信息
// @Tags Pipeline
// @Accept json
// @Produce json
// @Param name path string true "Pipeline名称"
// @Success 200 {object} models.APIResponse{data=models.Pipeline}
// @Failure 400 {object} models.APIResponse
// @Failure 404 {object} models.APIResponse
// @Failure 500 {object} models.APIResponse
// @Router /api/v1/pipelines/name/{name} [get]
func (pc *PipelineController) GetPipelineByName(c *gin.Context) {
	name := c.Param("name")
	pipeline, err := pc.service.GetPipelineByName(name)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Code:    models.CodeInternalError,
			Message: "获取Pipeline失败: " + err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, models.APIResponse{
		Code:    models.CodeSuccess,
		Message: "获取Pipeline成功",
		Data:    pipeline,
	})
}

// ClonePipeline 克隆Pipeline
// @Summary 克隆Pipeline
// @Description 从现有Pipeline克隆创建新的Pipeline
// @Tags Pipeline
// @Accept json
// @Produce json
// @Param id path int true "源Pipeline ID"
// @Param clone_info body map[string]interface{} true "克隆信息"
// @Success 201 {object} models.APIResponse{data=models.Pipeline}
// @Failure 400 {object} models.APIResponse
// @Failure 404 {object} models.APIResponse
// @Failure 500 {object} models.APIResponse
// @Router /api/v1/pipelines/{id}/clone [post]
func (pc *PipelineController) ClonePipeline(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Code:    models.CodeBadRequest,
			Message: "无效的Pipeline ID",
		})
		return
	}

	var cloneInfo map[string]interface{}
	if err := c.ShouldBindJSON(&cloneInfo); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Code:    models.CodeBadRequest,
			Message: "请求参数错误: " + err.Error(),
		})
		return
	}

	pipeline, err := pc.service.ClonePipeline(uint(id), cloneInfo)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, models.APIResponse{
				Code:    models.CodeNotFound,
				Message: "源Pipeline不存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Code:    models.CodeInternalError,
			Message: "克隆Pipeline失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, models.APIResponse{
		Code:    models.CodeCreated,
		Message: "Pipeline克隆成功",
		Data:    pipeline,
	})
}
