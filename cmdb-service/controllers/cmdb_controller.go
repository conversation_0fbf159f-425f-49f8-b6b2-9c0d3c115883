package controllers

import (
	"fmt"
	"strings"
	"time"

	"github.com/devops-microservices/cmdb-service/models"
	"github.com/devops-microservices/cmdb-service/services"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// CMDBController CMDB控制器
type CMDBController struct {
	*BaseController
	service services.CMDBService
}

// NewCMDBController 创建CMDB控制器实例
func NewCMDBController(service services.CMDBService, log *logrus.Logger, db *gorm.DB) *CMDBController {
	return &CMDBController{
		BaseController: NewBaseController(db, log),
		service:        service,
	}
}

// updatePipelineID 更新pipeline_id（优化版本）
func (c *CMDBController) updatePipelineID(pipelineID uint, pipelineType string, sourceID int) error {
	switch pipelineType {
	case "language":
		language, err := c.service.GetDevLanguageByID(uint(sourceID))
		if err != nil {
			c.log.Errorf("获取语言配置失败: %v", err)
			return fmt.Errorf("获取语言配置失败: %v", err)
		}
		language.PipelineID = &pipelineID
		return c.service.UpdateLanguage(language)

	case "microapp":
		app, err := c.service.GetMicroAppByID(uint(sourceID))
		if err != nil {
			c.log.Errorf("获取应用配置失败: %v", err)
			return fmt.Errorf("获取应用配置失败: %v", err)
		}
		_, err = c.service.UpdateMicroApp(app.ID, map[string]interface{}{
			"pipeline_id": pipelineID,
		})
		return err

	case "appinfo":
		appinfo, err := c.service.GetAppInfoByID(uint(sourceID))
		if err != nil {
			c.log.Errorf("获取应用配置失败: %v", err)
			return fmt.Errorf("获取应用配置失败: %v", err)
		}
		_, err = c.service.UpdateAppInfo(appinfo.ID, map[string]interface{}{
			"pipeline_id": pipelineID,
		})
		return err

	default:
		return fmt.Errorf("不支持的流水线类型: %s", pipelineType)
	}
}

// // ============== Pipeline相关接口 ==============

// // @Summary 获取流水线配置列表
// // @Description 获取流水线配置列表，支持分页
// // @Tags Pipeline
// // @Accept json
// // @Produce json
// // @Param page query int false "页码" default(1)
// // @Param page_size query int false "每页数量" default(20)
// // @Success 200 {object} models.DTOListResponse
// // @Router /api/v1/cmdb/pipeline [get]
// func (c *CMDBController) GetPipelines(ctx *gin.Context) {
// 	pipelines, err := c.service.GetPipelines()
// 	if err != nil {
// 		c.log.Errorf("获取流水线配置失败: %v", err)
// 		c.response.InternalError(ctx, "获取流水线配置失败")
// 		return
// 	}

// 	c.response.Success(ctx, pipelines)
// }

// // @Summary 根据名称获取流水线配置
// // @Description 根据名称获取流水线配置详情
// // @Tags Pipeline
// // @Accept json
// // @Produce json
// // @Param name path string true "流水线名称"
// // @Success 200 {object} models.DTOResponse
// // @Failure 404 {object} models.DTOResponse
// // @Router /api/v1/cmdb/pipeline/name/{name} [get]
// func (c *CMDBController) GetPipelineByName(ctx *gin.Context) {
// 	name := ctx.Param("name")
// 	if name == "" {
// 		c.response.BadRequest(ctx, "流水线名称不能为空")
// 		return
// 	}

// 	pipeline, err := c.service.GetPipelineByName(name)
// 	if err != nil {
// 		if err == gorm.ErrRecordNotFound {
// 			c.response.NotFound(ctx, "流水线配置不存在")
// 			return
// 		}
// 		c.log.Errorf("获取流水线配置失败: %v", err)
// 		c.response.InternalError(ctx, "获取流水线配置失败")
// 		return
// 	}

// 	c.response.Success(ctx, pipeline)
// }

// // @Summary 根据ID获取流水线配置
// // @Description 根据ID获取流水线配置详情
// // @Tags Pipeline
// // @Accept json
// // @Produce json
// // @Param id path int true "流水线ID"
// // @Success 200 {object} models.DTOResponse
// // @Failure 404 {object} models.DTOResponse
// // @Router /api/v1/cmdb/pipeline/{id} [get]
// func (c *CMDBController) GetPipeline(ctx *gin.Context) {
// 	id, err := c.ParseUintParam(ctx, "id")
// 	if err != nil {
// 		c.response.BadRequest(ctx, "无效的流水线ID")
// 		return
// 	}

// 	pipeline, err := c.service.GetPipelineByID(id)
// 	if err != nil {
// 		if err == gorm.ErrRecordNotFound {
// 			c.response.NotFound(ctx, "流水线配置不存在")
// 			return
// 		}
// 		c.log.Errorf("获取流水线配置失败: %v", err)
// 		c.response.InternalError(ctx, "获取流水线配置失败")
// 		return
// 	}

// 	c.response.Success(ctx, pipeline)
// }

// // @Summary 继承流水线配置
// // @Description 继承流水线配置，删除现有配置并重置关联
// // @Tags Pipeline
// // @Accept json
// // @Produce json
// // @Param pipeline body models.PipelineCreateRequest true "流水线配置"
// // @Success 200 {object} models.DTOResponse
// // @Router /api/v1/cmdb/pipeline/inherit [post]
// func (c *CMDBController) InheritPipeline(ctx *gin.Context) {
// 	var req models.PipelineCreateRequest
// 	if !c.ValidateRequest(ctx, &req) {
// 		return
// 	}

// 	// 删除现有配置
// 	if err := c.service.DeletePipelineByName(req.Name); err != nil {
// 		c.log.Errorf("删除流水线配置失败: %v", err)
// 	}

// 	// 重置关联的pipeline_id
// 	if err := c.updatePipelineID(0, req.PipelineType, req.SourceID); err != nil {
// 		c.log.Errorf("重置流水线关联失败: %v", err)
// 		c.response.InternalError(ctx, "重置流水线关联失败")
// 		return
// 	}

// 	c.response.Success(ctx, gin.H{"message": "流水线配置继承成功"})
// }

// // @Summary 创建流水线配置
// // @Description 创建新的流水线配置
// // @Tags Pipeline
// // @Accept json
// // @Produce json
// // @Param pipeline body models.PipelineCreateRequest true "流水线配置"
// // @Success 201 {object} models.DTOResponse
// // @Router /api/v1/cmdb/pipeline [post]
// func (c *CMDBController) CreatePipeline(ctx *gin.Context) {
// 	var req models.PipelineCreateRequest
// 	if !c.ValidateRequest(ctx, &req) {
// 		return
// 	}

// 	pipeline, err := c.service.CreatePipeline(&req)
// 	if err != nil {
// 		c.log.Errorf("创建流水线配置失败: %v", err)
// 		c.response.InternalError(ctx, "创建流水线配置失败")
// 		return
// 	}

// 	// 更新关联的pipeline_id
// 	if err := c.updatePipelineID(pipeline.ID, req.PipelineType, req.SourceID); err != nil {
// 		c.log.Errorf("更新流水线关联失败: %v", err)
// 		// 不影响创建结果，只记录日志
// 	}

// 	c.response.Created(ctx, pipeline)
// }

// // @Summary 更新流水线配置
// // @Description 更新指定ID的流水线配置
// // @Tags Pipeline
// // @Accept json
// // @Produce json
// // @Param id path int true "流水线ID"
// // @Param pipeline body models.PipelineCreateRequest true "流水线配置"
// // @Success 200 {object} models.DTOResponse
// // @Failure 404 {object} models.DTOResponse
// // @Router /api/v1/cmdb/pipeline/{id} [put]
// func (c *CMDBController) UpdatePipeline(ctx *gin.Context) {
// 	id, err := c.ParseUintParam(ctx, "id")
// 	if err != nil {
// 		c.response.BadRequest(ctx, "无效的流水线ID")
// 		return
// 	}

// 	var req models.PipelineCreateRequest
// 	if !c.ValidateRequest(ctx, &req) {
// 		return
// 	}

// 	updates := map[string]interface{}{
// 		"source_id":       req.SourceID,
// 		"name":            req.Name,
// 		"pipeline_type":   req.PipelineType,
// 		"pipeline_config": req.PipelineConfig,
// 		"updated_at":      time.Now(),
// 	}

// 	pipeline, err := c.service.UpdatePipeline(id, updates)
// 	if err != nil {
// 		if err == gorm.ErrRecordNotFound {
// 			c.response.NotFound(ctx, "流水线配置不存在")
// 			return
// 		}
// 		c.log.Errorf("更新流水线配置失败: %v", err)
// 		c.response.InternalError(ctx, "更新流水线配置失败")
// 		return
// 	}

// 	// 更新关联的pipeline_id
// 	if err := c.updatePipelineID(pipeline.ID, req.PipelineType, req.SourceID); err != nil {
// 		c.log.Errorf("更新流水线关联失败: %v", err)
// 	}

// 	c.response.Success(ctx, pipeline)
// }

// // @Summary 删除流水线配置
// // @Description 删除指定ID的流水线配置
// // @Tags Pipeline
// // @Accept json
// // @Produce json
// // @Param id path int true "流水线ID"
// // @Success 200 {object} models.DTOResponse
// // @Failure 404 {object} models.DTOResponse
// // @Router /api/v1/cmdb/pipeline/{id} [delete]
// func (c *CMDBController) DeletePipeline(ctx *gin.Context) {
// 	id, err := c.ParseUintParam(ctx, "id")
// 	if err != nil {
// 		c.response.BadRequest(ctx, "无效的流水线ID")
// 		return
// 	}

// 	// 先获取流水线信息以便重置关联
// 	pipeline, err := c.service.GetPipelineByID(id)
// 	if err != nil {
// 		if err == gorm.ErrRecordNotFound {
// 			c.response.NotFound(ctx, "流水线配置不存在")
// 			return
// 		}
// 		c.log.Errorf("获取流水线配置失败: %v", err)
// 		c.response.InternalError(ctx, "获取流水线配置失败")
// 		return
// 	}

// 	// 删除流水线配置
// 	if err := c.service.DeletePipeline(id); err != nil {
// 		c.log.Errorf("删除流水线配置失败: %v", err)
// 		c.response.InternalError(ctx, "删除流水线配置失败")
// 		return
// 	}

// 	// 重置关联的pipeline_id
// 	if err := c.updatePipelineID(0, pipeline.PipelineType, pipeline.SourceID); err != nil {
// 		c.log.Errorf("重置流水线关联失败: %v", err)
// 	}

// 	c.response.NoContent(ctx)
// }

// ============== Product相关接口 ==============

// @Summary 获取产品项目列表
// @Description 获取产品下的所有项目列表
// @Tags Product
// @Accept json
// @Produce json
// @Param product_id path int true "产品ID"
// @Success 200 {object} models.DTOListResponse
// @Router /api/v1/cmdb/product/{product_id}/projects [get]
func (c *CMDBController) GetProductProjects(ctx *gin.Context) {
	// 获取产品ID
	productID, err := c.ParseUintParam(ctx, "id")
	if err != nil {
		c.response.BadRequest(ctx, "无效的产品ID")
		return
	}

	projects, total, err := c.service.GetProjects(1, 10000, "", productID)
	if err != nil {
		c.log.Errorf("获取产品项目列表失败: %v", err)
		c.response.InternalError(ctx, "获取产品项目列表失败")
		return
	}

	c.response.List(ctx, projects, total, 1, 10000)
}

// @Summary 获取产品列表
// @Description 获取产品列表，支持分页和搜索
// @Tags Product
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param search query string false "搜索关键词"
// @Success 200 {object} models.DTOListResponse
// @Router /api/v1/cmdb/product [get]
func (c *CMDBController) GetProducts(ctx *gin.Context) {
	page, pageSize := c.GetPaginationParams(ctx)
	search := strings.TrimSpace(ctx.Query("search"))

	products, total, err := c.service.GetProducts(page, pageSize, search)
	if err != nil {
		c.log.Errorf("获取产品列表失败: %v", err)
		c.response.InternalError(ctx, "获取产品列表失败")
		return
	}

	c.response.List(ctx, products, total, page, pageSize)
}

// @Summary 获取产品详情
// @Description 根据ID获取产品详情
// @Tags Product
// @Accept json
// @Produce json
// @Param id path int true "产品ID"
// @Success 200 {object} models.DTOResponse
// @Failure 404 {object} models.DTOResponse
// @Router /api/v1/cmdb/product/{id} [get]
func (c *CMDBController) GetProduct(ctx *gin.Context) {
	id, err := c.ParseUintParam(ctx, "id")
	if err != nil {
		c.response.BadRequest(ctx, "无效的产品ID")
		return
	}

	product, err := c.service.GetProductByID(id)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.response.NotFound(ctx, "产品不存在")
			return
		}
		c.log.Errorf("获取产品详情失败: %v", err)
		c.response.InternalError(ctx, "获取产品详情失败")
		return
	}

	c.response.Success(ctx, product)
}

// @Summary 创建产品
// @Description 创建新产品
// @Tags Product
// @Accept json
// @Produce json
// @Param product body models.ProductCreateRequest true "产品信息"
// @Success 201 {object} models.DTOResponse
// @Router /api/v1/cmdb/product [post]
func (c *CMDBController) CreateProduct(ctx *gin.Context) {
	var req models.ProductCreateRequest
	if !c.ValidateRequest(ctx, &req) {
		return
	}

	// TODO: 从认证中间件获取用户ID
	creatorID := uint(1) // 临时硬编码，应该从JWT token中获取

	product, err := c.service.CreateProduct(&req, creatorID)
	if err != nil {
		c.log.Errorf("创建产品失败: %v", err)
		c.response.InternalError(ctx, "创建产品失败")
		return
	}

	c.response.Created(ctx, product)
}

// @Summary 更新产品
// @Description 更新指定ID的产品信息
// @Tags Product
// @Accept json
// @Produce json
// @Param id path int true "产品ID"
// @Param product body models.ProductCreateRequest true "产品信息"
// @Success 200 {object} models.DTOResponse
// @Failure 404 {object} models.DTOResponse
// @Router /api/v1/cmdb/product/{id} [put]
func (c *CMDBController) UpdateProduct(ctx *gin.Context) {
	id, err := c.ParseUintParam(ctx, "id")
	if err != nil {
		c.response.BadRequest(ctx, "无效的产品ID")
		return
	}

	var req models.ProductCreateRequest
	if !c.ValidateRequest(ctx, &req) {
		return
	}

	updates := map[string]interface{}{
		"name":         req.Name,
		"product_code": req.ProductCode,
		"region_id":    req.RegionID,
		"description":  req.Description,
		"name_prefix":  req.NamePrefix,
		"managers":     req.Managers,
		"updated_at":   time.Now(),
	}

	product, err := c.service.UpdateProduct(id, updates)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.response.NotFound(ctx, "产品不存在")
			return
		}
		c.log.Errorf("更新产品失败: %v", err)
		c.response.InternalError(ctx, "更新产品失败")
		return
	}

	c.response.Success(ctx, product)
}

// @Summary 删除产品
// @Description 删除指定ID的产品
// @Tags Product
// @Accept json
// @Produce json
// @Param id path int true "产品ID"
// @Success 200 {object} models.DTOResponse
// @Failure 404 {object} models.DTOResponse
// @Router /api/v1/cmdb/product/{id} [delete]
func (c *CMDBController) DeleteProduct(ctx *gin.Context) {
	id, err := c.ParseUintParam(ctx, "id")
	if err != nil {
		c.response.BadRequest(ctx, "无效的产品ID")
		return
	}

	if err := c.service.DeleteProduct(id); err != nil {
		if err == gorm.ErrRecordNotFound {
			c.response.NotFound(ctx, "产品不存在")
			return
		}
		c.log.Errorf("删除产品失败: %v", err)
		c.response.InternalError(ctx, "删除产品失败")
		return
	}

	c.response.NoContent(ctx)
}

// ============== Environment相关接口 ==============

// @Summary 获取环境列表
// @Description 获取环境列表，支持分页和搜索
// @Tags Environment
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param search query string false "搜索关键词"
// @Success 200 {object} models.DTOListResponse
// @Router /api/v1/cmdb/environment [get]
func (c *CMDBController) GetEnvironments(ctx *gin.Context) {
	page, pageSize := c.GetPaginationParams(ctx)
	search := strings.TrimSpace(ctx.Query("search"))

	environments, total, err := c.service.GetEnvironments(page, pageSize, search)
	if err != nil {
		c.log.Errorf("获取环境列表失败: %v", err)
		c.response.InternalError(ctx, "获取环境列表失败")
		return
	}

	c.response.List(ctx, environments, total, page, pageSize)
}

// @Summary 获取环境详情
// @Description 根据ID获取环境详情
// @Tags Environment
// @Accept json
// @Produce json
// @Param id path int true "环境ID"
// @Success 200 {object} models.DTOResponse
// @Failure 404 {object} models.DTOResponse
// @Router /api/v1/cmdb/environment/{id} [get]
func (c *CMDBController) GetEnvironment(ctx *gin.Context) {
	id, err := c.ParseUintParam(ctx, "id")
	if err != nil {
		c.response.BadRequest(ctx, "无效的环境ID")
		return
	}

	environment, err := c.service.GetEnvironmentByID(id)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.response.NotFound(ctx, "环境不存在")
			return
		}
		c.log.Errorf("获取环境详情失败: %v", err)
		c.response.InternalError(ctx, "获取环境详情失败")
		return
	}

	c.response.Success(ctx, environment)
}

// @Summary 创建环境
// @Description 创建新环境
// @Tags Environment
// @Accept json
// @Produce json
// @Param environment body models.EnvironmentCreateRequest true "环境信息"
// @Success 201 {object} models.DTOResponse
// @Router /api/v1/cmdb/environment [post]
func (c *CMDBController) CreateEnvironment(ctx *gin.Context) {
	var req models.EnvironmentCreateRequest
	if !c.ValidateRequest(ctx, &req) {
		return
	}

	environment, err := c.service.CreateEnvironment(&req)
	if err != nil {
		c.log.Errorf("创建环境失败: %v", err)
		c.response.InternalError(ctx, "创建环境失败")
		return
	}

	c.response.Created(ctx, environment)
}

// @Summary 更新环境
// @Description 更新指定ID的环境信息
// @Tags Environment
// @Accept json
// @Produce json
// @Param id path int true "环境ID"
// @Param environment body models.EnvironmentCreateRequest true "环境信息"
// @Success 200 {object} models.DTOResponse
// @Failure 404 {object} models.DTOResponse
// @Router /api/v1/cmdb/environment/{id} [put]
func (c *CMDBController) UpdateEnvironment(ctx *gin.Context) {
	id, err := c.ParseUintParam(ctx, "id")
	if err != nil {
		c.response.BadRequest(ctx, "无效的环境ID")
		return
	}

	var req models.EnvironmentCreateRequest
	if !c.ValidateRequest(ctx, &req) {
		return
	}

	updates := map[string]interface{}{
		"name":              req.Name,
		"environment_code":  req.EnvironmentCode,
		"ticket_enabled":    req.TicketEnabled,
		"merge_enabled":     req.MergeEnabled,
		"template_settings": req.TemplateSettings,
		"branch_settings":   req.BranchSettings,
		"extra_data":        req.ExtraData,
		"description":       req.Description,
		"sort_order":        req.SortOrder,
		"updated_at":        time.Now(),
	}

	environment, err := c.service.UpdateEnvironment(id, updates)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.response.NotFound(ctx, "环境不存在")
			return
		}
		c.log.Errorf("更新环境失败: %v", err)
		c.response.InternalError(ctx, "更新环境失败")
		return
	}

	c.response.Success(ctx, environment)
}

// @Summary 删除环境
// @Description 删除指定ID的环境
// @Tags Environment
// @Accept json
// @Produce json
// @Param id path int true "环境ID"
// @Success 200 {object} models.DTOResponse
// @Failure 404 {object} models.DTOResponse
// @Router /api/v1/cmdb/environment/{id} [delete]
func (c *CMDBController) DeleteEnvironment(ctx *gin.Context) {
	id, err := c.ParseUintParam(ctx, "id")
	if err != nil {
		c.response.BadRequest(ctx, "无效的环境ID")
		return
	}

	if err := c.service.DeleteEnvironment(id); err != nil {
		if err == gorm.ErrRecordNotFound {
			c.response.NotFound(ctx, "环境不存在")
			return
		}
		c.log.Errorf("删除环境失败: %v", err)
		c.response.InternalError(ctx, "删除环境失败")
		return
	}

	c.response.NoContent(ctx)
}

// ============== Project相关接口 ==============

// @Summary 获取项目列表
// @Description 获取项目列表，支持分页和搜索
// @Tags Project
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param search query string false "搜索关键词"
// @Param product_id query int false "产品ID"
// @Success 200 {object} models.DTOListResponse
// @Router /api/v1/cmdb/project [get]
func (c *CMDBController) GetProjects(ctx *gin.Context) {
	page, pageSize := c.GetPaginationParams(ctx)
	search := strings.TrimSpace(ctx.Query("search"))
	productID := uint(c.ParseIntQuery(ctx, "product_id", 0))

	projects, total, err := c.service.GetProjects(page, pageSize, search, productID)
	if err != nil {
		c.log.Errorf("获取项目列表失败: %v", err)
		c.response.InternalError(ctx, "获取项目列表失败")
		return
	}

	c.response.List(ctx, projects, total, page, pageSize)
}

// @Summary 获取项目详情
// @Description 根据ID获取项目详情
// @Tags Project
// @Accept json
// @Produce json
// @Param id path int true "项目ID"
// @Success 200 {object} models.DTOResponse
// @Failure 404 {object} models.DTOResponse
// @Router /api/v1/cmdb/project/{id} [get]
func (c *CMDBController) GetProject(ctx *gin.Context) {
	id, err := c.ParseUintParam(ctx, "id")
	if err != nil {
		c.response.BadRequest(ctx, "无效的项目ID")
		return
	}

	project, err := c.service.GetProjectByID(id)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.response.NotFound(ctx, "项目不存在")
			return
		}
		c.log.Errorf("获取项目详情失败: %v", err)
		c.response.InternalError(ctx, "获取项目详情失败")
		return
	}

	c.response.Success(ctx, project)
}

// @Summary 创建项目
// @Description 创建新项目
// @Tags Project
// @Accept json
// @Produce json
// @Param project body models.ProjectCreateRequest true "项目信息"
// @Success 201 {object} models.DTOResponse
// @Router /api/v1/cmdb/project [post]
func (c *CMDBController) CreateProject(ctx *gin.Context) {
	var req models.ProjectCreateRequest
	if !c.ValidateRequest(ctx, &req) {
		return
	}

	// TODO: 从用户上下文获取创建者ID
	creatorID := uint(1)

	project, err := c.service.CreateProject(&req, creatorID)
	if err != nil {
		c.log.Errorf("创建项目失败: %v", err)
		c.response.InternalError(ctx, "创建项目失败")
		return
	}

	c.response.Created(ctx, project)
}

// @Summary 更新项目
// @Description 更新项目信息
// @Tags Project
// @Accept json
// @Produce json
// @Param id path int true "项目ID"
// @Param project body models.ProjectCreateRequest true "项目信息"
// @Success 200 {object} models.DTOResponse
// @Failure 404 {object} models.DTOResponse
// @Router /api/v1/cmdb/project/{id} [put]
func (c *CMDBController) UpdateProject(ctx *gin.Context) {
	id, err := c.ParseUintParam(ctx, "id")
	if err != nil {
		c.response.BadRequest(ctx, "无效的项目ID")
		return
	}

	var req models.ProjectCreateRequest
	if !c.ValidateRequest(ctx, &req) {
		return
	}

	updates := map[string]interface{}{
		"name":            req.Name,
		"project_code":    req.ProjectCode,
		"managers":        req.Managers,
		"description":     req.Description,
		"notify_settings": req.NotifySettings,
	}

	if req.ProductID != nil {
		updates["product_id"] = *req.ProductID
	}

	project, err := c.service.UpdateProject(id, updates)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.response.NotFound(ctx, "项目不存在")
			return
		}
		c.log.Errorf("更新项目失败: %v", err)
		c.response.InternalError(ctx, "更新项目失败")
		return
	}

	c.response.Success(ctx, project)
}

// @Summary 删除项目
// @Description 删除项目
// @Tags Project
// @Accept json
// @Produce json
// @Param id path int true "项目ID"
// @Success 200 {object} models.DTOResponse
// @Failure 404 {object} models.DTOResponse
// @Router /api/v1/cmdb/project/{id} [delete]
func (c *CMDBController) DeleteProject(ctx *gin.Context) {
	id, err := c.ParseUintParam(ctx, "id")
	if err != nil {
		c.response.BadRequest(ctx, "无效的项目ID")
		return
	}

	if err := c.service.DeleteProject(id); err != nil {
		if err == gorm.ErrRecordNotFound {
			c.response.NotFound(ctx, "项目不存在")
			return
		}
		c.log.Errorf("删除项目失败: %v", err)
		c.response.InternalError(ctx, "删除项目失败")
		return
	}

	c.response.NoContent(ctx)
}

// ============== MicroApp相关接口 ==============

// @Summary 收藏应用/取消收藏
// @Description 收藏应用/取消收藏应用
// @Tags MicroApp
// @Accept json
// @Produce json
// @Param id path int true "应用ID"
// @Success 200 {object} models.DTOResponse
// @Failure 404 {object} models.DTOResponse
// @Router /api/v1/cmdb/app/microapp/{id}/favorite [post]
func (c *CMDBController) FavoriteMicroApp(ctx *gin.Context) {
	id, err := c.ParseUintParam(ctx, "id")
	if err != nil {
		c.response.BadRequest(ctx, "无效的应用ID")
		return
	}

	// 获取应用信息
	app, err := c.service.GetMicroAppByID(id)
	if err != nil {
		c.response.NotFound(ctx, "应用不存在")
		return
	}

	// 切换收藏状态
	app.IsFavorite = !app.IsFavorite
	app, err = c.service.UpdateMicroApp(id, map[string]interface{}{"is_favorite": app.IsFavorite})
	if err != nil {
		c.log.Errorf("更新应用收藏状态失败: %v", err)
		c.response.InternalError(ctx, "更新收藏状态失败")
		return
	}

	c.response.Success(ctx, app)
}

// @Summary 获取应用列表
// @Description 获取应用列表，支持分页和搜索
// @Tags MicroApp
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param search query string false "搜索关键词"
// @Param product_id query int false "产品ID"
// @Param project_id query int false "项目ID"
// @Param tags query string false "标签(多个用逗号分隔)"
// @Success 200 {object} models.DTOListResponse
// @Router /api/v1/cmdb/app [get]
func (c *CMDBController) GetMicroApps(ctx *gin.Context) {
	page, pageSize := c.GetPaginationParams(ctx)
	isFavorite := ctx.Query("is_favorite") == "true"
	search := strings.TrimSpace(ctx.Query("search"))
	projectID := uint(c.ParseIntQuery(ctx, "project_id", 0))
	productID := uint(c.ParseIntQuery(ctx, "product_id", 0))
	// 解析tags参数
	// tagsParam := ctx.Query("tags")
	// var tags []string
	// if tagsParam != "" {
	// 	tags = strings.Split(tagsParam, ",")
	// }
	apps, total, err := c.service.GetMicroApps(page, pageSize, isFavorite, search, projectID, productID)
	if err != nil {
		c.log.Errorf("获取应用列表失败: %v", err)
		c.response.InternalError(ctx, "获取应用列表失败")
		return
	}
	c.response.List(ctx, apps, total, page, pageSize)
}

// @Summary 获取应用详情
// @Description 根据ID获取应用详情
// @Tags MicroApp
// @Accept json
// @Produce json
// @Param id path int true "应用ID"
// @Success 200 {object} models.DTOResponse
// @Failure 404 {object} models.DTOResponse
// @Router /api/v1/cmdb/app/{id} [get]
func (c *CMDBController) GetMicroApp(ctx *gin.Context) {
	id, err := c.ParseUintParam(ctx, "id")
	if err != nil {
		c.response.BadRequest(ctx, "无效的应用ID")
		return
	}

	app, err := c.service.GetMicroAppByID(id)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.response.NotFound(ctx, "应用不存在")
			return
		}
		c.log.Errorf("获取应用详情失败: %v", err)
		c.response.InternalError(ctx, "获取应用详情失败")
		return
	}

	c.response.Success(ctx, app)
}

// @Summary 创建应用
// @Description 创建新应用
// @Tags MicroApp
// @Accept json
// @Produce json
// @Param app body models.MicroAppCreateRequest true "应用信息"
// @Success 201 {object} models.DTOResponse
// @Router /api/v1/cmdb/app [post]
func (c *CMDBController) CreateMicroApp(ctx *gin.Context) {
	var req models.MicroAppCreateRequest
	if !c.ValidateRequest(ctx, &req) {
		return
	}
	creatorID := uint(1)
	app, err := c.service.CreateMicroApp(&req, creatorID)
	if err != nil {
		c.log.Errorf("创建应用失败: %v", err)
		c.response.InternalError(ctx, "创建应用失败")
		return
	}
	c.response.Created(ctx, app)
}

// @Summary 更新微应用
// @Description 更新微应用信息
// @Tags MicroApp
// @Accept json
// @Produce json
// @Param id path int true "应用ID"
// @Param app body models.MicroAppCreateRequest true "应用信息"
// @Success 200 {object} models.DTOResponse
// @Failure 404 {object} models.DTOResponse
// @Router /api/v1/cmdb/app/microapp/{id} [put]
func (c *CMDBController) UpdateMicroApp(ctx *gin.Context) {
	id, err := c.ParseUintParam(ctx, "id")
	if err != nil {
		c.response.BadRequest(ctx, "无效的应用ID")
		return
	}
	var req models.MicroAppCreateRequest
	if !c.ValidateRequest(ctx, &req) {
		return
	}
	updates := map[string]interface{}{
		"name":            req.Name,
		"app_code":        req.AppCode,
		"language_code":   req.LanguageCode,
		"repo_settings":   req.RepoSettings,
		"target_settings": req.TargetSettings,
		"team_members":    req.TeamMembers,
		"category_name":   req.CategoryName,
		"template_data":   req.TemplateData,
		"build_command":   req.BuildCommand,
		"is_multi_app":    req.IsMultiApp,
		"multi_app_ids":   req.MultiAppIDs,
		"docker_settings": req.DockerSettings,
		"enabled":         req.Enabled,
		"description":     req.Description,
		"notify_settings": req.NotifySettings,
		"edit_permission": req.EditPermission,
		"deployment_type": req.DeploymentType,
		"module_settings": req.ModuleSettings,
		"scan_branch":     req.ScanBranch,
		"pipeline_id":     req.PipelineID,
	}
	if req.ProjectID != nil {
		updates["project_id"] = *req.ProjectID
	}
	if req.ProductID != nil {
		updates["product_id"] = *req.ProductID
	}
	app, err := c.service.UpdateMicroApp(id, updates)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.response.NotFound(ctx, "应用不存在")
			return
		}
		c.log.Errorf("更新应用失败: %v", err)
		c.response.InternalError(ctx, "更新应用失败")
		return
	}
	c.response.Success(ctx, app)
}

// @Summary 删除微应用
// @Description 删除微应用
// @Tags MicroApp
// @Accept json
// @Produce json
// @Param id path int true "应用ID"
// @Success 200 {object} models.DTOResponse
// @Failure 404 {object} models.DTOResponse
// @Router /api/v1/cmdb/app/microapp/{id} [delete]
func (c *CMDBController) DeleteMicroApp(ctx *gin.Context) {
	id, err := c.ParseUintParam(ctx, "id")
	if err != nil {
		c.response.BadRequest(ctx, "无效的应用ID")
		return
	}

	if err := c.service.DeleteMicroApp(id); err != nil {
		if err == gorm.ErrRecordNotFound {
			c.response.NotFound(ctx, "应用不存在")
			return
		}
		c.log.Errorf("删除应用失败: %v", err)
		c.response.InternalError(ctx, "删除应用失败")
		return
	}

	c.response.NoContent(ctx)
}

// ============== AppInfo相关接口 ==============

// @Summary 获取应用服务列表
// @Description 获取应用服务列表，支持分页和搜索
// @Tags AppInfo
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param search query string false "搜索关键词"
// @Param app_id query int false "应用ID"
// @Param env_id query int false "环境ID"
// @Success 200 {object} models.DTOListResponse
// @Router /api/v1/cmdb/app/service [get]
func (c *CMDBController) GetAppInfos(ctx *gin.Context) {
	page, pageSize := c.GetPaginationParams(ctx)
	search := strings.TrimSpace(ctx.Query("search"))
	appID := uint(c.ParseIntQuery(ctx, "app_id", 0))
	envID := uint(c.ParseIntQuery(ctx, "env_id", 0))

	appInfos, total, err := c.service.GetAppInfos(page, pageSize, search, appID, envID)
	if err != nil {
		c.log.Errorf("获取应用服务列表失败: %v", err)
		c.response.InternalError(ctx, "获取应用服务列表失败")
		return
	}

	c.response.List(ctx, appInfos, total, page, pageSize)
}

// @Summary 获取应用服务详情
// @Description 根据ID获取应用服务详情
// @Tags AppInfo
// @Accept json
// @Produce json
// @Param id path int true "应用服务ID"
// @Success 200 {object} models.DTOResponse
// @Failure 404 {object} models.DTOResponse
// @Router /api/v1/cmdb/app/service/{id} [get]
func (c *CMDBController) GetAppInfo(ctx *gin.Context) {
	id, err := c.ParseUintParam(ctx, "id")
	if err != nil {
		c.response.BadRequest(ctx, "无效的应用服务ID")
		return
	}

	appInfo, err := c.service.GetAppInfoByID(id)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.response.NotFound(ctx, "应用服务不存在")
			return
		}
		c.log.Errorf("获取应用服务详情失败: %v", err)
		c.response.InternalError(ctx, "获取应用服务详情失败")
		return
	}

	c.response.Success(ctx, appInfo)
}

// @Summary 创建应用服务
// @Description 创建新应用服务
// @Tags AppInfo
// @Accept json
// @Produce json
// @Param appInfo body models.AppInfoCreateRequest true "应用服务信息"
// @Success 201 {object} models.DTOResponse
// @Router /api/v1/cmdb/app/service [post]
func (c *CMDBController) CreateAppInfo(ctx *gin.Context) {
	var req models.AppInfoCreateRequest
	if !c.ValidateRequest(ctx, &req) {
		return
	}
	appInfo, err := c.service.CreateAppInfo(&req)
	if err != nil {
		c.log.Errorf("创建应用服务失败: %v", err)
		c.response.InternalError(ctx, "创建应用服务失败")
		return
	}
	c.response.Created(ctx, appInfo)
}

// @Summary 更新应用服务
// @Description 更新应用服务信息
// @Tags AppInfo
// @Accept json
// @Produce json
// @Param id path int true "应用服务ID"
// @Param appInfo body models.AppInfoCreateRequest true "应用服务信息"
// @Success 200 {object} models.DTOResponse
// @Failure 404 {object} models.DTOResponse
// @Router /api/v1/cmdb/app/service/{id} [put]
func (c *CMDBController) UpdateAppInfo(ctx *gin.Context) {
	id, err := c.ParseUintParam(ctx, "id")
	if err != nil {
		c.response.BadRequest(ctx, "无效的应用服务ID")
		return
	}
	var req models.AppInfoCreateRequest
	if !c.ValidateRequest(ctx, &req) {
		return
	}
	updates := map[string]interface{}{
		"unique_tag":             req.UniqueTag,
		"app_id":                 req.AppID,
		"environment_id":         req.EnvironmentID,
		"branch_settings":        req.BranchSettings,
		"build_command":          req.BuildCommand,
		"version_number":         req.VersionNumber,
		"template_data":          req.TemplateData,
		"pipeline_id":            req.PipelineID,
		"is_enabled":             req.IsEnabled,
		"description":            req.Description,
		"edit_permission":        req.EditPermission,
		"online_status":          req.OnlineStatus,
		"port_settings":          req.PortSettings,
		"kubernetes_cluster_ids": req.KubernetesClusterIDs,
	}
	appInfo, err := c.service.UpdateAppInfo(id, updates)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.response.NotFound(ctx, "应用服务不存在")
			return
		}
		c.log.Errorf("更新应用服务失败: %v", err)
		c.response.InternalError(ctx, "更新应用服务失败")
		return
	}
	c.response.Success(ctx, appInfo)
}

// @Summary 删除应用服务
// @Description 删除应用服务
// @Tags AppInfo
// @Accept json
// @Produce json
// @Param id path int true "应用服务ID"
// @Success 200 {object} models.DTOResponse
// @Failure 404 {object} models.DTOResponse
// @Router /api/v1/cmdb/app/service/{id} [delete]
func (c *CMDBController) DeleteAppInfo(ctx *gin.Context) {
	id, err := c.ParseUintParam(ctx, "id")
	if err != nil {
		c.response.BadRequest(ctx, "无效的应用服务ID")
		return
	}

	if err := c.service.DeleteAppInfo(id); err != nil {
		if err == gorm.ErrRecordNotFound {
			c.response.NotFound(ctx, "应用服务不存在")
			return
		}
		c.log.Errorf("删除应用服务失败: %v", err)
		c.response.InternalError(ctx, "删除应用服务失败")
		return
	}

	c.response.NoContent(ctx)
}

// ============== 其他资源查询接口 ==============

// @Summary 获取K8s集群列表
// @Description 获取K8s集群列表，支持分页和搜索
// @Tags Kubernetes
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param search query string false "搜索关键词"
// @Success 200 {object} models.DTOListResponse
// @Router /api/v1/cmdb/kubernetes [get]
func (c *CMDBController) GetKubernetesClusters(ctx *gin.Context) {
	page, pageSize := c.GetPaginationParams(ctx)
	search := strings.TrimSpace(ctx.Query("search"))

	clusters, total, err := c.service.GetKubernetesClusters(page, pageSize, search)
	if err != nil {
		c.log.Errorf("获取K8s集群列表失败: %v", err)
		c.response.InternalError(ctx, "获取K8s集群列表失败")
		return
	}

	c.response.List(ctx, clusters, total, page, pageSize)
}

// @Summary 获取开发语言列表
// @Description 获取开发语言列表，支持分页和搜索
// @Tags DevLanguage
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param search query string false "搜索关键词"
// @Success 200 {object} models.DTOListResponse
// @Router /api/v1/cmdb/app/language [get]
func (c *CMDBController) GetDevLanguages(ctx *gin.Context) {
	page, pageSize := c.GetPaginationParams(ctx)
	search := strings.TrimSpace(ctx.Query("search"))

	languages, total, err := c.service.GetDevLanguages(page, pageSize, search)
	if err != nil {
		c.log.Errorf("获取开发语言列表失败: %v", err)
		c.response.InternalError(ctx, "获取开发语言列表失败")
		return
	}

	c.response.List(ctx, languages, total, page, pageSize)
}

// @Summary 获取区域列表
// @Description 获取区域列表，支持分页和搜索
// @Tags Region
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param search query string false "搜索关键词"
// @Success 200 {object} models.DTOListResponse
// @Router /api/v1/cmdb/region [get]
func (c *CMDBController) GetRegions(ctx *gin.Context) {
	page, pageSize := c.GetPaginationParams(ctx)
	search := strings.TrimSpace(ctx.Query("search"))

	regions, total, err := c.service.GetRegions(page, pageSize, search)
	if err != nil {
		c.log.Errorf("获取区域列表失败: %v", err)
		c.response.InternalError(ctx, "获取区域列表失败")
		return
	}

	c.response.List(ctx, regions, total, page, pageSize)
}

// @Summary 获取DataCenter列表
// @Description 获取DataCenter列表，支持分页和搜索
// @Tags DataCenter
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param search query string false "搜索关键词"
// @Success 200 {object} models.DTOListResponse
// @Router /api/v1/cmdb/asset/datacenter [get]
func (c *CMDBController) GetDataCenters(ctx *gin.Context) {
	page, pageSize := c.GetPaginationParams(ctx)
	search := strings.TrimSpace(ctx.Query("search"))

	dataCenters, total, err := c.service.GetDataCenters(page, pageSize, search)
	if err != nil {
		c.log.Errorf("获取DataCenter列表失败: %v", err)
		c.response.InternalError(ctx, "获取DataCenter列表失败")
		return
	}

	c.response.List(ctx, dataCenters, total, page, pageSize)
}

// @Summary 获取DataCenter详情
// @Description 根据ID获取DataCenter详情
// @Tags DataCenter
// @Accept json
// @Produce json
// @Param id path int true "DataCenter ID"
// @Success 200 {object} models.DTOResponse
// @Failure 404 {object} models.DTOResponse
// @Router /api/v1/cmdb/asset/datacenter/{id} [get]
func (c *CMDBController) GetDataCenter(ctx *gin.Context) {
	id, err := c.ParseUintParam(ctx, "id")
	if err != nil {
		c.response.BadRequest(ctx, "无效的DataCenter ID")
		return
	}

	dataCenter, err := c.service.GetDataCenterByID(id)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.response.NotFound(ctx, "DataCenter不存在")
			return
		}
		c.log.Errorf("获取DataCenter详情失败: %v", err)
		c.response.InternalError(ctx, "获取DataCenter详情失败")
		return
	}

	c.response.Success(ctx, dataCenter)
}

// @Summary 创建DataCenter
// @Description 创建新DataCenter
// @Tags DataCenter
// @Accept json
// @Produce json
// @Param datacenter body models.DataCenter true "DataCenter信息"
// @Success 201 {object} models.DTOResponse
// @Router /api/v1/cmdb/asset/datacenter [post]
func (c *CMDBController) CreateDataCenter(ctx *gin.Context) {
	var req models.DataCenter
	if !c.ValidateRequest(ctx, &req) {
		return
	}

	if err := c.db.Create(&req).Error; err != nil {
		c.log.Errorf("创建DataCenter失败: %v", err)
		c.response.InternalError(ctx, "创建DataCenter失败")
		return
	}

	c.response.Created(ctx, req)
}

// @Summary 更新DataCenter
// @Description 更新DataCenter信息
// @Tags DataCenter
// @Accept json
// @Produce json
// @Param id path int true "DataCenter ID"
// @Param datacenter body models.DataCenter true "DataCenter信息"
// @Success 200 {object} models.DTOResponse
// @Failure 404 {object} models.DTOResponse
// @Router /api/v1/cmdb/asset/datacenter/{id} [put]
func (c *CMDBController) UpdateDataCenter(ctx *gin.Context) {
	id, err := c.ParseUintParam(ctx, "id")
	if err != nil {
		c.response.BadRequest(ctx, "无效的DataCenter ID")
		return
	}

	var req models.DataCenter
	if !c.ValidateRequest(ctx, &req) {
		return
	}

	updates := map[string]interface{}{
		"name":             req.Name,
		"data_center_code": req.DataCenterCode,
		"region_id":        req.RegionID,
		"description":      req.Description,
		"address":          req.Address,
	}

	if err := c.db.Model(&models.DataCenter{}).Where("id = ?", id).Updates(updates).Error; err != nil {
		c.log.Errorf("更新DataCenter失败: %v", err)
		c.response.InternalError(ctx, "更新DataCenter失败")
		return
	}

	var dc models.DataCenter
	if err := c.db.Preload("Region").First(&dc, id).Error; err != nil {
		c.log.Errorf("获取DataCenter详情失败: %v", err)
		c.response.InternalError(ctx, "获取DataCenter详情失败")
		return
	}

	c.response.Success(ctx, dc)
}

// @Summary 删除DataCenter
// @Description 删除DataCenter
// @Tags DataCenter
// @Accept json
// @Produce json
// @Param id path int true "DataCenter ID"
// @Success 200 {object} models.DTOResponse
// @Failure 404 {object} models.DTOResponse
// @Router /api/v1/cmdb/asset/datacenter/{id} [delete]
func (c *CMDBController) DeleteDataCenter(ctx *gin.Context) {
	id, err := c.ParseUintParam(ctx, "id")
	if err != nil {
		c.response.BadRequest(ctx, "无效的DataCenter ID")
		return
	}

	if err := c.db.Delete(&models.DataCenter{}, id).Error; err != nil {
		c.log.Errorf("删除DataCenter失败: %v", err)
		c.response.InternalError(ctx, "删除DataCenter失败")
		return
	}

	c.response.NoContent(ctx)
}

// @Summary 部署微应用
// @Description 部署微应用到指定环境
// @Tags MicroApp
// @Accept json
// @Produce json
// @Param id path int true "应用ID"
// @Param deploy body object true "部署信息"
// @Success 200 {object} models.DTOResponse
// @Router /api/v1/cmdb/app/microapp/{id}/deploy [post]
func (c *CMDBController) DeployMicroApp(ctx *gin.Context) {
	id, err := c.ParseUintParam(ctx, "id")
	if err != nil {
		c.response.BadRequest(ctx, "无效的应用ID")
		return
	}

	var req struct {
		Environment string `json:"environment" validate:"required"`
		Branch      string `json:"branch" validate:"required"`
		Version     string `json:"version"`
	}
	if !c.ValidateRequest(ctx, &req) {
		return
	}

	app, err := c.service.GetMicroAppByID(id)
	if err != nil {
		c.log.Errorf("获取应用详情失败: %v", err)
		c.response.InternalError(ctx, "获取应用详情失败")
		return
	}

	// 这里应该调用部署服务，暂时返回模拟结果
	c.response.Success(ctx, map[string]interface{}{
		"task_id":     fmt.Sprintf("deploy-%d-%d", app.ID, time.Now().Unix()),
		"app_id":      app.ID,
		"app_name":    app.Name,
		"environment": req.Environment,
		"branch":      req.Branch,
		"version":     req.Version,
		"status":      "pending",
		"message":     "部署任务已创建",
	})
}

// @Summary 获取部署历史
// @Description 获取应用部署历史
// @Tags MicroApp
// @Accept json
// @Produce json
// @Param id path int true "应用ID"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Success 200 {object} models.DTOResponse
// @Router /api/v1/cmdb/app/microapp/{id}/deploy/history [get]
func (c *CMDBController) GetDeployHistory(ctx *gin.Context) {
	id, err := c.ParseUintParam(ctx, "id")
	if err != nil {
		c.response.BadRequest(ctx, "无效的应用ID")
		return
	}

	page, pageSize := c.GetPaginationParams(ctx)

	app, err := c.service.GetMicroAppByID(id)
	if err != nil {
		c.log.Errorf("获取应用详情失败: %v", err)
		c.response.InternalError(ctx, "获取应用详情失败")
		return
	}

	// 这里应该查询实际的部署历史，暂时返回模拟数据
	items := []map[string]interface{}{
		{
			"id":          1,
			"app_id":      app.ID,
			"app_name":    app.Name,
			"environment": "production",
			"branch":      "master",
			"version":     "v1.0.0",
			"status":      "success",
			"created_at":  time.Now().Add(-24 * time.Hour).Format("2006-01-02 15:04:05"),
			"duration":    "5m30s",
		},
	}

	c.response.Success(ctx, map[string]interface{}{
		"items":     items,
		"total":     int64(len(items)),
		"page":      page,
		"page_size": pageSize,
	})
}

// @Summary 获取微应用健康状态
// @Description 获取微应用在各环境的健康状态
// @Tags MicroApp
// @Accept json
// @Produce json
// @Param id path int true "应用ID"
// @Success 200 {object} models.DTOResponse
// @Router /api/v1/cmdb/app/microapp/{id}/health [get]
func (c *CMDBController) GetMicroAppHealth(ctx *gin.Context) {
	id, err := c.ParseUintParam(ctx, "id")
	if err != nil {
		c.response.BadRequest(ctx, "无效的应用ID")
		return
	}

	app, err := c.service.GetMicroAppByID(id)
	if err != nil {
		c.log.Errorf("获取应用详情失败: %v", err)
		c.response.InternalError(ctx, "获取应用详情失败")
		return
	}

	// 这里应该查询实际的健康状态，暂时返回模拟数据
	healthStatus := map[string]interface{}{
		"app_id":   app.ID,
		"app_name": app.Name,
		"environments": []map[string]interface{}{
			{
				"name":     "production",
				"status":   "healthy",
				"replicas": 3,
				"ready":    3,
			},
			{
				"name":     "staging",
				"status":   "warning",
				"replicas": 2,
				"ready":    1,
			},
		},
		"overall_status": "healthy",
	}

	c.response.Success(ctx, healthStatus)
}

// @Summary 获取微应用资源使用情况
// @Description 获取微应用的资源使用情况
// @Tags MicroApp
// @Accept json
// @Produce json
// @Param id path int true "应用ID"
// @Success 200 {object} models.DTOResponse
// @Router /api/v1/cmdb/app/microapp/{id}/resources [get]
func (c *CMDBController) GetMicroAppResources(ctx *gin.Context) {
	id, err := c.ParseUintParam(ctx, "id")
	if err != nil {
		c.response.BadRequest(ctx, "无效的应用ID")
		return
	}

	app, err := c.service.GetMicroAppByID(id)
	if err != nil {
		c.log.Errorf("获取应用详情失败: %v", err)
		c.response.InternalError(ctx, "获取应用详情失败")
		return
	}

	// 这里应该查询实际的资源使用情况，暂时返回模拟数据
	resourceUsage := map[string]interface{}{
		"app_id":   app.ID,
		"app_name": app.Name,
		"environments": []map[string]interface{}{
			{
				"name": "production",
				"cpu": map[string]interface{}{
					"used":  "500m",
					"limit": "1000m",
					"usage": "50%",
				},
				"memory": map[string]interface{}{
					"used":  "512Mi",
					"limit": "1Gi",
					"usage": "50%",
				},
			},
		},
	}

	c.response.Success(ctx, resourceUsage)
}
