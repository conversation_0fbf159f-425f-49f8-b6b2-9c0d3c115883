package controllers

import (
	"net/http"
	"strconv"

	"github.com/devops-microservices/cmdb-service/models"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type DevLanguageController struct {
	db *gorm.DB
}

func NewDevLanguageController(db *gorm.DB) *DevLanguageController {
	return &DevLanguageController{db: db}
}

// GetDevLanguages 获取开发语言列表
// @Tags DevLanguages
// @Summary 获取开发语言列表
// @Description 获取所有开发语言的列表信息
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页大小" default(10)
// @Param search query string false "搜索关键字"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Router /api/v1/cmdb/app/language [get]
func (c *DevLanguageController) GetDevLanguages(ctx *gin.Context) {
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "10"))
	search := ctx.Query("search")

	var languages []models.DevLanguage
	query := c.db

	// 搜索条件
	if search != "" {
		query = query.Where("name LIKE ? OR language_code LIKE ? OR description LIKE ?", "%"+search+"%", "%"+search+"%", "%"+search+"%")
	}

	// 计算总数
	var total int64
	query.Model(&models.DevLanguage{}).Count(&total)

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Find(&languages).Error; err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "查询开发语言失败",
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 20000,
		"data": gin.H{
			"total": total,
			"items": languages,
		},
	})
}

// GetDevLanguage 获取单个开发语言
// @Tags DevLanguages
// @Summary 获取单个开发语言
// @Description 根据ID获取开发语言详情
// @Accept json
// @Produce json
// @Param id path int true "开发语言ID"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Router /api/v1/cmdb/app/language/{id} [get]
func (c *DevLanguageController) GetDevLanguage(ctx *gin.Context) {
	id := ctx.Param("id")

	var language models.DevLanguage
	if err := c.db.First(&language, id).Error; err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{
			"code":    40400,
			"message": "开发语言不存在",
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 20000,
		"data": language,
	})
}

// GetDevLanguageByCode 根据language_code获取开发语言
// @Tags DevLanguages
// @Summary 根据language_code获取开发语言
// @Description 根据language_code获取开发语言详情
// @Accept json
// @Produce json
// @Param language_code path string true "开发语言code"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Router /api/v1/cmdb/app/language/code/{language_code} [get]
func (c *DevLanguageController) GetDevLanguageByCode(ctx *gin.Context) {
	language_code := ctx.Param("language_code")

	var language models.DevLanguage
	if err := c.db.Where("language_code = ?", language_code).First(&language).Error; err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{
			"code":    40400,
			"message": "开发语言不存在",
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 20000,
		"data": language,
	})
}

// CreateDevLanguage 创建开发语言
// @Tags DevLanguages
// @Summary 创建开发语言
// @Description 创建新的开发语言
// @Accept json
// @Produce json
// @Param language body models.DevLanguage true "开发语言信息"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Router /api/v1/cmdb/app/language [post]
func (c *DevLanguageController) CreateDevLanguage(ctx *gin.Context) {
	var language models.DevLanguage
	if err := ctx.ShouldBindJSON(&language); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    40000,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	if err := c.db.Create(&language).Error; err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "创建开发语言失败",
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 20000,
		"data": language,
	})
}

// UpdateDevLanguage 更新开发语言
// @Tags DevLanguages
// @Summary 更新开发语言
// @Description 更新开发语言信息
// @Accept json
// @Produce json
// @Param id path int true "开发语言ID"
// @Param language body models.DevLanguage true "开发语言信息"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Router /api/v1/cmdb/app/language/{id} [put]
func (c *DevLanguageController) UpdateDevLanguage(ctx *gin.Context) {
	id := ctx.Param("id")

	var language models.DevLanguage
	if err := c.db.First(&language, id).Error; err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{
			"code":    40400,
			"message": "开发语言不存在",
		})
		return
	}

	if err := ctx.ShouldBindJSON(&language); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    40000,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	if err := c.db.Save(&language).Error; err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "更新开发语言失败",
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 20000,
		"data": language,
	})
}

// DeleteDevLanguage 删除开发语言
// @Tags DevLanguages
// @Summary 删除开发语言
// @Description 删除开发语言
// @Accept json
// @Produce json
// @Param id path int true "开发语言ID"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Router /api/v1/cmdb/app/language/{id} [delete]
func (c *DevLanguageController) DeleteDevLanguage(ctx *gin.Context) {
	id := ctx.Param("id")

	if err := c.db.Delete(&models.DevLanguage{}, id).Error; err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "删除开发语言失败",
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    20000,
		"message": "删除成功",
	})
}

// GetDockerfile 获取开发语言的Dockerfile
// @Tags DevLanguages
// @Summary 获取开发语言的Dockerfile
// @Description 根据ID获取开发语言的Dockerfile模板
// @Accept json
// @Produce json
// @Param id path int true "开发语言ID"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Router /api/v1/cmdb/app/language/{id}/dockerfile [get]
func (c *DevLanguageController) GetDockerfile(ctx *gin.Context) {
	id := ctx.Param("id")

	var language models.DevLanguage
	if err := c.db.First(&language, id).Error; err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{
			"code":    40400,
			"message": "开发语言不存在",
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 20000,
		"data": gin.H{
			"content": language.DockerTemplate,
		},
	})
}

// GetPipeline 获取开发语言的Pipeline
// @Tags DevLanguages
// @Summary 获取开发语言的Pipeline
// @Description 根据ID获取开发语言的Pipeline模板
// @Accept json
// @Produce json
// @Param id path int true "开发语言ID"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Router /api/v1/cmdb/app/language/{id}/pipeline [get]
func (c *DevLanguageController) GetPipeline(ctx *gin.Context) {
	id := ctx.Param("id")

	var language models.DevLanguage
	if err := c.db.First(&language, id).Error; err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{
			"code":    40400,
			"message": "开发语言不存在",
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 20000,
		"data": gin.H{
			"pipeline_id": language.PipelineID,
			"base_image":  language.BaseImage,
			"build":       language.BuildSettings,
		},
	})
}

// SavePipeline 保存开发语言的Pipeline
// @Tags DevLanguages
// @Summary 保存开发语言的Pipeline
// @Description 根据ID保存开发语言的Pipeline模板
// @Accept json
// @Produce json
// @Param id path int true "开发语言ID"
// @Param pipeline body map[string]interface{} true "Pipeline内容"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Router /api/v1/cmdb/app/language/{id}/pipeline [put]
func (c *DevLanguageController) SavePipeline(ctx *gin.Context) {
	id := ctx.Param("id")

	var language models.DevLanguage
	if err := c.db.First(&language, id).Error; err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{
			"code":    40400,
			"message": "开发语言不存在",
		})
		return
	}

	var data map[string]interface{}
	if err := ctx.ShouldBindJSON(&data); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    40000,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	// 更新Pipeline字段
	if pipeline, ok := data["pipeline"]; ok {
		language.PipelineID = pipeline.(*uint)
	}

	if err := c.db.Save(&language).Error; err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "保存Pipeline失败",
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    20000,
		"message": "保存成功",
	})
}
