package controllers

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/devops-microservices/cmdb-service/models"
	"github.com/devops-microservices/cmdb-service/utils"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"github.com/devops-microservices/cmdb-service/config"
	gitlab "gitlab.com/gitlab-org/api/client-go"
	"gopkg.in/yaml.v3"
)

// ApplicationController 应用控制器
type ApplicationController struct {
	db     *gorm.DB
	config *config.Config
}

// NewApplicationController 创建新的应用控制器
func NewApplicationController(db *gorm.DB, cfg *config.Config) *ApplicationController {
	return &ApplicationController{
		db:     db,
		config: cfg,
	}
}

// GetApplications 获取应用列表 - 对应Django的MicroAppViewSet.list
// @Tags Applications
// @Summary 获取应用列表
// @Description 获取微服务应用列表，支持搜索、分页和过滤
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页大小" default(10)
// @Param search query string false "搜索关键字"
// @Param category query string false "应用分类"
// @Param project_id query int false "项目ID"
// @Param product_id query int false "产品ID"
// @Param force query int false "强制刷新"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Router /api/v1/cmdb/app [get]
func (c *ApplicationController) GetApplications(ctx *gin.Context) {
	search := ctx.DefaultQuery("search", "")
	category := ctx.DefaultQuery("category", "")
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "10"))
	query := c.db.Model(&models.MicroApp{})
	if search != "" {
		query = query.Where("name LIKE ? OR app_code LIKE ?", "%"+search+"%", "%"+search+"%")
	}
	if category != "" {
		query = query.Where("category_name = ?", category)
	}
	var total int64
	if err := query.Count(&total).Error; err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"code": 500, "message": "查询应用总数失败: " + err.Error()})
		return
	}
	var apps []models.MicroApp
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Order("id DESC").Find(&apps).Error; err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"code": 500, "message": "查询应用列表失败: " + err.Error()})
		return
	}
	for _, app := range apps {
		if app.ProjectID != nil {
			var project models.Project
			if err := c.db.First(&project, *app.ProjectID).Error; err == nil {
				app.Project = &project
			}
		}
	}
	ctx.JSON(http.StatusOK, gin.H{
		"code":    20000,
		"message": "获取应用列表成功",
		"data": gin.H{
			"total":     total,
			"items":     apps,
			"page":      page,
			"page_size": pageSize,
		},
	})
}

// GetApplication 获取单个应用详情 - 对应Django的MicroAppViewSet.retrieve
// @Tags Applications
// @Summary 获取应用详情
// @Description 根据ID获取单个应用的详细信息
// @Accept json
// @Produce json
// @Param id path int true "应用ID"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Router /api/v1/cmdb/app/{id} [get]
func (c *ApplicationController) GetApplication(ctx *gin.Context) {
	id := ctx.Param("id")

	// 将字符串ID转换为整数
	appID, err := strconv.Atoi(id)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的应用ID",
		})
		return
	}

	var app models.MicroApp
	// 不使用Preload，避免JSONField编码问题
	if err := c.db.First(&app, appID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			ctx.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": "应用不存在",
			})
			return
		}
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "查询应用失败: " + err.Error(),
		})
		return
	}

	// 构建基本响应数据
	appData := map[string]interface{}{
		"id":            app.ID,
		"app_code":      app.AppCode,
		"name":          app.Name,
		"category":      app.CategoryName,
		"language_code": app.LanguageCode,
		"build_command": app.BuildCommand,
		"multiple_app":  app.IsMultiApp,
		"enabled":       app.Enabled,
		"created_time":  app.CreatedAt,
		"update_time":   app.UpdatedAt,
		"description":   app.Description,
		"is_k8s":        app.DeploymentType == "k8s",
		"scan_branch":   app.ScanBranch,
		"pipeline_id":   app.PipelineID,
		"project_id":    app.ProjectID,
		"creator_id":    app.CreatorID,

		// 兼容字段
		"project": app.ProjectID,
		"creator": app.CreatorID,
		"app_id":  app.AppCode, // 使用AppCode作为app_id
	}

	// 手动查询项目信息
	if app.ProjectID != nil {
		var project models.Project
		if err := c.db.First(&project, *app.ProjectID).Error; err == nil {
			appData["project_info"] = map[string]interface{}{
				"id":           project.ID,
				"name":         project.Name,
				"project_code": project.ProjectCode,
			}

			// 手动查询产品信息
			if project.ProductID != nil {
				var product models.Product
				if err := c.db.First(&product, *project.ProductID).Error; err == nil {
					appData["product_info"] = map[string]interface{}{
						"id":           product.ID,
						"name":         product.Name,
						"product_code": product.ProductCode,
					}
				}
			}
		}
	}

	// 查询创建者信息
	var creatorInfo map[string]interface{}
	if app.CreatorID != nil {
		var creator models.UserProfile
		if err := c.db.First(&creator, *app.CreatorID).Error; err == nil {
			creatorInfo = map[string]interface{}{
				"id":         creator.ID,
				"username":   creator.Username,
				"first_name": creator.FirstName,
			}
		}
	}
	if creatorInfo == nil {
		creatorInfo = map[string]interface{}{
			"id":         app.CreatorID,
			"username":   "",
			"first_name": "",
		}
	}
	appData["creator_info"] = creatorInfo

	// 查询应用实例信息
	var appInfos []models.AppInfo
	c.db.Where("app_id = ?", app.ID).Find(&appInfos)

	// 添加环境信息
	envApp := make(map[uint]map[string]interface{})
	for _, appInfo := range appInfos {
		if appInfo.EnvironmentID != nil {
			envID := *appInfo.EnvironmentID
			if _, exists := envApp[envID]; !exists {
				var environment models.Environment
				if err := c.db.First(&environment, envID).Error; err == nil {
					envApp[envID] = map[string]interface{}{
						"env": map[string]interface{}{
							"environment_code": environment.EnvironmentCode,
							"name":             environment.Name,
							"id":               environment.ID,
						},
					}
				}
			}
		}
	}

	// 安全地处理其他JSONField - 避免复杂的序列化，直接返回Data
	jsonFields := map[string]*models.JSONField{
		"repo":         &app.RepoSettings,
		"target":       &app.TargetSettings,
		"template":     &app.TemplateData,
		"multiple_ids": &app.MultiAppIDs,
		"dockerfile":   &app.DockerSettings,
		"notify":       &app.NotifySettings,
		"can_edit":     &app.EditPermission,
		"modules":      &app.ModuleSettings,
	}

	for fieldName, field := range jsonFields {
		if field != nil && field.Data != nil {
			appData[fieldName] = field.Data
		} else {
			// 提供合理的默认值
			appData[fieldName] = getDefaultValue(fieldName)
		}
	}

	appData["appinfo"] = appInfos
	appData["env_app"] = envApp

	// 处理团队成员信息 - 查询完整用户信息
	teamMembers := map[string]interface{}{
		"op":      []map[string]interface{}{},
		"dev":     []map[string]interface{}{},
		"test":    []map[string]interface{}{},
		"product": []map[string]interface{}{},
	}

	// 安全地处理TeamMembers JSONField
	if app.TeamMembers.Data != nil {
		if membersData, ok := app.TeamMembers.Data.(map[string]interface{}); ok {
			for role, memberIds := range membersData {
				if ids, ok := memberIds.([]interface{}); ok {
					var members []map[string]interface{}
					for _, id := range ids {
						if userID, ok := id.(float64); ok {
							var user models.UserProfile
							if err := c.db.First(&user, uint(userID)).Error; err == nil {
								members = append(members, map[string]interface{}{
									"id":       user.ID,
									"name":     user.FirstName,
									"username": user.Username,
								})
							}
						}
					}
					if teamMembers[role] != nil {
						teamMembers[role] = members
					}
				}
			}
		}
	}

	appData["team_members"] = teamMembers
	appData["related_apps"] = []map[string]interface{}{} // 暂时为空

	ctx.JSON(http.StatusOK, gin.H{
		"code":    20000,
		"message": "获取应用详情成功",
		"data":    appData,
	})
}

// getDefaultValue 为JSONField提供默认值
func getDefaultValue(fieldName string) interface{} {
	switch fieldName {
	case "target":
		return map[string]interface{}{
			"key":   "default",
			"value": "default",
		}
	case "dockerfile":
		return map[string]interface{}{
			"key":   "default",
			"value": "default",
		}
	case "ports":
		return []interface{}{
			map[string]interface{}{
				"port":      8080,
				"protocol":  "tcp",
				"node_port": 0,
			},
		}
	case "multiple_ids", "can_edit", "modules":
		return []interface{}{}
	default:
		return map[string]interface{}{}
	}
}

// CreateApplication 创建应用 - 对应Django的MicroAppViewSet.create
// @Tags Applications
// @Summary 创建应用
// @Description 创建新的微服务应用
// @Accept json
// @Produce json
// @Param application body models.MicroAppCreateRequest true "应用信息"
// @Success 201 {object} map[string]interface{} "创建成功"
// @Router /api/v1/cmdb/app [post]
func (c *ApplicationController) CreateApplication(ctx *gin.Context) {
	var req models.MicroAppCreateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误: " + err.Error(),
		})
		return
	}

	// 生成应用ID (product.project.appname格式)
	var appID string
	if req.ProjectID != nil {
		var project models.Project
		if err := c.db.Preload("Product").First(&project, *req.ProjectID).Error; err == nil {
			if project.Product != nil {
				appID = strings.ToLower(project.Product.Name + "." + project.Name + "." + req.Name)
			} else {
				appID = strings.ToLower(project.Name + "." + req.Name)
			}
		} else {
			appID = strings.ToLower(req.Name)
		}
	} else {
		appID = strings.ToLower(req.Name)
	}

	app := models.MicroApp{
		AppCode:        appID,
		Name:           req.Name,
		ProjectID:      req.ProjectID,
		RepoSettings:   req.RepoSettings,
		TargetSettings: req.TargetSettings,
		TeamMembers:    req.TeamMembers,
		CategoryName:   req.CategoryName,
		TemplateData:   req.TemplateData,
		LanguageCode:   req.LanguageCode,
		BuildCommand:   req.BuildCommand,
		IsMultiApp:     req.IsMultiApp,
		MultiAppIDs:    req.MultiAppIDs,
		DockerSettings: req.DockerSettings,
		Enabled:        req.Enabled,
		Description:    req.Description,
		NotifySettings: req.NotifySettings,
		EditPermission: req.EditPermission,
		DeploymentType: req.DeploymentType,
		ModuleSettings: req.ModuleSettings,
		ScanBranch:     req.ScanBranch,
		PipelineID:     req.PipelineID,
	}

	if err := c.db.Create(&app).Error; err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "创建应用失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusCreated, gin.H{
		"code":    201,
		"message": "创建应用成功",
		"data": gin.H{
			"id":     app.ID,
			"app_id": app.AppCode,
		},
	})
}

// UpdateApplication 更新应用 - 对应Django的MicroAppViewSet.update
// @Tags Applications
// @Summary 更新应用
// @Description 更新应用信息
// @Accept json
// @Produce json
// @Param id path int true "应用ID"
// @Param application body models.MicroAppCreateRequest true "应用信息"
// @Success 200 {object} map[string]interface{} "更新成功"
// @Router /api/v1/cmdb/app/{id} [put]
func (c *ApplicationController) UpdateApplication(ctx *gin.Context) {
	id := ctx.Param("id")

	// 将字符串ID转换为整数
	appID, err := strconv.Atoi(id)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的应用ID",
		})
		return
	}

	var req models.MicroAppCreateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误: " + err.Error(),
		})
		return
	}

	var app models.MicroApp
	if err := c.db.First(&app, appID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			ctx.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": "应用不存在",
			})
			return
		}
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "查询应用失败: " + err.Error(),
		})
		return
	}

	// 更新字段
	updateData := map[string]interface{}{
		"name":          req.Name,
		"project_id":    req.ProjectID,
		"repo":          req.RepoSettings,
		"target":        req.TargetSettings,
		"team_members":  req.TeamMembers,
		"category":      req.CategoryName,
		"template":      req.TemplateData,
		"language_code": req.LanguageCode,
		"build_command": req.BuildCommand,
		"multiple_app":  req.IsMultiApp,
		"multiple_ids":  req.MultiAppIDs,
		"dockerfile":    req.DockerSettings,
		"enabled":       req.Enabled,
		"desc":          req.Description,
		"notify":        req.NotifySettings,
		"can_edit":      req.EditPermission,
		"is_k8s":        req.DeploymentType,
		"modules":       req.ModuleSettings,
		"pipeline_id":   req.PipelineID,
		"scan_branch":   req.ScanBranch,
	}

	if err := c.db.Model(&app).Updates(updateData).Error; err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "更新应用失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "更新应用成功",
	})
}

// DeleteApplication 删除应用 - 对应Django的MicroAppViewSet.destroy
// @Tags Applications
// @Summary 删除应用
// @Description 删除指定的应用
// @Accept json
// @Produce json
// @Param id path int true "应用ID"
// @Success 204 "删除成功"
// @Router /api/v1/cmdb/app/{id} [delete]
func (c *ApplicationController) DeleteApplication(ctx *gin.Context) {
	id := ctx.Param("id")

	// 将字符串ID转换为整数
	appID, err := strconv.Atoi(id)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的应用ID",
		})
		return
	}

	var app models.MicroApp
	if err := c.db.First(&app, appID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			ctx.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": "应用不存在",
			})
			return
		}
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "查询应用失败: " + err.Error(),
		})
		return
	}

	if err := c.db.Delete(&app).Error; err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "删除应用失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusNoContent, gin.H{
		"code":    204,
		"message": "删除应用成功",
	})
}

// GetRepos 获取Git仓库列表 - 对应Django的MicroAppViewSet.get_repository
// @Tags Applications
// @Summary 获取Git仓库列表
// @Description 获取GitLab仓库项目列表
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param search query string false "搜索关键词"
// @Param project_id query string false "项目ID"
// @Param repo_ids query []string false "仓库ID列表"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Router /api/v1/cmdb/git/repo [get]
func (c *ApplicationController) GetRepos(ctx *gin.Context) {
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "20"))
	search := ctx.Query("search")
	projectID := ctx.Query("project_id")
	repoIDs := ctx.QueryArray("repo_ids[]")

	// 读取GitLab配置
	if c.config == nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "系统配置未初始化",
		})
		return
	}

	if c.config.Gitlab.URL == "" || c.config.Gitlab.Token == "" {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "GitLab配置不完整",
		})
		return
	}

	// 创建GitLab客户端
	client := utils.GetGitLabClient(c.config.Gitlab.URL, c.config.Gitlab.Token)

	var projects []*gitlab.Project
	var err error

	// 如果指定了项目ID，只获取该项目
	if projectID != "" {
		project, err := utils.GetGitLabProject(client, projectID)
		if err != nil {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"code":    50000,
				"message": "获取GitLab项目失败: " + err.Error(),
			})
			return
		}
		projects = []*gitlab.Project{project}
	} else {
		// 构建查询选项
		options := &gitlab.ListProjectsOptions{
			ListOptions: gitlab.ListOptions{
				Page:    page,
				PerPage: pageSize,
			},
			OrderBy: gitlab.Ptr("updated_at"),
			Sort:    gitlab.Ptr("desc"),
		}

		if search != "" {
			// 提取项目名称部分进行搜索
			searchKey := search
			if strings.Contains(search, "/") {
				parts := strings.Split(search, "/")
				searchKey = strings.TrimSuffix(parts[len(parts)-1], ".git")
			}
			options.Search = gitlab.Ptr(searchKey)
		}

		// 获取项目列表
		projects, err = utils.GetGitLabProjects(client, options)
		if err != nil {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"code":    50000,
				"message": "获取GitLab项目列表失败: " + err.Error(),
			})
			return
		}
	}

	// 如果有指定的repo_ids，确保它们在列表中
	if len(repoIDs) > 0 {
		existingIDs := make(map[string]bool)
		for _, project := range projects {
			existingIDs[fmt.Sprintf("%d", project.ID)] = true
		}

		// 获取不在当前列表中的指定仓库
		for _, repoID := range repoIDs {
			if !existingIDs[repoID] {
				project, err := utils.GetGitLabProject(client, repoID)
				if err == nil {
					// 将指定的项目添加到列表开头
					projects = append([]*gitlab.Project{project}, projects...)
				}
			}
		}
	}

	// 格式化响应数据
	data := make([]map[string]interface{}, len(projects))
	for i, project := range projects {
		data[i] = map[string]interface{}{
			"id":                  project.ID,
			"name":                project.Name,
			"description":         project.Description,
			"path_with_namespace": project.PathWithNamespace,
			"http_url_to_repo":    project.HTTPURLToRepo,
			"default_branch":      project.DefaultBranch,
			"created_at":          project.CreatedAt,
			"updated_at":          project.LastActivityAt,
		}
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 20000,
		"data": map[string]interface{}{
			"items": data,
			"total": len(data),
		},
	})
}

// GetBranches 获取仓库分支信息 - 对应Django的MicroAppViewSet.get_branches
// @Summary 获取仓库分支信息
// @Description 获取GitLab仓库的分支和标签信息
// @Tags 应用管理
// @Accept json
// @Produce json
// @Param project_id query string true "GitLab项目ID"
// @Param type query string false "类型(branch/tag)"
// @Param branch query string false "分支名称"
// @Param protected query string false "是否受保护(0:所有,1:受保护,2:不受保护)" default(0)
// @Success 200 {object} map[string]interface{} "成功"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 500 {object} map[string]interface{} "服务器内部错误"
// @Router /api/v1/cmdb/git/repo/branches [get]
func (c *ApplicationController) GetBranches(ctx *gin.Context) {
	projectID := ctx.Query("project_id")
	if projectID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "project_id参数是必需的",
		})
		return
	}

	projectType := ctx.Query("type")
	branchName := ctx.Query("branch")
	protected := ctx.DefaultQuery("protected", "0")

	// 读取GitLab配置
	if c.config == nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "系统配置未初始化",
		})
		return
	}

	if c.config.Gitlab.URL == "" || c.config.Gitlab.Token == "" {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "GitLab配置不完整",
		})
		return
	}

	// 创建GitLab客户端
	client := utils.GetGitLabClient(c.config.Gitlab.URL, c.config.Gitlab.Token)

	// 根据type参数返回不同数据
	switch projectType {
	case "tag":
		// 获取标签
		tags, err := utils.GetGitLabTags(client, projectID)
		if err != nil {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"code":    50000,
				"message": "获取标签失败: " + err.Error(),
			})
			return
		}

		// 格式化为前端所需的数据结构
		formattedTags := make([]map[string]interface{}, 0)
		for _, tag := range tags {
			formattedTags = append(formattedTags, c.formatGitLabTag(tag))
		}

		// 返回标签列表
		data := []map[string]interface{}{
			{
				"label":   "标签",
				"options": formattedTags,
			},
		}
		ctx.JSON(http.StatusOK, gin.H{
			"code": 20000,
			"data": data,
		})
		return

	case "branch":
		// 获取分支
		branches, err := c.getGitLabBranchesFiltered(client, projectID, branchName, protected)
		if err != nil {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"code":    50000,
				"message": "获取分支失败: " + err.Error(),
			})
			return
		}

		// 格式化为前端所需的数据结构
		formattedBranches := make([]map[string]interface{}, 0)
		for _, branch := range branches {
			formattedBranches = append(formattedBranches, c.formatGitLabBranch(branch))
		}

		// 返回分支列表
		data := []map[string]interface{}{
			{
				"label":   "分支",
				"options": formattedBranches,
			},
		}
		ctx.JSON(http.StatusOK, gin.H{
			"code": 20000,
			"data": data,
		})
		return

	default:
		// 返回分支和标签的分组数据
		branches, err := c.getGitLabBranchesFiltered(client, projectID, branchName, protected)
		if err != nil {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"code":    50000,
				"message": "获取分支失败: " + err.Error(),
			})
			return
		}

		tags, err := utils.GetGitLabTags(client, projectID)
		if err != nil {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"code":    50000,
				"message": "获取标签失败: " + err.Error(),
			})
			return
		}

		// 格式化为前端所需的数据结构
		formattedBranches := make([]map[string]interface{}, 0)
		for _, branch := range branches {
			formattedBranches = append(formattedBranches, c.formatGitLabBranch(branch))
		}

		formattedTags := make([]map[string]interface{}, 0)
		for _, tag := range tags {
			formattedTags = append(formattedTags, c.formatGitLabTag(tag))
		}

		data := []map[string]interface{}{
			{
				"label":   "分支",
				"options": formattedBranches,
			},
			{
				"label":   "标签",
				"options": formattedTags,
			},
		}

		ctx.JSON(http.StatusOK, gin.H{
			"code": 20000,
			"data": map[string]interface{}{
				"items": data,
				"total": len(data),
			},
		})
	}
}

// getGitLabBranchesFiltered 获取过滤后的GitLab分支
func (c *ApplicationController) getGitLabBranchesFiltered(client *gitlab.Client, projectID, search, protected string) ([]*gitlab.Branch, error) {
	// 获取所有分支
	branches, err := utils.GetGitLabBranches(client, projectID)
	if err != nil {
		return nil, err
	}

	// 应用搜索过滤
	if search != "" {
		var filtered []*gitlab.Branch
		for _, branch := range branches {
			if strings.Contains(strings.ToLower(branch.Name), strings.ToLower(search)) {
				filtered = append(filtered, branch)
			}
		}
		branches = filtered
	}

	// 根据保护状态过滤分支
	if protected != "0" {
		var filtered []*gitlab.Branch
		isProtected := protected == "1"
		for _, branch := range branches {
			if branch.Protected == isProtected {
				filtered = append(filtered, branch)
			}
		}
		branches = filtered
	}

	return branches, nil
}

// formatGitLabBranch 格式化GitLab分支数据
func (c *ApplicationController) formatGitLabBranch(branch *gitlab.Branch) map[string]interface{} {
	result := map[string]interface{}{
		"name":      branch.Name,
		"uid":       fmt.Sprintf("heads:%s", branch.Name),
		"protected": branch.Protected,
		"web_url":   branch.WebURL,
	}

	// 处理commit信息
	if branch.Commit != nil {
		result["commit"] = map[string]interface{}{
			"id":             branch.Commit.ID,
			"message":        branch.Commit.Message,
			"author_name":    branch.Commit.AuthorName,
			"committed_date": branch.Commit.CommittedDate,
		}

		result["id"] = branch.Commit.ID
		if len(branch.Commit.ID) >= 8 {
			result["short_id"] = branch.Commit.ID[:8]
		} else {
			result["short_id"] = branch.Commit.ID
		}

		result["message"] = branch.Commit.Message
		result["author_name"] = branch.Commit.AuthorName
		result["committed_date"] = branch.Commit.CommittedDate
		result["created_at"] = branch.Commit.CommittedDate

		// 提取第一行作为标题
		if idx := strings.Index(branch.Commit.Message, "\n"); idx > 0 {
			result["title"] = branch.Commit.Message[:idx]
		} else {
			result["title"] = branch.Commit.Message
		}
	}

	return result
}

// formatGitLabTag 格式化GitLab标签数据
func (c *ApplicationController) formatGitLabTag(tag *gitlab.Tag) map[string]interface{} {
	result := map[string]interface{}{
		"name":    tag.Name,
		"uid":     fmt.Sprintf("tags:%s", tag.Name),
		"web_url": "",
	}

	// 处理commit信息
	if tag.Commit != nil {
		result["commit"] = map[string]interface{}{
			"id":             tag.Commit.ID,
			"message":        tag.Commit.Message,
			"author_name":    tag.Commit.AuthorName,
			"committed_date": tag.Commit.CommittedDate,
		}

		result["id"] = tag.Commit.ID
		if len(tag.Commit.ID) >= 8 {
			result["short_id"] = tag.Commit.ID[:8]
		} else {
			result["short_id"] = tag.Commit.ID
		}

		result["message"] = tag.Commit.Message
		result["author_name"] = tag.Commit.AuthorName
		result["committed_date"] = tag.Commit.CommittedDate
		result["created_at"] = tag.Commit.CommittedDate

		// 提取第一行作为标题
		if idx := strings.Index(tag.Commit.Message, "\n"); idx > 0 {
			result["title"] = tag.Commit.Message[:idx]
		} else {
			result["title"] = tag.Commit.Message
		}
	}

	return result
}

// GetHarbor 获取Harbor仓库信息 - 对应Django的MicroAppViewSet.get_harbor
// @Tags Applications
// @Summary 获取Harbor仓库信息
// @Description 获取Harbor仓库的项目、镜像仓库或标签信息
// @Accept json
// @Produce json
// @Param type query string true "类型：projects|repos|tags"
// @Param search query string false "搜索关键字"
// @Param project_id query string false "项目ID（type=repos时需要）"
// @Param project_name query string false "项目名称"
// @Param image query string false "镜像名称（type=tags时需要）"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Router /api/v1/cmdb/harbor [get]
func (c *ApplicationController) GetHarbor(ctx *gin.Context) {
	reqType := ctx.Query("type")
	search := ctx.Query("search")
	projectID := ctx.Query("project_id")
	projectName := ctx.Query("project_name")
	image := ctx.Query("image")

	// 读取Harbor配置
	if c.config == nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "系统配置未初始化",
		})
		return
	}

	if c.config.Harbor.URL == "" || c.config.Harbor.Username == "" || c.config.Harbor.Password == "" {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "Harbor配置不完整",
		})
		return
	}

	harborURL := c.config.Harbor.URL
	harborUsername := c.config.Harbor.Username
	harborPassword := c.config.Harbor.Password

	// 创建HTTP客户端
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	switch reqType {
	case "projects":
		// 获取Harbor项目列表
		data, err := c.getHarborProjects(client, harborURL, harborUsername, harborPassword, search)
		if err != nil {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"code":    50000,
				"message": "获取Harbor项目失败: " + err.Error(),
			})
			return
		}
		projects := make([]map[string]interface{}, 0)
		for _, project := range data {
			projects = append(projects, map[string]interface{}{
				"id":         project["project_id"],
				"name":       project["name"],
				"repo_count": project["repo_count"],
			})
		}
		ctx.JSON(http.StatusOK, gin.H{
			"code": 20000,
			"data": gin.H{
				"items": projects,
				"total": len(projects),
			},
		})
		return

	case "repos":
		// 获取项目的镜像仓库列表
		if projectName == "" && projectID == "" {
			ctx.JSON(http.StatusBadRequest, gin.H{
				"code":    400,
				"message": "type=repos时，project_name或project_id参数是必需的",
			})
			return
		}

		var targetProjectName string
		if projectName != "" {
			targetProjectName = projectName
		} else {
			// 通过project_id获取项目名称
			projects, err := c.getHarborProjects(client, harborURL, harborUsername, harborPassword, "")
			if err != nil {
				ctx.JSON(http.StatusInternalServerError, gin.H{
					"code":    50000,
					"message": "获取Harbor项目失败: " + err.Error(),
				})
				return
			}
			for _, project := range projects {
				if fmt.Sprintf("%v", project["project_id"]) == projectID {
					targetProjectName = project["name"].(string)
					break
				}
			}
			if targetProjectName == "" {
				ctx.JSON(http.StatusNotFound, gin.H{
					"code":    404,
					"message": "找不到指定的项目ID",
				})
				return
			}
		}

		data, err := c.getHarborRepositories(client, harborURL, harborUsername, harborPassword, targetProjectName, search)
		if err != nil {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"code":    50000,
				"message": "获取Harbor仓库失败: " + err.Error(),
			})
			return
		}
		ctx.JSON(http.StatusOK, gin.H{
			"code": 20000,
			"data": gin.H{
				"items": data,
				"total": len(data),
			},
		})
		return

	case "tags":
		// 获取镜像标签列表
		if projectName == "" || image == "" {
			ctx.JSON(http.StatusBadRequest, gin.H{
				"code":    400,
				"message": "type=tags时，project_name和image参数是必需的",
			})
			return
		}

		data, err := c.getHarborTags(client, harborURL, harborUsername, harborPassword, projectName, image)
		if err != nil {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"code":    50000,
				"message": "获取Harbor标签失败: " + err.Error(),
			})
			return
		}
		ctx.JSON(http.StatusOK, gin.H{
			"code": 20000,
			"data": gin.H{
				"items": data,
				"total": len(data),
			},
		})
		return

	default:
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "type参数必须是projects、repos或tags",
		})
		return
	}
}

// getHarborProjects 获取Harbor项目列表
func (c *ApplicationController) getHarborProjects(client *http.Client, harborURL, username, password, search string) ([]map[string]interface{}, error) {
	// 不再使用Harbor客户端SDK，直接使用http客户端

	// 构建API调用的URL
	apiURL := fmt.Sprintf("%s/api/v2.0/projects", strings.TrimSuffix(harborURL, "/"))
	req, err := http.NewRequest("GET", apiURL, nil)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %s", err)
	}

	// 设置认证和请求参数
	req.SetBasicAuth(username, password)
	req.Header.Set("Content-Type", "application/json")

	// 添加查询参数
	q := req.URL.Query()
	q.Add("page_size", "100")
	if search != "" {
		q.Add("name", search)
	}
	req.URL.RawQuery = q.Encode()

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("请求Harbor API失败: %s", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		bodyBytes, _ := ioutil.ReadAll(resp.Body)
		return nil, fmt.Errorf("Harbor API返回错误状态: %d, %s", resp.StatusCode, string(bodyBytes))
	}

	// 读取并解析响应
	bodyBytes, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %s", err)
	}

	var projects []map[string]interface{}
	if err := json.Unmarshal(bodyBytes, &projects); err != nil {
		return nil, fmt.Errorf("解析响应失败: %s", err)
	}

	return projects, nil
}

// getHarborRepositories 获取Harbor项目的镜像仓库列表
func (c *ApplicationController) getHarborRepositories(client *http.Client, harborURL, username, password, projectName, search string) ([]map[string]interface{}, error) {
	// 构建API调用的URL
	apiURL := fmt.Sprintf("%s/api/v2.0/projects/%s/repositories", strings.TrimSuffix(harborURL, "/"), projectName)
	req, err := http.NewRequest("GET", apiURL, nil)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %s", err)
	}

	// 设置认证和请求参数
	req.SetBasicAuth(username, password)
	req.Header.Set("Content-Type", "application/json")

	// 添加查询参数
	q := req.URL.Query()
	q.Add("page_size", "100")
	if search != "" {
		q.Add("q", fmt.Sprintf("name~%s", search))
	}
	req.URL.RawQuery = q.Encode()

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("请求Harbor API失败: %s", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		bodyBytes, _ := ioutil.ReadAll(resp.Body)
		return nil, fmt.Errorf("Harbor API返回错误状态: %d, %s", resp.StatusCode, string(bodyBytes))
	}

	// 读取并解析响应
	bodyBytes, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %s", err)
	}

	var repositories []map[string]interface{}
	if err := json.Unmarshal(bodyBytes, &repositories); err != nil {
		return nil, fmt.Errorf("解析响应失败: %s", err)
	}

	return repositories, nil
}

// getHarborTags 获取Harbor镜像的标签列表
func (c *ApplicationController) getHarborTags(client *http.Client, harborURL, username, password, projectName, image string) ([]map[string]interface{}, error) {
	// 构建API调用的URL - 注意，Harbor V2 API 使用 artifacts 端点获取标签
	// 对镜像名称进行处理，由于Harbor API的路径结构，需要将项目名称从镜像名称中移除
	// 例如：如果镜像名称是 "basic/nginx"，项目名称是 "basic"，则在URL中应使用 "nginx"
	repositoryName := image
	if strings.HasPrefix(image, projectName+"/") {
		repositoryName = strings.TrimPrefix(image, projectName+"/")
	}

	apiURL := fmt.Sprintf("%s/api/v2.0/projects/%s/repositories/%s/artifacts",
		strings.TrimSuffix(harborURL, "/"),
		projectName,
		repositoryName)

	req, err := http.NewRequest("GET", apiURL, nil)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %s", err)
	}

	// 设置认证和请求参数
	req.SetBasicAuth(username, password)
	req.Header.Set("Content-Type", "application/json")

	// 添加查询参数
	q := req.URL.Query()
	q.Add("page_size", "100")
	q.Add("with_tag", "true") // 确保包含标签信息
	req.URL.RawQuery = q.Encode()

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("请求Harbor API失败: %s", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		bodyBytes, _ := ioutil.ReadAll(resp.Body)
		return nil, fmt.Errorf("Harbor API返回错误状态: %d, %s", resp.StatusCode, string(bodyBytes))
	}

	// 读取并解析响应
	bodyBytes, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %s", err)
	}

	var artifacts []map[string]interface{}
	if err := json.Unmarshal(bodyBytes, &artifacts); err != nil {
		return nil, fmt.Errorf("解析响应失败: %s", err)
	}

	// 提取标签信息
	var result []map[string]interface{}
	for _, artifact := range artifacts {
		if tags, ok := artifact["tags"].([]interface{}); ok {
			for _, tag := range tags {
				if tagInfo, ok := tag.(map[string]interface{}); ok {
					tagData := map[string]interface{}{
						"name":      tagInfo["name"],
						"push_time": tagInfo["push_time"],
						"pull_time": tagInfo["pull_time"],
						"digest":    artifact["digest"],
						"size":      artifact["size"],
						"type":      artifact["type"],
					}
					result = append(result, tagData)
				}
			}
		}
	}

	return result, nil
}

// GetAppInfos 获取应用服务列表 - 对应Django的AppInfoViewSet.list
// @Tags Applications
// @Summary 获取应用服务列表
// @Description 获取应用服务列表，支持分页和过滤
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页大小" default(20)
// @Param app_id query int false "应用ID过滤"
// @Param environment query int false "环境ID过滤"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Router /api/v1/cmdb/app/service [get]
func (c *ApplicationController) GetAppInfos(ctx *gin.Context) {
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "20"))
	appIDStr := ctx.Query("app_id")
	environmentStr := ctx.Query("environment")
	var appInfos []models.AppInfo
	var total int64
	query := c.db.Model(&models.AppInfo{})
	if appIDStr != "" {
		query = query.Where("app_id = ?", appIDStr)
	}
	if environmentStr != "" {
		query = query.Where("environment_id = ?", environmentStr)
	}
	if err := query.Count(&total).Error; err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"code": 50000, "message": "获取应用服务列表失败: " + err.Error()})
		return
	}
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Find(&appInfos).Error; err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"code": 50000, "message": "获取应用服务列表失败: " + err.Error()})
		return
	}
	for i := range appInfos {
		if appInfos[i].AppID != nil {
			var app models.MicroApp
			if err := c.db.First(&app, *appInfos[i].AppID).Error; err == nil {
				appInfos[i].App = &app
			}
		}
		if appInfos[i].EnvironmentID != nil {
			var env models.Environment
			if err := c.db.First(&env, *appInfos[i].EnvironmentID).Error; err == nil {
				appInfos[i].Environment = &env
			}
		}
	}
	ctx.JSON(http.StatusOK, gin.H{
		"data":    map[string]interface{}{"items": appInfos, "total": total, "page": page, "page_size": pageSize},
		"code":    20000,
		"message": "success",
	})
}

// AppEditor 应用编辑权限管理 - 对应Django的AppInfoViewSet.app_editor
// @Tags Applications
// @Summary 应用编辑权限管理
// @Description 获取或设置应用的编辑权限用户列表
// @Accept json
// @Produce json
// @Param id path int true "应用服务ID"
// @Param env query int false "环境ID"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Router /api/v1/cmdb/app/service/{id}/editor [get]
// @Router /api/v1/cmdb/app/service/{id}/editor [put]
func (c *ApplicationController) AppEditor(ctx *gin.Context) {
	method := strings.ToLower(ctx.Request.Method)
	id := ctx.Param("id")

	var env string
	if method == "get" {
		env = ctx.Query("env")
	} else {
		// PUT 请求从 body 获取
		var body map[string]interface{}
		if err := ctx.ShouldBindJSON(&body); err != nil {
			ctx.JSON(http.StatusBadRequest, gin.H{
				"code":    40000,
				"message": "请求参数格式错误: " + err.Error(),
			})
			return
		}
		if envVal, ok := body["env"].(string); ok {
			env = envVal
		}
	}

	if env != "" {
		// 操作 AppInfo.can_edit
		var appInfo models.AppInfo
		if err := c.db.Where("app_id = ? AND environment_id = ?", id, env).First(&appInfo).Error; err != nil {
			ctx.JSON(http.StatusNotFound, gin.H{
				"status":  "failed",
				"code":    40000,
				"message": "当前环境没有数据",
			})
			return
		}

		if method == "get" {
			ctx.JSON(http.StatusOK, gin.H{
				"status": "success",
				"code":   20000,
				"data": map[string]interface{}{
					"env":   env,
					"users": appInfo.EditPermission.Data,
				},
			})
			return
		}

		// PUT 操作
		var body map[string]interface{}
		if err := ctx.ShouldBindJSON(&body); err != nil {
			ctx.JSON(http.StatusBadRequest, gin.H{
				"code":    40000,
				"message": "请求参数格式错误: " + err.Error(),
			})
			return
		}

		if users, ok := body["user"]; ok {
			appInfo.EditPermission.Data = users
			if err := c.db.Save(&appInfo).Error; err != nil {
				ctx.JSON(http.StatusInternalServerError, gin.H{
					"status":  "failed",
					"code":    50000,
					"message": "更新权限失败: " + err.Error(),
				})
				return
			}
		}

		ctx.JSON(http.StatusOK, gin.H{
			"status":  "success",
			"code":    20000,
			"message": "app 权限用户更新完毕",
		})

	} else {
		// 操作 MicroApp.can_edit
		var microApp models.MicroApp
		if err := c.db.First(&microApp, id).Error; err != nil {
			ctx.JSON(http.StatusNotFound, gin.H{
				"status":  "failed",
				"code":    40000,
				"message": "应用不存在",
			})
			return
		}

		if method == "get" {
			ctx.JSON(http.StatusOK, gin.H{
				"status": "success",
				"code":   20000,
				"data": map[string]interface{}{
					"env":   env,
					"users": microApp.EditPermission.Data,
				},
			})
			return
		}

		// PUT 操作
		var body map[string]interface{}
		if err := ctx.ShouldBindJSON(&body); err != nil {
			ctx.JSON(http.StatusBadRequest, gin.H{
				"code":    40000,
				"message": "请求参数格式错误: " + err.Error(),
			})
			return
		}

		if users, ok := body["user"]; ok {
			microApp.EditPermission.Data = users
			if err := c.db.Save(&microApp).Error; err != nil {
				ctx.JSON(http.StatusInternalServerError, gin.H{
					"status":  "failed",
					"code":    50000,
					"message": "更新权限失败: " + err.Error(),
				})
				return
			}
		}

		ctx.JSON(http.StatusOK, gin.H{
			"status":  "success",
			"code":    20000,
			"message": "app 权限用户更新完毕",
		})
	}
}

// GetProjectConfigInheritTemplate 获取项目配置的可继承模板 - 对应Django的ProjectConfigViewSet.get_inherit_template
// @Tags Applications
// @Summary 获取项目配置的可继承模板
// @Description 获取项目配置的可继承模板，基于环境
// @Accept json
// @Produce json
// @Param environment query int true "环境ID"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Router /api/v1/cmdb/project/config/template/inherit [get]
func (c *ApplicationController) GetProjectConfigInheritTemplate(ctx *gin.Context) {
	environmentStr := ctx.Query("environment")
	if environmentStr == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"message": "Environment is required.",
			"code":    50000,
		})
		return
	}

	environmentID, err := strconv.Atoi(environmentStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"message": "环境ID格式错误",
			"code":    50000,
		})
		return
	}

	// 获取环境信息
	var environment models.Environment
	if err := c.db.First(&environment, environmentID).Error; err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{
			"message": "环境不存在",
			"code":    50000,
		})
		return
	}

	// 处理环境模板，设置所有项为非自定义
	templateData := make(map[string]interface{})
	if environment.TemplateSettings.Data != nil {
		if template, ok := environment.TemplateSettings.Data.(map[string]interface{}); ok {
			templateData = template
		}
	}

	ctx.JSON(http.StatusOK, gin.H{
		"data": templateData,
		"code": 20000,
	})
}

// GetProjectConfigs 获取项目配置列表 - 对应Django的ProjectConfigViewSet.list
// @Tags Applications
// @Summary 获取项目配置列表
// @Description 获取项目配置列表，支持按项目和环境过滤
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页大小" default(20)
// @Param project query int false "项目ID过滤"
// @Param environment query int false "环境ID过滤"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Router /api/v1/cmdb/project/config [get]
func (c *ApplicationController) GetProjectConfigs(ctx *gin.Context) {
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "20"))
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 50
	}

	projectIDStr := ctx.Query("project_id")
	environmentIDStr := ctx.Query("environment_id")

	var projectConfigs []models.ProjectConfig
	var total int64

	// 构建查询
	query := c.db.Model(&models.ProjectConfig{})

	// 添加过滤条件
	if projectIDStr != "" {
		projectID, err := strconv.Atoi(projectIDStr)
		if err != nil {
			ctx.JSON(http.StatusBadRequest, gin.H{
				"code":    50000,
				"message": "无效的项目ID",
			})
			return
		}

		// 验证项目是否存在
		var project models.Project
		if err := c.db.First(&project, projectID).Error; err != nil {
			ctx.JSON(http.StatusNotFound, gin.H{
				"code":    50000,
				"message": "项目不存在",
			})
			return
		}

		query = query.Where("project_id = ?", projectID)
	}

	if environmentIDStr != "" {
		environmentID, err := strconv.Atoi(environmentIDStr)
		if err != nil {
			ctx.JSON(http.StatusBadRequest, gin.H{
				"code":    50000,
				"message": "无效的环境ID",
			})
			return
		}

		// 验证环境是否存在
		var environment models.Environment
		if err := c.db.First(&environment, environmentID).Error; err != nil {
			ctx.JSON(http.StatusNotFound, gin.H{
				"code":    50000,
				"message": "环境不存在",
			})
			return
		}

		query = query.Where("environment_id = ?", environmentID)
	}

	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "获取项目配置列表失败: " + err.Error(),
		})
		return
	}

	// 查询数据
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Find(&projectConfigs).Error; err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "获取项目配置列表失败: " + err.Error(),
		})
		return
	}

	// 手动加载关联的项目和环境信息
	for i := range projectConfigs {
		// 加载项目信息
		var project models.Project
		if err := c.db.First(&project, projectConfigs[i].ProjectID).Error; err == nil {
			projectConfigs[i].Project = project
		}

		// 加载环境信息
		var environment models.Environment
		if err := c.db.First(&environment, projectConfigs[i].EnvironmentID).Error; err == nil {
			projectConfigs[i].Environment = environment
		}
	}

	ctx.JSON(http.StatusOK, gin.H{
		"data":    map[string]interface{}{"items": projectConfigs, "total": total, "page": page, "page_size": pageSize},
		"code":    20000,
		"message": "success",
	})
}

// GetRobot 获取项目机器人列表 - 对应Django的ProjectViewSet.get_robot
// @Tags Applications
// @Summary 获取项目机器人列表
// @Description 获取系统中配置的机器人列表
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "成功响应"
// @Router /api/v1/cmdb/project/robot [get]
func (c *ApplicationController) GetRobot(ctx *gin.Context) {
	var robots []models.SystemConfig

	// 查询type为'robot'的系统配置
	if err := c.db.Where("type = ?", "robot").Find(&robots).Error; err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "获取机器人列表失败: " + err.Error(),
		})
		return
	}

	// 构建返回数据
	var data []map[string]interface{}
	for _, robot := range robots {
		robotData := map[string]interface{}{
			"id":   robot.ID,
			"name": robot.Name,
		}

		// 解析config JSON获取robot_type
		var config map[string]interface{}
		robotType := "dingtalk" // 默认值
		if robot.Config != "" {
			if err := json.Unmarshal([]byte(robot.Config), &config); err == nil {
				if typ, ok := config["type"].(string); ok {
					robotType = typ
				}
			}
		}
		robotData["robot_type"] = robotType

		data = append(data, robotData)
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 20000,
		"data": data,
	})
}

// GetProjectConfig 获取项目配置详情 - 对应Django的ProjectConfigViewSet.retrieve
// @Tags Applications
// @Summary 获取项目配置详情
// @Description 根据ID获取项目配置详情
// @Accept json
// @Produce json
// @Param id path int true "项目配置ID"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Router /api/v1/cmdb/project/config/{id} [get]
func (c *ApplicationController) GetProjectConfig(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    50000,
			"message": "无效的项目配置ID",
		})
		return
	}

	var projectConfig models.ProjectConfig
	if err := c.db.First(&projectConfig, id).Error; err != nil {
		ctx.JSON(http.StatusOK, gin.H{
			"code":    50000,
			"message": "项目配置不存在",
		})
		return
	}

	// 加载关联的项目和环境信息
	var project models.Project
	if err := c.db.First(&project, projectConfig.ProjectID).Error; err == nil {
		projectConfig.Project = project
	}

	var environment models.Environment
	if err := c.db.First(&environment, projectConfig.EnvironmentID).Error; err == nil {
		projectConfig.Environment = environment
	}

	ctx.JSON(http.StatusOK, gin.H{
		"data":    projectConfig,
		"code":    20000,
		"message": "success",
	})
}

// CreateProjectConfig 创建项目配置 - 对应Django的ProjectConfigViewSet.create
// @Tags Applications
// @Summary 创建项目配置
// @Description 创建新的项目配置
// @Accept json
// @Produce json
// @Param project_config body models.ProjectConfig true "项目配置信息"
// @Success 201 {object} map[string]interface{} "成功响应"
// @Router /api/v1/cmdb/project/config [post]
func (c *ApplicationController) CreateProjectConfig(ctx *gin.Context) {
	// 直接从请求体中解析原始数据，以便正确处理复杂的嵌套JSON结构
	var requestData models.ProjectConfig
	if err := ctx.ShouldBindJSON(&requestData); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    40000,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	// 验证项目ID
	if requestData.ProjectID == 0 {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    40000,
			"message": "项目ID不能为空",
		})
		return
	}

	// 验证项目是否存在
	var project models.Project
	if err := c.db.First(&project, requestData.ProjectID).Error; err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    40000,
			"message": "项目不存在",
		})
		return
	}

	// 验证环境ID
	if requestData.EnvironmentID == 0 {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    40000,
			"message": "环境ID不能为空",
		})
		return
	}

	// 验证环境是否存在
	var environment models.Environment
	if err := c.db.First(&environment, requestData.EnvironmentID).Error; err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    40000,
			"message": "环境不存在",
		})
		return
	}

	// 检查该项目在该环境下是否已有配置
	var count int64
	c.db.Model(&models.ProjectConfig{}).
		Where("project_id = ? AND environment_id = ?", requestData.ProjectID, requestData.EnvironmentID).
		Count(&count)

	if count > 0 {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    40000,
			"message": "该项目在此环境下已有配置",
		})
		return
	}

	// 创建项目配置实例，使用请求数据
	projectConfig := models.ProjectConfig{
		ProjectID:     requestData.ProjectID,
		EnvironmentID: requestData.EnvironmentID,
		TemplateData:  requestData.TemplateData,
	}

	// 如果模板数据为空，初始化为空的JSON对象
	if projectConfig.TemplateData.Data == nil {
		projectConfig.TemplateData.Data = map[string]interface{}{}
	}

	// 保存项目配置
	if err := c.db.Create(&projectConfig).Error; err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "创建项目配置失败: " + err.Error(),
		})
		return
	}

	// 重新查询以获取完整数据，包括ID和关联信息
	if err := c.db.Preload("Project").Preload("Environment").First(&projectConfig, projectConfig.ID).Error; err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "获取创建的项目配置失败",
		})
		return
	}

	ctx.JSON(http.StatusCreated, gin.H{
		"code":    20000,
		"message": "创建项目配置成功",
		"data":    projectConfig,
	})
}

// UpdateProjectConfig 更新项目配置 - 对应Django的ProjectConfigViewSet.update
// @Tags Applications
// @Summary 更新项目配置
// @Description 更新项目配置信息
// @Accept json
// @Produce json
// @Param id path int true "项目配置ID"
// @Param project_config body models.ProjectConfig true "项目配置信息"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Router /api/v1/cmdb/project/config/{id} [put]
func (c *ApplicationController) UpdateProjectConfig(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    40000,
			"message": "无效的项目配置ID",
		})
		return
	}

	// 检查项目配置是否存在
	var existingConfig models.ProjectConfig
	if err := c.db.First(&existingConfig, id).Error; err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{
			"code":    40000,
			"message": "项目配置不存在",
		})
		return
	}

	// 直接从请求体中解析原始数据，以便正确处理复杂的嵌套JSON结构
	var requestData models.ProjectConfig
	if err := ctx.ShouldBindJSON(&requestData); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    40000,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	// 从请求数据中提取项目ID和环境ID
	// var projectID uint = existingConfig.ProjectID
	// var environmentID uint = existingConfig.EnvironmentID

	// // 处理项目ID字段，优先使用project_id
	// if projectIDVal, exists := requestData["project_id"]; exists && projectIDVal != nil {
	// 	switch v := projectIDVal.(type) {
	// 	case float64:
	// 		projectID = uint(v)
	// 	case int:
	// 		projectID = uint(v)
	// 	case string:
	// 		if id, err := strconv.ParseUint(v, 10, 32); err == nil {
	// 			projectID = uint(id)
	// 		}
	// 	}
	// } else if projectVal, exists := requestData["project"]; exists && projectVal != nil {
	// 	// 如果没有project_id，尝试使用project字段
	// 	switch v := projectVal.(type) {
	// 	case float64:
	// 		projectID = uint(v)
	// 	case int:
	// 		projectID = uint(v)
	// 	case string:
	// 		if id, err := strconv.ParseUint(v, 10, 32); err == nil {
	// 			projectID = uint(id)
	// 		}
	// 	case map[string]interface{}:
	// 		if idVal, ok := v["id"].(float64); ok {
	// 			projectID = uint(idVal)
	// 		}
	// 	}
	// }

	// // 处理环境ID字段，优先使用environment_id
	// if envIDVal, exists := requestData["environment_id"]; exists && envIDVal != nil {
	// 	switch v := envIDVal.(type) {
	// 	case float64:
	// 		environmentID = uint(v)
	// 	case int:
	// 		environmentID = uint(v)
	// 	case string:
	// 		if id, err := strconv.ParseUint(v, 10, 32); err == nil {
	// 			environmentID = uint(id)
	// 		}
	// 	}
	// } else if envVal, exists := requestData["environment"]; exists && envVal != nil {
	// 	// 如果没有environment_id，尝试使用environment字段
	// 	switch v := envVal.(type) {
	// 	case float64:
	// 		environmentID = uint(v)
	// 	case int:
	// 		environmentID = uint(v)
	// 	case string:
	// 		if id, err := strconv.ParseUint(v, 10, 32); err == nil {
	// 			environmentID = uint(id)
	// 		}
	// 	case map[string]interface{}:
	// 		if idVal, ok := v["id"].(float64); ok {
	// 			environmentID = uint(idVal)
	// 		}
	// 	}
	// }

	// 验证项目ID
	if requestData.ProjectID != existingConfig.ProjectID {
		var project models.Project
		if err := c.db.First(&project, requestData.ProjectID).Error; err != nil {
			ctx.JSON(http.StatusBadRequest, gin.H{
				"code":    40000,
				"message": "项目不存在",
			})
			return
		}
	}

	// 验证环境ID
	if requestData.EnvironmentID != existingConfig.EnvironmentID {
		var environment models.Environment
		if err := c.db.First(&environment, requestData.EnvironmentID).Error; err != nil {
			ctx.JSON(http.StatusBadRequest, gin.H{
				"code":    40000,
				"message": "环境不存在",
			})
			return
		}

		// 检查是否与其他记录冲突
		var count int64
		c.db.Model(&models.ProjectConfig{}).
			Where("id != ? AND project_id = ? AND environment_id = ?",
				id, requestData.ProjectID, requestData.EnvironmentID).
			Count(&count)

		if count > 0 {
			ctx.JSON(http.StatusBadRequest, gin.H{
				"code":    40000,
				"message": "该项目在此环境下已有配置",
			})
			return
		}
	}

	// 构建更新字段
	updates := map[string]interface{}{
		"project_id":     requestData.ProjectID,
		"environment_id": requestData.EnvironmentID,
		"template_data":  requestData.TemplateData,
	}

	// 处理模板数据
	// if templateData, exists := requestData["template"]; exists && templateData != nil {
	// 	updates["template"] = templateData
	// }

	// 执行更新
	if err := c.db.Model(&models.ProjectConfig{}).Where("id = ?", id).Updates(updates).Error; err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "更新项目配置失败: " + err.Error(),
		})
		return
	}

	existingConfig.TemplateData = requestData.TemplateData
	existingConfig.ProjectID = requestData.ProjectID
	existingConfig.EnvironmentID = requestData.EnvironmentID

	// 获取更新后的配置
	// var updatedConfig models.ProjectConfig
	// if err := c.db.First(&updatedConfig, id).Error; err != nil {
	// 	ctx.JSON(http.StatusInternalServerError, gin.H{
	// 		"code":    50000,
	// 		"message": "获取更新后的项目配置失败",
	// 	})
	// 	return
	// }

	// 加载关联的项目和环境信息
	// var project models.Project
	// if err := c.db.First(&project, updatedConfig.ProjectID).Error; err == nil {
	// 	updatedConfig.Project = project
	// }

	// var environment models.Environment
	// if err := c.db.First(&environment, updatedConfig.EnvironmentID).Error; err == nil {
	// 	updatedConfig.Environment = environment
	// }

	ctx.JSON(http.StatusOK, gin.H{
		"code":    20000,
		"message": "更新项目配置成功",
		"data":    existingConfig,
	})
}

// DeleteProjectConfig 删除项目配置 - 对应Django的ProjectConfigViewSet.destroy
// @Tags Applications
// @Summary 删除项目配置
// @Description 删除项目配置
// @Accept json
// @Produce json
// @Param id path int true "项目配置ID"
// @Success 204 {object} map[string]interface{} "成功响应"
// @Router /api/v1/cmdb/project/config/{id} [delete]
func (c *ApplicationController) DeleteProjectConfig(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    50000,
			"message": "无效的项目配置ID",
		})
		return
	}

	// 检查项目配置是否存在
	var existingConfig models.ProjectConfig
	if err := c.db.First(&existingConfig, id).Error; err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{
			"code":    50000,
			"message": "项目配置不存在",
		})
		return
	}

	// 执行删除
	if err := c.db.Delete(&models.ProjectConfig{}, id).Error; err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "删除项目配置失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusNoContent, gin.H{
		"code":    20000,
		"message": "success",
	})
}

// GetAppInfoPreview 预览应用服务的YAML配置
// @Tags Applications
// @Summary 预览应用服务的YAML配置
// @Description 根据应用服务ID和镜像参数生成Kubernetes YAML配置
// @Accept json
// @Produce json
// @Param id path int true "应用服务ID"
// @Param image query string true "镜像地址"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Router /api/v1/cmdb/app/service/{id}/preview [get]
func (c *ApplicationController) GetAppInfoPreview(ctx *gin.Context) {
	// 获取应用服务ID
	idStr := ctx.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的应用服务ID",
		})
		return
	}

	// 获取镜像参数
	image := ctx.Query("image")
	if image == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "镜像参数不能为空",
		})
		return
	}

	// 查询应用服务信息
	var appInfo models.AppInfo
	if err := c.db.First(&appInfo, id).Error; err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "应用服务不存在: " + err.Error(),
		})
		return
	}

	// 查询应用信息
	var app models.MicroApp
	if err := c.db.First(&app, appInfo.AppID).Error; err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "应用不存在: " + err.Error(),
		})
		return
	}

	// 查询环境信息
	var environment models.Environment
	if err := c.db.First(&environment, appInfo.EnvironmentID).Error; err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "环境不存在: " + err.Error(),
		})
		return
	}

	// 查询项目信息
	var project models.Project
	if app.ProjectID != nil {
		if err := c.db.First(&project, *app.ProjectID).Error; err != nil {
			ctx.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": "项目不存在: " + err.Error(),
			})
			return
		}
	} else {
		ctx.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "应用未关联项目",
		})
		return
	}

	// 查询产品信息
	var product models.Product
	if project.ProductID != nil {
		if err := c.db.First(&product, *project.ProductID).Error; err != nil {
			ctx.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": "产品不存在: " + err.Error(),
			})
			return
		}
	} else {
		ctx.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "项目未关联产品",
		})
		return
	}

	// 生成Kubernetes命名空间
	namespace := fmt.Sprintf("%s-%s",
		strings.ToLower(environment.Name),
		strings.ToLower(product.Name))

	// 解析端口配置
	var ports []int32
	// 从JSONField中解析端口配置
	portsJSON, _ := json.Marshal(appInfo.PortSettings.Data)
	if len(portsJSON) > 0 && string(portsJSON) != "null" {
		var portConfig map[string]interface{}
		if err := json.Unmarshal(portsJSON, &portConfig); err == nil {
			if portsArray, ok := portConfig["ports"].([]interface{}); ok {
				for _, port := range portsArray {
					if portNum, ok := port.(float64); ok {
						ports = append(ports, int32(portNum))
					}
				}
			}
		}
	}

	// 如果没有配置端口，使用默认端口
	if len(ports) == 0 {
		ports = []int32{80}
	}

	// 构建容器端口
	var containerPorts []map[string]interface{}
	for _, port := range ports {
		portName := fmt.Sprintf("%s-%d", app.Name, port)
		if len(portName) > 15 {
			// 名称不能超过15个字符
			portName = fmt.Sprintf("%s-%d", app.Name[:10], port)
		}
		containerPorts = append(containerPorts, map[string]interface{}{
			"name":          portName,
			"containerPort": port,
			"protocol":      "TCP",
		})
	}

	// 构建环境变量
	envVars := []map[string]string{
		{"name": "TZ", "value": "Asia/Shanghai"},
		{"name": "_RESTART", "value": time.Now().Format("20060102150405")},
		{"name": "PRODUCT_NAME", "value": product.Name},
		{"name": "PROJECT_NAME", "value": project.Name},
		{"name": "APPNAME", "value": app.Name},
		{"name": "APP_ID", "value": app.AppCode},
		{"name": "ENV", "value": environment.Name},
		{"name": "POD_NAMESPACE", "value": namespace},
	}

	// 构建标签
	labels := map[string]string{
		"app":                              app.Name,
		"environment":                      environment.Name,
		"version":                          "v1",
		"status-app-name-for-ops-platform": app.Name,
	}

	// 构建卷挂载
	volumeMounts := []map[string]interface{}{
		{
			"name":      "logs",
			"mountPath": "/data/logs",
			"readOnly":  false,
		},
	}

	// 构建卷
	volumes := []map[string]interface{}{
		{
			"name": "logs",
			"hostPath": map[string]string{
				"path": fmt.Sprintf("/data/%s-applogs/%s/", strings.ToLower(environment.Name), project.Name),
			},
		},
	}

	// 构建Deployment配置
	deployment := map[string]interface{}{
		"apiVersion": "apps/v1",
		"kind":       "Deployment",
		"metadata": map[string]interface{}{
			"name":      app.Name,
			"namespace": namespace,
			"labels":    labels,
		},
		"spec": map[string]interface{}{
			"replicas": 1,
			"selector": map[string]interface{}{
				"matchLabels": map[string]string{
					"app": app.Name,
				},
			},
			"template": map[string]interface{}{
				"metadata": map[string]interface{}{
					"labels": labels,
					"annotations": map[string]string{
						"prometheus.io/app_product": product.Name,
						"prometheus.io/app_env":     environment.Name,
						"prometheus.io/app_project": project.Name,
					},
				},
				"spec": map[string]interface{}{
					"containers": []map[string]interface{}{
						{
							"name":  app.Name,
							"image": image,
							"ports": containerPorts,
							"env":   envVars,
							"resources": map[string]interface{}{
								"requests": map[string]string{
									"cpu":    "100m",
									"memory": "128Mi",
								},
								"limits": map[string]string{
									"cpu":    "500m",
									"memory": "512Mi",
								},
							},
							"volumeMounts": volumeMounts,
						},
					},
					"imagePullSecrets": []map[string]string{
						{"name": "loginharbor"},
					},
					"hostAliases": []map[string]interface{}{
						{
							"ip":        "************",
							"hostnames": []string{"harbor.fundpark.com", "source.fundpark.com"},
						},
						{
							"ip":        "************",
							"hostnames": []string{"devops.fundpark.com"},
						},
					},
					"terminationGracePeriodSeconds": 120,
					"volumes":                       volumes,
				},
			},
			"strategy": map[string]interface{}{
				"type": "RollingUpdate",
				"rollingUpdate": map[string]string{
					"maxSurge":       "25%",
					"maxUnavailable": "25%",
				},
			},
		},
	}

	// 构建Service配置
	var serviceConfig map[string]interface{}
	hasNodePort := false

	// 检查是否有NodePort配置
	portsJSON, _ = json.Marshal(appInfo.PortSettings.Data)
	if len(portsJSON) > 0 && string(portsJSON) != "null" {
		var portConfig map[string]interface{}
		if err := json.Unmarshal(portsJSON, &portConfig); err == nil {
			if portsArray, ok := portConfig["ports"].([]interface{}); ok {
				var servicePorts []map[string]interface{}
				for _, portData := range portsArray {
					if portMap, ok := portData.(map[string]interface{}); ok {
						port := int32(portMap["port"].(float64))
						protocol := "TCP"
						if proto, exists := portMap["protocol"]; exists {
							if strings.ToUpper(proto.(string)) == "UDP" {
								protocol = "UDP"
							}
						}

						portName := fmt.Sprintf("port-%d", port)
						if name, exists := portMap["name"]; exists {
							portName = name.(string)
						}

						servicePort := map[string]interface{}{
							"name":       portName,
							"port":       port,
							"targetPort": port,
							"protocol":   protocol,
						}

						// 检查是否有NodePort配置
						if nodePort, exists := portMap["node_port"]; exists {
							nodePortNum := int32(nodePort.(float64))
							if nodePortNum > 0 {
								servicePort["nodePort"] = nodePortNum
								hasNodePort = true
							}
						}

						servicePorts = append(servicePorts, servicePort)
					}
				}

				// 如果有服务端口，构建Service配置
				if len(servicePorts) > 0 {
					serviceType := "ClusterIP"
					if hasNodePort {
						serviceType = "NodePort"
					}

					serviceConfig = map[string]interface{}{
						"apiVersion": "v1",
						"kind":       "Service",
						"metadata": map[string]interface{}{
							"name":      app.Name,
							"namespace": namespace,
							"labels":    labels,
						},
						"spec": map[string]interface{}{
							"selector": map[string]string{
								"app": app.Name,
							},
							"ports": servicePorts,
							"type":  serviceType,
						},
					}
				}
			}
		}
	}

	// 如果没有构建Service配置，但有端口，创建默认Service配置
	if serviceConfig == nil && len(ports) > 0 {
		var servicePorts []map[string]interface{}
		for _, port := range ports {
			servicePorts = append(servicePorts, map[string]interface{}{
				"name":       fmt.Sprintf("port-%d", port),
				"port":       port,
				"targetPort": port,
				"protocol":   "TCP",
			})
		}

		serviceConfig = map[string]interface{}{
			"apiVersion": "v1",
			"kind":       "Service",
			"metadata": map[string]interface{}{
				"name":      app.Name,
				"namespace": namespace,
				"labels":    labels,
			},
			"spec": map[string]interface{}{
				"selector": map[string]string{
					"app": app.Name,
				},
				"ports": servicePorts,
				"type":  "ClusterIP",
			},
		}
	}

	// 生成YAML配置
	deploymentYAML, err := yaml.Marshal(deployment)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "生成Deployment YAML失败: " + err.Error(),
		})
		return
	}

	// 构建响应
	result := map[string]interface{}{
		"yaml":  string(deploymentYAML),
		"image": image,
	}

	// 如果有Service配置，生成Service YAML
	if serviceConfig != nil {
		serviceYAML, err := yaml.Marshal(serviceConfig)
		if err != nil {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "生成Service YAML失败: " + err.Error(),
			})
			return
		}
		result["service"] = string(serviceYAML)
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 20000,
		"data": result,
	})
}

// GetInheritTemplate 是 GetProjectConfigInheritTemplate 的别名
// @Tags Applications
// @Summary 获取项目配置的可继承模板
// @Description 获取项目配置的可继承模板，基于环境
// @Accept json
// @Produce json
// @Param environment query int true "环境ID"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Router /api/v1/cmdb/app/service/template/inherit [get]
func (c *ApplicationController) GetInheritTemplate(ctx *gin.Context) {
	c.GetProjectConfigInheritTemplate(ctx)
}
