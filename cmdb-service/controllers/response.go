package controllers

import (
	"net/http"

	"github.com/devops-microservices/cmdb-service/models"
	"github.com/gin-gonic/gin"
)

// ResponseHelper 响应助手
type ResponseHelper struct{}

// NewResponseHelper 创建响应助手
func NewResponseHelper() *ResponseHelper {
	return &ResponseHelper{}
}

// Success 成功响应
func (r *ResponseHelper) Success(ctx *gin.Context, data interface{}) {
	ctx.JSON(http.StatusOK, models.DTOResponse{
		Code:    models.CodeSuccess,
		Message: "success",
		Data:    data,
	})
}

// SuccessWithMessage 带消息的成功响应
func (r *ResponseHelper) SuccessWithMessage(ctx *gin.Context, message string, data interface{}) {
	ctx.JSON(http.StatusOK, models.DTOResponse{
		Code:    models.CodeSuccess,
		Message: message,
		Data:    data,
	})
}

// List 列表响应（统一格式）
func (r *ResponseHelper) List(ctx *gin.Context, data interface{}, total int64, page, pageSize int) {
	ctx.JSON(http.StatusOK, models.DTOListResponse{
		Code:     models.CodeSuccess,
		Message:  "success",
		Data:     map[string]interface{}{"items": data},
		Total:    total,
		Page:     page,
		PageSize: pageSize,
	})
}

// Error 错误响应
func (r *ResponseHelper) Error(ctx *gin.Context, code int, message string) {
	ctx.JSON(http.StatusOK, models.DTOResponse{
		Code:    code,
		Message: message,
	})
}

// BadRequest 400错误响应
func (r *ResponseHelper) BadRequest(ctx *gin.Context, message string) {
	r.Error(ctx, models.CodeBadRequest, message)
}

// NotFound 404错误响应
func (r *ResponseHelper) NotFound(ctx *gin.Context, message string) {
	r.Error(ctx, models.CodeNotFound, message)
}

// InternalError 500错误响应
func (r *ResponseHelper) InternalError(ctx *gin.Context, message string) {
	r.Error(ctx, models.CodeInternalError, message)
}

// ValidationError 验证错误响应
func (r *ResponseHelper) ValidationError(ctx *gin.Context, message string) {
	r.Error(ctx, models.CodeValidationError, message)
}

// Created 创建成功响应
func (r *ResponseHelper) Created(ctx *gin.Context, data interface{}) {
	ctx.JSON(http.StatusCreated, models.DTOResponse{
		Code:    models.CodeCreated,
		Message: "created successfully",
		Data:    data,
	})
}

// NoContent 无内容响应
func (r *ResponseHelper) NoContent(ctx *gin.Context) {
	ctx.JSON(http.StatusOK, models.DTOResponse{
		Code:    models.CodeSuccess,
		Message: "deleted successfully",
	})
}
