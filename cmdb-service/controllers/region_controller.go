package controllers

import (
	"net/http"
	"strconv"

	"github.com/devops-microservices/cmdb-service/models"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type RegionController struct {
	db *gorm.DB
}

func NewRegionController(db *gorm.DB) *RegionController {
	return &RegionController{db: db}
}

// GetRegions 获取区域列表
// @Tags Regions
// @Summary 获取区域列表
// @Description 获取所有区域的列表信息
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页大小" default(10)
// @Param search query string false "搜索关键字"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Router /api/v1/cmdb/region [get]
func (c *RegionController) GetRegions(ctx *gin.Context) {
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "10"))
	search := ctx.Query("search")

	var regions []models.Region
	query := c.db

	// 搜索条件
	if search != "" {
		query = query.Where("name LIKE ? OR desc LIKE ?", "%"+search+"%", "%"+search+"%")
	}

	// 计算总数
	var total int64
	query.Model(&models.Region{}).Count(&total)

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Find(&regions).Error; err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "查询区域失败",
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 20000,
		"data": gin.H{
			"total": total,
			"items": regions,
		},
	})
}

// GetRegion 获取单个区域
// @Tags Regions
// @Summary 获取单个区域
// @Description 根据ID获取区域详情
// @Accept json
// @Produce json
// @Param id path int true "区域ID"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Router /api/v1/cmdb/region/{id} [get]
func (c *RegionController) GetRegion(ctx *gin.Context) {
	id := ctx.Param("id")

	var region models.Region
	if err := c.db.First(&region, id).Error; err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{
			"code":    40400,
			"message": "区域不存在",
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 20000,
		"data": region,
	})
}

// GetRegionProducts 获取区域下的产品列表
// @Tags Regions
// @Summary 获取区域下的产品列表
// @Description 根据区域ID获取该区域下的所有产品
// @Accept json
// @Produce json
// @Param id path int true "区域ID"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页大小" default(10)
// @Param search query string false "搜索关键字"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Router /api/v1/cmdb/region/product [get]
func (c *RegionController) GetRegionProducts(ctx *gin.Context) {
	regionID := ctx.Query("region_id")
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "10"))
	search := ctx.Query("search")

	// 验证区域是否存在
	if regionID != "" {
		var region models.Region
		if err := c.db.First(&region, regionID).Error; err != nil {
			ctx.JSON(http.StatusNotFound, gin.H{
				"code":    40400,
				"message": "区域不存在",
			})
			return
		}
	}

	var products []models.Product
	query := c.db.Model(&models.Product{})

	// 添加区域过滤
	if regionID != "" {
		query = query.Where("region_id = ?", regionID)
	}

	// 添加搜索条件
	if search != "" {
		query = query.Where("name LIKE ? OR alias LIKE ? OR desc LIKE ?",
			"%"+search+"%", "%"+search+"%", "%"+search+"%")
	}

	// 计算总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "查询产品总数失败: " + err.Error(),
		})
		return
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Find(&products).Error; err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "查询产品列表失败: " + err.Error(),
		})
		return
	}

	// 为每个产品加载区域信息
	for i := range products {
		if products[i].RegionID != nil {
			var region models.Region
			if err := c.db.First(&region, *products[i].RegionID).Error; err == nil {
				products[i].Region = &region
			}
		}
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 20000,
		"data": gin.H{
			"total": total,
			"items": products,
		},
	})
}

// CreateRegion 创建区域
// @Tags Regions
// @Summary 创建区域
// @Description 创建新的区域
// @Accept json
// @Produce json
// @Param region body models.Region true "区域信息"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Router /api/v1/cmdb/region [post]
func (c *RegionController) CreateRegion(ctx *gin.Context) {
	var region models.Region
	if err := ctx.ShouldBindJSON(&region); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    40000,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	if err := c.db.Create(&region).Error; err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "创建区域失败",
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 20000,
		"data": region,
	})
}

// UpdateRegion 更新区域
// @Tags Regions
// @Summary 更新区域
// @Description 更新区域信息
// @Accept json
// @Produce json
// @Param id path int true "区域ID"
// @Param region body models.Region true "区域信息"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Router /api/v1/cmdb/region/{id} [put]
func (c *RegionController) UpdateRegion(ctx *gin.Context) {
	id := ctx.Param("id")

	var region models.Region
	if err := c.db.First(&region, id).Error; err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{
			"code":    40400,
			"message": "区域不存在",
		})
		return
	}

	if err := ctx.ShouldBindJSON(&region); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    40000,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	if err := c.db.Save(&region).Error; err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "更新区域失败",
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 20000,
		"data": region,
	})
}

// DeleteRegion 删除区域
// @Tags Regions
// @Summary 删除区域
// @Description 删除区域
// @Accept json
// @Produce json
// @Param id path int true "区域ID"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Router /api/v1/cmdb/region/{id} [delete]
func (c *RegionController) DeleteRegion(ctx *gin.Context) {
	id := ctx.Param("id")

	if err := c.db.Delete(&models.Region{}, id).Error; err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "删除区域失败",
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    20000,
		"message": "删除成功",
	})
}
