import datetime
import simplej<PERSON> as json
import re
import time
import traceback
import logging
import MySQLdb
import sqlparse
from pymysql.converters import escape_string
from django.shortcuts import render
from django.core.cache import cache
# from common.extends.cache import cache
from common.exception import OkAPIException
from common.extends.decorators import can_query_database
from common.extends.filters import CustomSearchFilter
from common.extends.permissions import RbacPer<PERSON>
from common.get_ip import user_ip
# from qtasks.tasks_sql import add_kill_conn_schedule, del_schedule
from celery_tasks.tasks_sql import add_kill_conn_schedule, sql_execute_task
from common.ext_fun import get_redis_data
from common.timer import FuncTimer
from common.variables import SQL_QUERY_RESULT_KEY
from sql.engines import get_engine
from common.sql.sql_tuning import SqlTuning
from common.sql.plugins.soar import Soar
# Create your views here.
from rest_framework import permissions
from rest_framework.decorators import action
from rest_framework.response import Response

from django.http import StreamingHttpResponse
import django_filters
import django_excel as excel
from django.db import connection, close_old_connections
# from django_q.tasks import async_task
from dbapp.model.model_workflow import Workflow, WorkflowTemplate
from dbapp.model.model_sql import DBInstance, DataMaskingColumns, QueryPrivileges, SqlQueryAdvisor, SqlQueryLog, SqlWorkflow, SqlWorkflowResult
from sql.serializers import DBInstanceListSerializer, DBInstanceSerializer, DataMaskingColumnsSerializer, ExecuteCheckResultSerializer, SqlQueryLogSerializer, SqlWorkflowListSerializer, SqlWorkflowRetrieveSerializer, SqlWorkflowSerializer

from common.extends.viewsets import AutoModelViewSet
from dbapp.model.model_ucenter import AuditLog
from workflow.ext_func import create_workflow
from workflow.lifecycle import LifeCycle
from workflow.views.workflow import IS_NOTICE_ASYNC

logger = logging.getLogger('drf')


class DBInstanceViewSet(AutoModelViewSet):
    """
    数据库实例视图
    ### 权限
        {'*': ('dbinstance_all', '数据库实例管理')},
        {'get': ('dbinstance_list', '查看数据库实例')},
        {'post': ('dbinstance_create', '创建数据库实例')},
        {'put': ('dbinstance_edit', '编辑数据库实例')},
        {'patch': ('dbinstance_edit', '编辑数据库实例')},
        {'delete': ('dbinstance_delete', '删除数据库实例')}
    """
    perms_map = (
        {'*': ('admin', '管理员')},
        {'*': ('dbinstance_all', '数据库实例管理')},
        {'get': ('dbinstance_list', '查看数据库实例')},
        {'post': ('dbinstance_create', '创建数据库实例')},
        {'put': ('dbinstance_edit', '编辑数据库实例')},
        {'patch': ('dbinstance_edit', '编辑数据库实例')},
        {'delete': ('dbinstance_delete', '删除数据库实例')}
    )
    queryset = DBInstance.objects.filter(is_deleted=0)
    serializer_class = DBInstanceSerializer
    serializer_list_class = DBInstanceListSerializer
    # permission_classes = [permissions.AllowAny]
    filter_backends = (
        django_filters.rest_framework.DjangoFilterBackend, CustomSearchFilter)
    filter_fields = ('db_type', 'mode', 'is_deleted')
    search_fields = ('instance_name', 'alias', 'host', 'db_name')

    def query(self, request, pk, sql_content):
        instance = self.queryset.get(pk=pk)
        click_action = request.data.get('click', None)
        db_name = request.data.get('db_name', None)
        tb_name = request.data.get('tb_name', None)
        col_order = request.data.get('order', 'DESC')
        order_column = request.data.get('order_column', None)
        columns = request.data.get('columns', None)
        limit_num = request.data.get('limit_num', 100)
        offset = request.data.get('offset', 0)
        schema_name = request.data.get('schema_name', None)
        # if instance.db_type == 'pgsql':
        #     # pgsql数据库，模式和数据库名组合一起
        #     # 在这通过>分隔成shcema_name和db_name
        #     db_name, schema_name = db_name.split(' > ')
        result = {"status": 0, "msg": "ok", "data": {}}
        try:
            config = get_redis_data('sqlquery') or {}
            # pgsql类型
            if click_action:
                if instance.db_type == 'mongo':
                    # mongodb
                    sql_content = f'''db.{tb_name}.find().
                    skip({offset}).limit({limit_num}).
                    sort({{"{order_column}": {-1 if col_order == "DESC" else 1}}})'''
                else:
                    columns_selected = '`' + ('`,`').join(columns) + '`'
                    if instance.db_type == 'pgsql':
                        columns_selected = '"' + ('","').join(columns) + '"'
                        # sql_content = sql_content.replace('`', '"')
                    sql_content = f'''select {columns_selected} from {tb_name}
                        order by {order_column} {col_order}
                        limit {limit_num} offset {offset};'''
            # 查询前的检查，禁用语句检查，语句切分
            query_engine = get_engine(instance=instance)
            query_check_info = query_engine.query_check(
                db_name=db_name, sql=sql_content)
            if query_check_info.get("bad_query"):
                # 引擎内部判断为 bad_query
                result["status"] = 1
                result["msg"] = query_check_info.get("msg")
                return False, result
            if query_check_info.get("has_star") and config.get("disable_star", False):
                # 引擎内部判断为有 * 且禁止 * 选项打开
                result["status"] = 1
                result["msg"] = query_check_info.get("msg")
                return False, result
            sql_content = query_check_info["filtered_sql"]
            # explain的limit_num设置为0
            limit_num = 0 if re.match(
                r"^explain", sql_content.lower()) else limit_num
            # 对查询sql增加limit限制或者改写语句
            if not click_action:
                sql_content = query_engine.filter_sql(
                    sql=sql_content, limit_num=limit_num)
            # 先获取查询连接，用于后面查询复用连接以及终止会话
            query_engine.get_connection(db_name=db_name)
            thread_id = query_engine.thread_id
            max_execution_time = int(config.get("max_execution_time", 60))
            # 执行查询语句，并增加一个定时终止语句的schedule，timeout=max_execution_time
            if thread_id:
                # schedule_name = f"query-{time.time()}"
                run_date = datetime.datetime.now() + datetime.timedelta(
                    seconds=max_execution_time
                )
                # add_kill_conn_schedule(
                #     schedule_name, run_date, instance.id, thread_id)
                add_kill_conn_schedule.apply_async(
                    (instance.id, thread_id), eta=run_date)
            with FuncTimer() as t:
                # 获取主从延迟信息
                seconds_behind_master = query_engine.seconds_behind_master
                query_result = query_engine.query(
                    db_name,
                    sql_content,
                    limit_num,
                    schema_name=schema_name,
                    tb_name=tb_name,
                    max_execution_time=max_execution_time * 1000,
                )
            query_result.query_time = t.cost
            # # 返回查询结果后删除schedule
            # if thread_id:
            #     del_schedule(schedule_name)
            hit_rule = False
            masking = False
            # 查询异常
            if query_result.error:
                result["status"] = 1
                result["msg"] = query_result.error
            # 数据脱敏，仅对查询无错误的结果集进行脱敏，并且按照query_check配置是否返回
            elif config.get('data_masking', False):
                try:
                    with FuncTimer() as t:
                        masking_result = query_engine.query_masking(
                            db_name, sql_content, query_result
                        )
                    masking_result.mask_time = t.cost
                    hit_rule = True
                    # 脱敏出错
                    if masking_result.error:
                        # 开启query_check，直接返回异常，禁止执行
                        if config.get('query_check', False):
                            result["status"] = 1
                            result["msg"] = f"数据脱敏异常：{masking_result.error}"
                            return False, result
                        # 关闭query_check，忽略错误信息，返回未脱敏数据，权限校验标记为跳过
                        else:
                            print(
                                f"数据脱敏异常，按照配置放行，查询语句：{sql_content}，错误信息：{masking_result.error}"
                            )
                            query_result.error = None
                            result["data"] = query_result.__dict__
                    # 正常脱敏
                    else:
                        masking = True
                        result["data"] = masking_result.__dict__
                except Exception as msg:
                    print(traceback.format_exc())
                    # 抛出未定义异常，并且开启query_check，直接返回异常，禁止执行
                    if config.get('query_check', False):
                        result["status"] = 1
                        result["msg"] = f"数据脱敏异常，请联系管理员，错误信息：{msg}"
                        return False, result
                    # 关闭query_check，忽略错误信息，返回未脱敏数据，权限校验标记为跳过
                    else:
                        print(f"数据脱敏异常，按照配置放行，查询语句：{sql_content}，错误信息：{msg}")
                        query_result.error = None
                        result["data"] = query_result.__dict__
            # 无需脱敏的语句
            else:
                result["data"] = query_result.__dict__
            # 仅将成功的查询语句记录存入数据库
            if not query_result.error:
                # 转换long int为str
                rows = [[str(j) if isinstance(j, (int, )) else j for j in i]
                        for i in query_result.rows]
                result['data']['rows'] = rows
                result['data']['seconds_behind_master'] = seconds_behind_master
                if int(limit_num) == 0:
                    limit_num = int(query_result.affected_rows)
                else:
                    limit_num = min(int(limit_num), int(
                        query_result.affected_rows))
                # 存储查询结果
                # result['table'] = tb_name
                # 记录查询日志
                querylog = SqlQueryLog.objects.create(instance=instance, db_name=db_name, schema_name=schema_name, sqllog=sql_content,
                                                      effect_row=limit_num, cost_time=query_result.query_time, deployer=request.user, hit_rule=hit_rule, masking=masking)
                cache.set(
                    f"{SQL_QUERY_RESULT_KEY}:{request.user.id}:{querylog.id}", result, 60 * 60 * 0.5)
                result['id'] = querylog.id
                # 防止查询超时
                if connection.connection and not connection.is_usable():
                    close_old_connections()
                # query_log.save()
        except Exception as e:
            # logger.error(f"查询异常报错，查询语句：{sql_content}\n，错误信息：{traceback.format_exc()}")
            print(f"查询异常报错，查询语句：{sql_content}\n，错误信息：{traceback.format_exc()}")
            result["status"] = 1
            result["msg"] = f"查询异常报错，错误信息：{e}"
            return False, result
        # 返回查询结果
        return True, result

    @action(methods=['GET'], url_path='user/instances', detail=False)
    def user_instances(self, request):
        """
        获取用户有权限的数据库实例
        """
        is_admin = RbacPermission.check_is_admin(request)
        if is_admin or request.user.is_superuser:
            # 管理员
            data = []
            for i in self.queryset.filter(is_deleted=0):
                query_engine = get_engine(instance=i)
                try:
                    databases = query_engine.get_all_databases()

                    # if i.db_type == 'pgsql':
                    #     # pgsql数据库，模式和数据库名通过#组合一起返回到前端
                    #     _data = []
                    #     for j in databases.rows:
                    #         resource = query_engine.get_all_schemas(db_name=j)
                    #         _data.extend([f'{j} > {x}' for x in resource.rows])
                    #     databases = _data
                    # else:
                    databases = databases.rows
                except BaseException as e:
                    logger.info(f'获取数据库失败，{e}')
                    databases = []
                data.append(
                    {'instance_id': i.id, 'instance_name': i.instance_name, 'instance_alias': i.alias, 'db_type': i.db_type, 'databases': databases})
            return Response({'code': 20000, 'data': {'items': data}})

        qs = QueryPrivileges.objects.filter(
            username=request.user.username, is_deleted=0, valid_date__gte=datetime.datetime.now())
        data = [{'instance_id': i.instance_id, 'instance_name': i.instance.instance_name, 'instance_alias': i.instance.alias,
                 'db_name': i.db_name, 'db_type': i.instance.db_type} for i in qs if i.instance.is_deleted == 0]
        ret = {}
        for i in data:
            if i['instance_id'] not in ret:
                ret[i['instance_id']] = [i['db_name']]
                continue
            ret[i['instance_id']].append(i['db_name'])
        for i in data:
            i['databases'] = list(set(ret[i['instance_id']]))
        data = list({i['instance_id']: i for i in data}.values())
        return Response({'code': 20000, 'data': {'items': data}})

    @action(methods=['GET'], url_path='table/info', detail=True)
    def table_info(self, request, pk=None):
        """
        获取表元信息，包括表、列、索引、建表
        """
        instance = self.get_object()
        db_name = request.query_params.get('db_name', None)
        schema_name = request.query_params.get('schema_name', None)
        tb_name = request.query_params.get('tb_name', None)
        # if instance.db_type == 'pgsql':
        #     # pgsql数据库，模式和数据库名组合一起
        #     # 在这通过>分隔成shcema_name和db_name
        #     db_name, schema_name = db_name.split(' > ')
        data = {}
        try:
            query_engine = get_engine(instance=instance)
            data["meta_data"] = query_engine.get_table_meta_data(
                db_name=db_name, tb_name=tb_name
            )
            data["desc"] = query_engine.get_table_desc_data(
                db_name=db_name, tb_name=tb_name, schema_name=schema_name
            )
            data["index"] = query_engine.get_table_index_data(
                db_name=db_name, tb_name=tb_name
            )
            # mysql数据库可以获取创建表格的SQL语句，mssql暂无找到生成创建表格的SQL语句
            if instance.db_type == "mysql":
                _create_sql = query_engine.query(
                    db_name, "show create table `%s`;" % tb_name
                )
                data["create_sql"] = _create_sql.rows
            if instance.db_type == 'pgsql':
                _create_sql = query_engine.get_table_create(
                    db_name, tb_name, schema_name=schema_name)
                data['create_sql'] = [[tb_name, _create_sql.rows[0][0]]]
                data_total = query_engine.query(db_name, f'''SELECT n_live_tup AS Count
FROM pg_stat_user_tables where schemaname='{schema_name}' and relname='{tb_name}';''')
                data['meta_data'] = {
                    'rows': [tb_name, '', '', data_total.rows[0][0]]}
            if instance.db_type == 'mongo':
                print('获取mongo表行数')
                data_total = query_engine.exec_py(
                    tb_name=tb_name, db_name=db_name, func='count_documents')
                print('获取mongo表行数m0ongo db total', data_total)
                data['meta_data'] = {'rows': [tb_name, '', '', data_total]}
            res = {"status": 0, "data": data}
            code = 20000
        except Exception as e:
            res = {"status": 1, "msg": str(e)}
            code = 50000
        AuditLog.objects.create(user=request.user, type='SQL', action='查看表元信息',
                                action_ip=user_ip(request),
                                content=f'请求方法：{request.method}，请求路径：{request.path}，UserAgent：{request.META["HTTP_USER_AGENT"]}',
                                data=f'实例：{instance.instance_name}，数据库：{db_name}，表：{tb_name}\n',
                                old_data='')
        return Response({'code': code, 'data': res})

    @action(methods=['GET'], url_path='describe', detail=True)
    def table_desc(self, request, pk=None):
        """
        表结构
        """
        instance = self.get_object()
        db_name = request.query_params.get('db_name', None)
        schema_name = request.query_params.get('schema_name', None)
        tb_name = request.query_params.get('tb_name', None)
        # if instance.db_type == 'pgsql':
        #     # pgsql数据库，模式和数据库名组合一起
        #     # 在这通过>分隔成shcema_name和db_name
        #     db_name, schema_name = db_name.split(' > ')
        result = {"status": 0, "msg": "ok", "data": []}
        try:
            query_engine = get_engine(instance=instance)
            query_result = query_engine.describe_table(
                db_name, tb_name, schema_name=schema_name
            )
            result["data"] = query_result.__dict__
        except Exception as msg:
            result["status"] = 1
            result["msg"] = str(msg)
        if result["data"]["error"]:
            result["status"] = 1
            result["msg"] = result["data"]["error"]
        return Response({'code': 20000, 'data': result})

    @action(methods=['GET'], url_path='resource', detail=True)
    def instance_resource(self, request, pk=None):
        """
        获取实例内的资源信息，database、schema、table、column
        :param request:
        :return:
        """
        instance = self.get_object()
        from_workflow_apply = request.query_params.get(
            'from_workflow_apply', None)
        from_workbench_query = request.query_params.get(
            'from_workbench_query', None)
        db_name = request.query_params.get('db_name', None)
        schema_name = request.query_params.get('schema_name', None)
        tb_name = request.query_params.get('tb_name', None)
        resource_type = request.query_params.get('resource_type', 'database')
        # if db_name and instance.db_type == 'pgsql':
        #     # pgsql数据库，模式和数据库名组合一起
        #     # 在这通过>分隔成shcema_name和db_name
        #     db_name, schema_name = db_name.split(' > ')
        result = {"status": 0, "msg": "ok", "data": []}

        try:
            is_admin = RbacPermission.check_is_admin(request)
            query_engine = get_engine(instance=instance)
            if resource_type == "database":
                resource = query_engine.get_all_databases()
                if resource.error:
                    return Response({'code': 50000, 'message': resource.error})
                if instance.db_type == 'pgsql' and from_workflow_apply:
                    # pgsql数据库，前端发起数据库查询申请工单
                    # 数据库名和模式通过>组合一起返回到前端
                    _data = []
                    for j in resource.rows:
                        res = query_engine.get_all_schemas(db_name=j)
                        _data.extend([f'{j}>{x}' for x in res.rows])
                    data = _data
                    return Response({'code': 20000, 'data': {'items': data}})
                if all([from_workbench_query, not is_admin, not request.user.is_superuser]):
                    # 非管理员角色，SQL工作台返回用户有权限的数据库
                    qs = QueryPrivileges.objects.filter(username=request.user.username, is_deleted=0, valid_date__gte=datetime.datetime.now(),
                                                        instance_id=instance.id)
                    data = list(
                        set([i.db_name for i in qs if i.db_name in resource.rows]))
                    return Response({'code': 20000, 'data': {'items': data}})
                return Response({'code': 20000, 'data': {'items': resource.rows}})
            if resource_type == 'schema' and db_name:
                resource = query_engine.get_all_schemas(db_name=db_name)
                if all([from_workbench_query, not is_admin, not request.user.is_superuser]):
                    # 非管理员角色，SQL工作台返回用户有权限的模式
                    qs = QueryPrivileges.objects.filter(username=request.user.username, is_deleted=0, valid_date__gte=datetime.datetime.now(),
                                                        instance_id=instance.id)
                    data = list(
                        set([i.schema_name for i in qs if i.schema_name in resource.rows]))
                    return Response({'code': 20000, 'data': {'items': data}})
                return Response({'code': 20000, 'data': {'items': resource.rows}})
            if resource_type == "table" and db_name:
                resource = query_engine.get_all_tables(
                    db_name=db_name, schema_name=schema_name
                )
                if resource.error:
                    return Response({'code': 50000, 'message': resource.error})
                return Response({'code': 20000, 'data': {'items': resource.rows}})
            if resource_type == 'column' and db_name and tb_name:
                resource = query_engine.get_all_columns_by_tb(
                    db_name=db_name, tb_name=tb_name, schema_name=schema_name)
                if resource.error:
                    return Response({'code': 50000, 'message': resource.error})
                return Response({'code': 20000, 'data': {'items': resource.rows}})
            # # escape
            # db_name = MySQLdb.escape_string(db_name).decode("utf-8")
            # schema_name = MySQLdb.escape_string(schema_name).decode("utf-8")
            # tb_name = MySQLdb.escape_string(tb_name).decode("utf-8")

            # query_engine = get_engine(instance=instance)
            # if resource_type == "database":
            #     resource = query_engine.get_all_databases()
            # elif resource_type == "schema" and db_name:
            #     resource = query_engine.get_all_schemas(db_name=db_name)
            # else:
            return Response({'code': 40300, 'message': '不支持的资源类型或者参数不完整！'})
        except Exception as msg:
            result["status"] = 1
            result["msg"] = str(msg)
            return Response({'code': 50000, 'message': str(msg)})
        # else:
        #     if resource.error:
        #         result["status"] = 1
        #         result["msg"] = resource.error
        #     else:
        #         result["data"] = resource.rows

    @action(methods=['POST'], url_path='query', detail=True)
    @can_query_database()
    def sql_query(self, request, pk=None):
        """
        SQL查询
        """
        # result = {"status": 0, "msg": "ok", "data": {}}
        sql_content = request.data.get('sql_content', None)
        ok, result = self.query(request, pk, sql_content)
        if ok:
            return Response({'code': 20000, 'data': result})
        return Response({'code': 50000, 'message': result})

    @action(methods=['POST'], url_path='explain', detail=True)
    @can_query_database()
    def sql_explain(self, request, pk=None):
        """
        SQL执行计划
        """
        sql_content = request.data.get('sql_content', None)
        if not sql_content.startswith('explain'):
            sql_content = f'explain {sql_content}'
        ok, result = self.query(request, pk, sql_content)
        if ok:
            return Response({'code': 20000, 'data': result})
        return Response({'code': 50000, 'message': result})

    @action(methods=['POST'], url_path='soar', detail=True)
    @can_query_database()
    def optimize_soar(self, request, pk=None):
        instance = self.queryset.get(pk=pk)
        db_name = request.data.get('db_name', None)
        schema_name = request.data.get('schema_name', None)
        sql = request.data.get('sql_content', None)
        result = {"status": 0, "msg": "ok", "data": []}

        # 检查测试实例的连接信息和soar程序路径
        config = get_redis_data('sqladvisor') or {}
        soar_path = config.get('soar_path', None)
        if not soar_path:
            result["status"] = 1
            result["msg"] = "请配置soar可执行文件路径！"
            return Response({'code': 50000, 'message': result})

        # 目标实例的连接信息
        online_dsn = (
            f"{instance.user}:{instance.password}@{instance.host}:{instance.port}/{db_name}"
        )

        # 提交给soar获取分析报告
        soar = Soar()
        # 准备参数
        args = {
            "online-dsn": online_dsn,
            # "test-dsn": soar_test_dsn,
            "allow-online-as-test": False,
            "report-type": "markdown",
            "query": sql.strip(),
        }
        # 参数检查
        args_check_result = soar.check_args(args)
        if args_check_result["status"] == 1:
            return Response({'code': 50000, 'message': args_check_result})
        # 参数转换
        cmd_args = soar.generate_args2cmd(args)
        # 执行命令
        try:
            stdout, stderr = soar.execute_cmd(cmd_args).communicate()
            result["data"] = stdout if stdout else stderr
        except RuntimeError as e:
            result["status"] = 1
            result["msg"] = str(e)
        SqlQueryAdvisor.objects.create(
            instance=instance, db_name=db_name, schema_name=schema_name, sqllog=sql, deployer=request.user, tool='soar', result=result)
        return Response({'code': 20000, 'data': result})

    @action(methods=['POST'], url_path='sqltuning', detail=True)
    @can_query_database()
    def optimize_sqltuning(self, request, pk=None):
        """
        SQL优化
        """
        instance = self.queryset.get(pk=pk)
        db_name = request.data.get('db_name', None)
        schema_name = request.data.get('schema_name', None)
        option = request.data.get('option', {})
        sqltext = request.data.get('sql_content', None)
        print('打印option', option)
        sqltext = sqlparse.format(sqltext, strip_comments=True)
        sqltext = sqlparse.split(sqltext)[0]

        if re.match(r"^select|^show|^explain", sqltext, re.I) is None:
            result = {"status": 1, "msg": "只支持查询SQL！", "data": []}
            return Response({'code': 40300, 'message': result})

        # escape
        db_name = escape_string(db_name).decode("utf-8")

        sql_tunning = SqlTuning(
            instance_name=instance.instance_name, db_name=db_name, sqltext=sqltext
        )
        result = {"status": 0, "msg": "ok", "data": {}}
        if option.get('sys_param', False):
            basic_information = sql_tunning.basic_information()
            sys_parameter = sql_tunning.sys_parameter()
            optimizer_switch = sql_tunning.optimizer_switch()
            result["data"]["basic_information"] = basic_information
            result["data"]["sys_parameter"] = sys_parameter
            result["data"]["optimizer_switch"] = optimizer_switch
        if option.get('sql_plan', False):
            plan, optimizer_rewrite_sql = sql_tunning.sqlplan()
            result["data"]["optimizer_rewrite_sql"] = optimizer_rewrite_sql
            result["data"]["plan"] = plan
        if option.get('obj_stat', False):
            result["data"]["object_statistics"] = sql_tunning.object_statistics()
        # if "sql_profile" in option:
        #     session_status = sql_tunning.exec_sql()
        #     result["data"]["session_status"] = session_status
        # 关闭连接
        sql_tunning.engine.close()
        result["data"]["sqltext"] = sqltext
        result['data'] = json.dumps(result['data'], use_decimal=True)
        SqlQueryAdvisor.objects.create(
            instance=instance, db_name=db_name, schema_name=schema_name, sqllog=sqltext, deployer=request.user, tool='sqltuning', result=result)
        return Response({'code': 20000, 'data': result})

    @action(methods=['POST'], url_path='check', detail=True)
    def sql_check(self, request, pk=None):
        """
        SQL语句检测
        """
        instance = self.get_object()
        db_name = request.data.get('db_name', None)
        full_sql = request.data.get('full_sql', None)
        recheck = request.data.get('recheck', None)
        workflow_id = request.data.get('workflow_id', None)
        # 交给engine进行检测
        try:
            check_engine = get_engine(instance=instance)
            check_result = check_engine.execute_check(
                db_name=db_name, sql=full_sql.strip()
            )
        except Exception as e:
            return Response({'code': 50000, 'message': str(e)})
        check_result.rows = check_result.to_dict()
        serializer_obj = ExecuteCheckResultSerializer(check_result)
        if recheck and workflow_id:
            # 执行页面重新检测语句
            SqlWorkflowResult.objects.update_or_create(sqlworkflow_id=workflow_id, defaults={
                'sqlworkflow_id': workflow_id,
                'review_content': serializer_obj.data
            })
        return Response({'code': 20000, 'data': serializer_obj.data})

    @action(methods=['GET'], url_path='download', detail=False)
    def sql_query_download(self, request):
        """
        SQL查询结果下载
        """
        querylog_id = request.query_params.get('log_id', None)
        file_type = request.query_params.get('file_type', 'xlsx')
        result = cache.get(
            f"{SQL_QUERY_RESULT_KEY}:{request.user.id}:{querylog_id}")
        data = list(result['data']['rows'])
        select_fields = result['data']['column_list']

        # 记录下载日志
        AuditLog.objects.create(user=request.user, type='SQL', action='下载',
                                action_ip=user_ip(request),
                                content=f'查询日志ID: {querylog_id}\n请求方法：{request.method}，请求路径：{request.path}，UserAgent：{request.META["HTTP_USER_AGENT"]}',
                                data=f'查询日志ID: {querylog_id}\n',
                                old_data='')

        content = ''
        if file_type.lower() == 'sql':
            # 导出sql文件
            col = ('`, `').join(select_fields)
            for i in data:
                i = list(i)
                for index, j in enumerate(i):
                    if isinstance(j, (datetime.datetime, )):
                        i[index] = j.strftime('%Y-%m-%d %H:%M:%S')
                content += f'INSERT INTO `TableName` (`{col}`) VALUES {tuple(i)};\n'
            response = StreamingHttpResponse(content, 512)
            response['Content-Type'] = 'application/octet-stream'
            response['Content-Disposition'] = 'attachment; filename=SQL查询结果.sql'
            return response
        data.insert(0, select_fields)
        return excel.make_response_from_array(data, file_type=file_type, file_name=f"SQL查询结果.xlsx")

    def perform_destroy(self, instance):
        instance.is_deleted = 1
        instance.save()


class SqlQueryLogViewSet(AutoModelViewSet):
    """
    SQL查询日志视图
        ### 权限
        {'*': ('sqllog_all', 'SQL查询日志管理')},
        {'get': ('sqllog_list', '查看SQL查询日志')},
        {'post': ('sqllog_create', '创建SQL查询日志')},
        {'put': ('sqllog_edit', '编辑SQL查询日志')},
        {'patch': ('sqllog_edit', '编辑SQL查询日志')},
        {'delete': ('sqllog_delete', '删除SQL查询日志')}
    """
    perms_map = (
        {'*': ('admin', '管理员')},
        {'*': ('sqllog_all', 'SQL查询日志管理')},
        {'get': ('sqllog_list', '查看SQL查询日志')},
        {'post': ('sqllog_create', '创建SQL查询日志')},
        {'put': ('sqllog_edit', '编辑SQL查询日志')},
        {'patch': ('sqllog_edit', '编辑SQL查询日志')},
        {'delete': ('sqllog_delete', '删除SQL查询日志')}
    )
    queryset = SqlQueryLog.objects.all()
    serializer_class = SqlQueryLogSerializer
    filter_backends = (
        django_filters.rest_framework.DjangoFilterBackend, CustomSearchFilter)
    # filterset_fields = {
    #     'deployer__username': ['exact'],
    #     'deployer__first_name': ['exact'],
    #     'instance__instance_name': ['exact'],
    #     'instance__alias': ['exact'],
    # }
    filter_fields = ('instance', 'db_name',)
    search_fields = ('db_name', 'sqllog', 'deployer__username',
                     'deployer__first_name', 'instance__instance_name')


class DataMaskingColumnsViewSet(AutoModelViewSet):
    """
    脱敏字段视图
        ### 权限
        {'*': ('datamask_all', '脱敏字段管理')},
        {'get': ('datamask_list', '查看脱敏字段')},
        {'post': ('datamask_create', '创建脱敏字段')},
        {'put': ('datamask_edit', '编辑脱敏字段')},
        {'patch': ('datamask_edit', '编辑脱敏字段')},
        {'delete': ('datamask_delete', '删除脱敏字段')}
    """
    perms_map = (
        {'*': ('admin', '管理员')},
        {'*': ('datamask_all', '脱敏字段管理')},
        {'get': ('datamask_list', '查看脱敏字段')},
        {'post': ('datamask_create', '创建脱敏字段')},
        {'put': ('datamask_edit', '编辑脱敏字段')},
        {'patch': ('datamask_edit', '编辑脱敏字段')},
        {'delete': ('datamask_delete', '删除脱敏字段')}
    )

    queryset = DataMaskingColumns.objects.all()
    serializer_class = DataMaskingColumnsSerializer
    filter_backends = (
        django_filters.rest_framework.DjangoFilterBackend, CustomSearchFilter)
    filter_fields = ('instance', 'active', 'table_schema', 'table_name')
    search_fields = ('table_schema', 'table_name')


class SqlWorkflowViewSet(AutoModelViewSet):
    """
    数据库上线工单

    ### 权限
        {'*': ('sqlworkflow_all', '数据库工单管理')},
        {'get': ('sqlworkflow_list', '查看数据库工单')},
        {'post': ('sqlworkflow_create', '创建数据库工单')},
        {'put': ('sqlworkflow_edit', '编辑数据库工单')},
        {'patch': ('sqlworkflow_edit', '编辑数据库工单')},
        {'delete': ('sqlworkflow_delete', '删除数据库工单')}
    """
    perms_map = (
        {'*': ('admin', '管理员')},
        {'*': ('sqlworkflow_all', '数据库工单管理')},
        {'get': ('sqlworkflow_list', '查看数据库工单')},
        {'post': ('sqlworkflow_create', '创建数据库工单')},
        {'put': ('sqlworkflow_edit', '编辑数据库工单')},
        {'patch': ('sqlworkflow_edit', '编辑数据库工单')},
        {'delete': ('sqlworkflow_delete', '删除数据库工单')}
    )
    queryset = SqlWorkflow.objects.all()
    serializer_class = SqlWorkflowSerializer
    serializer_list_class = SqlWorkflowListSerializer
    serializer_upcoming_class = SqlWorkflowListSerializer
    serializer_retrieve_class = SqlWorkflowRetrieveSerializer
    # serializer_detail_class = SqlWorkflowRetrieveSerializer
    filterset_fields = {
        'created_time': ['gte', 'lte'],
        'status': ['exact', 'in'],
        'instance__instance_name': ['icontains'],
        'instance__alias': ['icontains']
    }
    filter_fields = ('syntax_type', 'status', 'method',)
    search_fields = ('order_id', 'title', 'engineer', 'engineer_display',
                     'status', 'db_name', 'instance__alias', 'instance__instance_name')

    def extend_filter(self, queryset):
        if self.action == 'upcoming':
            queryset = queryset.filter(
                status__in=['workflow_review_pass', 'workflow_exception'])
        if self.request.user.is_superuser or 'admin' in self.get_permission_from_role(
                self.request):
            return queryset
        queryset = queryset.filter(engineer=self.request.user.username)
        return queryset

    @action(methods=['GET'], url_path='detail', detail=False)
    def get_detail(self, request, *args, **kwargs):
        """
        获取工单详情
        """
        print('查看参数', request.query_params)
        order_id = request.query_params.get('order_id', None)
        instance = self.queryset.filter(order_id=order_id).first()
        serializer = SqlWorkflowRetrieveSerializer(instance)
        return Response(serializer.data)

    @action(methods=['POST'], url_path='execute', detail=True)
    def sql_execute(self, request, pk=None):
        """
        执行SQL
        """
        # TODO: 执行前重新检测SQL语句
        mode = request.data.get('mode', 'auto')
        # 交由系统执行
        # if mode == "auto":
        # 修改工单状态为排队中
        wf = self.get_object()
        # 只有排队中和定时执行的数据才可以继续执行，否则直接抛错
        if wf.status in ["workflow_finish"]:
            return Response({'code': 40300, 'message': '工单已完成，禁止执行！'})
        wf.status = 'workflow_queuing'
        wf.save()
        # # 删除定时执行任务
        # schedule_name = f"sqlreview-timing-{wf.id}"
        # del_schedule(schedule_name)
        # 加入执行队列
        # async_task(
        #     "common.sql.execute_sql.execute",
        #     wf.id,
        #     request.user,
        #     hook="common.sql.execute_sql.execute_callback",
        #     timeout=-1,
        #     task_name=f"sqlreview-execute-{wf.id}",
        # )
        task = sql_execute_task.apply_async((wf.id, ), )
        return Response({'code': 20000, 'data': '已提交后台执行！'})

    @action(methods=['POST'], url_path='rollback', detail=False)
    def sql_rollback(self, request, *args, **kwargs):
        """
        SQL回滚
        """
        deploy_method = request.data.get('deploy_method', 'manual')
        expect_time = request.data.get('expect_time', None)
        src_id = request.data.get('src_id', None)
        topic = request.data.get('topic', None)
        sql_wf = self.queryset.get(id=src_id)
        cache_key = f'sqlrollback:{sql_wf.order_id}'
        node_field = None
        try:
            wf = Workflow.objects.get(wid=sql_wf.order_id)
            src_template = WorkflowTemplate.objects.get(name=wf.template.name)
            print('获取到源模板', src_template, src_template.id)
            node_field = src_template.nodes[0]['form_models'][0]['field']
            if not node_field:
                return Response({'code': 50000, 'message': '创建回滚工单异常，原因: 找不到节点ID!'})
            query_engine = get_engine(instance=sql_wf.instance)
            list_backup_sql = cache.get(cache_key)
            if not list_backup_sql:
                logger.debug(f'从后端获取回滚语句')
                list_backup_sql = query_engine.get_rollback(workflow=sql_wf)
                cache.set(cache_key, list_backup_sql, 60 * 60 * 6)
            sql_content = '\n'.join([i[1] for i in list_backup_sql])
            template = {'id': src_template.id, 'nodes': src_template.nodes}
            workflow_form = {'deploy_method': deploy_method,
                             'flag': 'sql', 'topic': topic}
            if deploy_method == 'auto':
                expect_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            form = {node_field: {'expect_time': expect_time, 'db_name': sql_wf.db_name, 'instance': {'id': sql_wf.instance.id,
                                                                                                     'instance_name': sql_wf.instance.instance_name, 'alias': sql_wf.instance.alias}, 'method': deploy_method, 'sql_content': sql_content}}
            ok, data = create_workflow(
                form, workflow_form, template, request.user)
            if not ok:
                raise OkAPIException({
                    'status': 'failed',
                    'code': 40000,
                    'message': data
                }, code=200)

            life_cycle = LifeCycle(request, data['workflow_obj'],
                                   data['node_obj'], form, is_async=IS_NOTICE_ASYNC)
            life_cycle.handle(check_form=False)
            return Response({
                'data': data['data'],
                'status': 'success',
                'code': 20000,
            })
        except Exception as msg:
            logger.error(traceback.format_exc())
            return Response({'code': 50000, 'messgae': str(msg)})

    @action(methods=['GET'], url_path='rollback/sql', detail=True)
    def get_rollback_sql(self, request, pk=None):
        """
        获取回滚语句
        """
        sql_wf = self.get_object()
        page = request.query_params.get('page', 1)
        size = request.query_params.get('size', 5)
        get_all = request.query_params.get('get_all', None)
        cache_key = f'sqlrollback:{sql_wf.order_id}'
        try:
            wf = Workflow.objects.get(wid=sql_wf.order_id)
            src_template = WorkflowTemplate.objects.get(name=wf.template.name)
            print('获取到源模板', src_template, src_template.id)
            template_id = src_template.id
        except:
            template_id = 0
        try:
            query_engine = get_engine(instance=sql_wf.instance)
            list_backup_sql = cache.get(cache_key)
            if not list_backup_sql:
                logger.debug(f'从后端获取回滚语句')
                list_backup_sql = query_engine.get_rollback(workflow=sql_wf)
                cache.set(cache_key, list_backup_sql, 60 * 60 * 6)
            data = list_backup_sql[(int(page) - 1) *
                                   int(size):int(page) * int(size)]
            return Response({'code': 20000, 'data': {'template_id': template_id, 'rows': data, 'data': list_backup_sql if get_all else [], 'total': len(list_backup_sql)}})
        except Exception as msg:
            logger.error(traceback.format_exc())
            return Response({'code': 50000, 'messgae': str(msg)})

    @action(methods=['GET'], url_path='count', detail=False)
    def count(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset()).filter(
            status__in=['workflow_review_pass', 'workflow_exception'])
        return Response({'code': 20000, 'data': queryset.count()})

    @action(methods=['GET'], url_path='upcoming', detail=False)
    def upcoming(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
