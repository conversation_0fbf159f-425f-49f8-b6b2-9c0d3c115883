package models

// UserProfile 用户信息模型
type UserProfile struct {
	BaseModel
	Username    string `json:"username" gorm:"uniqueIndex"`
	FirstName   string `json:"first_name"`
	LastName    string `json:"last_name"`
	Email       string `json:"email"`
	Position    string `json:"position"`
	Title       string `json:"title"`
	Mobile      string `json:"mobile"`
	IsActive    bool   `json:"is_active" gorm:"default:true"`
	IsSuperuser bool   `json:"is_superuser" gorm:"default:false"`
}

// TableName 指定UserProfile的表名
func (UserProfile) TableName() string {
	return "ucenter_user"
}

// GetDisplayName 获取显示名称
func (u UserProfile) GetDisplayName() string {
	if u.FirstName != "" {
		return u.FirstName
	}
	if u.LastName != "" {
		return u.LastName
	}
	return u.Username
}

// UserLogin 用户登录相关结构
type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// LoginResponse 登录响应结构
type LoginResponse struct {
	Access   string      `json:"access"`
	Refresh  string      `json:"refresh"`
	Username UserProfile `json:"username"`
}

// RefreshTokenRequest 刷新token请求
type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" binding:"required"`
}

// ChangePasswordRequest 修改密码请求结构
type ChangePasswordRequest struct {
	OldPassword string `json:"old_password" binding:"required"`
	NewPassword string `json:"new_password" binding:"required,min=6"`
}

// AuditLog 审计日志
type AuditLog struct {
	BaseModel
	User     string `json:"user" gorm:"size:244;not null"`
	Type     string `json:"type" gorm:"size:64;not null"`
	Action   string `json:"action" gorm:"size:20;not null"`
	ActionIP string `json:"action_ip" gorm:"size:15;not null"`
	Content  string `json:"content" gorm:"type:text"`
	Data     string `json:"data" gorm:"type:text"`
	OldData  string `json:"old_data" gorm:"type:text"`
}

// TableName 指定表名
func (AuditLog) TableName() string {
	return "ucenter_auditlog"
}

// SystemConfig 系统配置
type SystemConfig struct {
	BaseModel
	Name   string `json:"name" gorm:"size:64;uniqueIndex;not null"`
	Config string `json:"config" gorm:"type:text"` // 存储JSON字符串
	Status bool   `json:"status" gorm:"default:false"`
	Type   string `json:"type" gorm:"size:64"`
}

// TableName 指定表名
func (SystemConfig) TableName() string {
	return "system_config"
}
