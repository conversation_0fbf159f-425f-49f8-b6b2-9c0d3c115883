package models

import (
	"time"
)

// WorkflowStatus 工作流状态常量
const (
	StatusWorkflowPending  = "Pending"   // 待处理
	StatusWorkflowComplete = "Completed" // 已完成
	StatusWorkflowReject   = "Rejected"  // 被驳回
	StatusWorkflowRevoke   = "Revoked"   // 已撤回
	StatusWorkflowClose    = "Closed"    // 已关闭
)

// HandleType 处理类型常量
const (
	HandleTypePassed = "passed"
	HandleTypeReject = "reject"
)

// CallbackStatus 回调状态常量
const (
	CallbackStatusPending = "pending"
	CallbackStatusRunning = "running"
	CallbackStatusSuccess = "success"
	CallbackStatusFailed  = "failed"
)

// WorkflowCategory 工作流分类
type WorkflowCategory struct {
	BaseModel
	Name       string `json:"name" gorm:"uniqueIndex"`
	Desc       string `json:"desc" gorm:"type:text"`
	Sort       int    `json:"sort" gorm:"default:999"`
	DeleteFlag bool   `json:"delete_flag" gorm:"default:false"`
}

// TableName 指定WorkflowCategory的表名
func (WorkflowCategory) TableName() string {
	return "workflow_category"
}

// WorkflowTemplate 工作流模板
type WorkflowTemplate struct {
	BaseModel
	Name        string           `json:"name"`
	Comment     string           `json:"comment" gorm:"type:text"`
	CategoryID  uint             `json:"category_id"`
	Category    WorkflowCategory `json:"category" gorm:"foreignKey:CategoryID"`
	Nodes       JSONField        `json:"nodes" gorm:"type:jsonb"`
	Projects    JSONField        `json:"projects" gorm:"type:jsonb"`
	Environment JSONField        `json:"environment" gorm:"type:jsonb"`
	Enabled     bool             `json:"enabled" gorm:"default:true"`
	DeleteFlag  bool             `json:"delete_flag" gorm:"default:false"`
}

// TableName 指定WorkflowTemplate的表名
func (WorkflowTemplate) TableName() string {
	return "workflow_template"
}

// WorkflowTemplateRevisionHistory 工作流模板修订历史
type WorkflowTemplateRevisionHistory struct {
	BaseModel
	TemplateID  uint             `json:"template_id"`
	Template    WorkflowTemplate `json:"template" gorm:"foreignKey:TemplateID"`
	Name        string           `json:"name"`
	Nodes       JSONField        `json:"nodes" gorm:"type:jsonb"`
	Projects    JSONField        `json:"projects" gorm:"type:jsonb"`
	Environment JSONField        `json:"environment" gorm:"type:jsonb"`
	Enabled     bool             `json:"enabled" gorm:"default:true"`
	DeleteFlag  bool             `json:"delete_flag" gorm:"default:false"`
}

// TableName 指定WorkflowTemplateRevisionHistory的表名
func (WorkflowTemplateRevisionHistory) TableName() string {
	return "workflow_template_revision_history"
}

// Workflow 工作流实例
type Workflow struct {
	BaseModel
	WID           string                          `json:"wid" gorm:"uniqueIndex"`
	Topic         string                          `json:"topic"`
	Status        string                          `json:"status" gorm:"default:'Pending'"`
	Node          string                          `json:"node"`
	Form          JSONField                       `json:"form" gorm:"type:jsonb"`
	TemplateID    uint                            `json:"template_id"`
	Template      WorkflowTemplateRevisionHistory `json:"template" gorm:"foreignKey:TemplateID"`
	CreatorID     uint                            `json:"creator_id"`
	Creator       UserProfile                     `json:"creator" gorm:"foreignKey:CreatorID;references:ID;constraint:OnDelete:SET NULL"`
	CloseUserID   uint                            `json:"close_user_id"`
	CloseUser     UserProfile                     `json:"close_user" gorm:"foreignKey:CloseUserID;references:ID;constraint:OnDelete:SET NULL"`
	CloseTime     time.Time                       `json:"close_time"`
	CompleteTime  time.Time                       `json:"complete_time"`
	CurNodeConf   JSONField                       `json:"cur_node_conf" gorm:"type:jsonb"`
	RelatedFields JSONField                       `json:"related_fields" gorm:"type:jsonb"`
	DeleteFlag    bool                            `json:"delete_flag" gorm:"default:false"`
}

// TableName 指定Workflow的表名
func (Workflow) TableName() string {
	return "workflow"
}

// WorkflowNodeHistory 工作流节点处理历史
type WorkflowNodeHistory struct {
	BaseModel
	WorkflowID uint        `json:"workflow_id" gorm:"index"`
	Workflow   Workflow    `json:"workflow" gorm:"foreignKey:WorkflowID"`
	Node       string      `json:"node"`
	OperatorID uint        `json:"operator_id"`
	Operator   UserProfile `json:"operator" gorm:"foreignKey:OperatorID;references:ID;constraint:OnDelete:SET NULL"`
	Comment    string      `json:"comment" gorm:"type:text"`
	HandleType string      `json:"handle_type"` // passed, reject
	Form       JSONField   `json:"form" gorm:"type:jsonb"`
	Data       JSONField   `json:"data" gorm:"type:jsonb"`
	DeleteFlag bool        `json:"delete_flag" gorm:"default:false"`
}

// TableName 指定WorkflowNodeHistory的表名
func (WorkflowNodeHistory) TableName() string {
	return "workflow_node_history"
}

// WorkflowNodeHistoryCallback 工作流节点回调
type WorkflowNodeHistoryCallback struct {
	BaseModel
	NodeHistoryID uint                `json:"node_history_id" gorm:"index"`
	NodeHistory   WorkflowNodeHistory `json:"node_history" gorm:"foreignKey:NodeHistoryID"`
	TriggerID     uint                `json:"trigger_id"`
	Trigger       UserProfile         `json:"trigger" gorm:"foreignKey:TriggerID;references:ID;constraint:OnDelete:SET NULL"`
	Status        string              `json:"status" gorm:"default:'pending'"`
	Callback      string              `json:"callback"`
	Result        JSONField           `json:"result" gorm:"type:jsonb"`
	DeleteFlag    bool                `json:"delete_flag" gorm:"default:false"`
}

// TableName 指定WorkflowNodeHistoryCallback的表名
func (WorkflowNodeHistoryCallback) TableName() string {
	return "workflow_node_history_callback"
}

// WorkflowDraftBox 工作流草稿箱
type WorkflowDraftBox struct {
	BaseModel
	Name       string           `json:"name"`
	TemplateID uint             `json:"template_id"`
	Template   WorkflowTemplate `json:"template" gorm:"foreignKey:TemplateID"`
	Form       JSONField        `json:"form" gorm:"type:jsonb"`
	CreatorID  uint             `json:"creator_id"`
	Creator    UserProfile      `json:"creator" gorm:"foreignKey:CreatorID;references:ID;constraint:OnDelete:SET NULL"`
	DeleteFlag bool             `json:"delete_flag" gorm:"default:false"`
}

// TableName 指定WorkflowDraftBox的表名
func (WorkflowDraftBox) TableName() string {
	return "workflow_draft_box"
}

// WorkflowAttachment 工作流附件
type WorkflowAttachment struct {
	BaseModel
	WorkflowID uint        `json:"workflow_id" gorm:"index"`
	Workflow   Workflow    `json:"workflow" gorm:"foreignKey:WorkflowID"`
	FileName   string      `json:"file_name"`
	FilePath   string      `json:"file_path"`
	FileSize   int64       `json:"file_size"`
	UploaderId uint        `json:"uploader_id"`
	Uploader   UserProfile `json:"uploader" gorm:"foreignKey:UploaderId;references:ID;constraint:OnDelete:SET NULL"`
	DeleteFlag bool        `json:"delete_flag" gorm:"default:false"`
}

// TableName 指定WorkflowAttachment的表名
func (WorkflowAttachment) TableName() string {
	return "workflow_attachment"
}
