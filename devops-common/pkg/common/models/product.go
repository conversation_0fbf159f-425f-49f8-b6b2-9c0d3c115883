package models

// Product 产品模型
type Product struct {
	BaseModel
	Name        string    `json:"name" gorm:"uniqueIndex"`
	<PERSON><PERSON>       string    `json:"alias"`
	Description string    `json:"desc" gorm:"type:text"`
	Prefix      string    `json:"prefix"`
	Managers    JSONField `json:"managers" gorm:"type:jsonb"`
}

// TableName 指定Product的表名
func (Product) TableName() string {
	return "cmdb_product"
}

// Project 项目模型
type Project struct {
	BaseModel
	ProjectID   string    `json:"projectid" gorm:"column:projectid;uniqueIndex"`
	Name        string    `json:"name"`
	Alias       string    `json:"alias"`
	ProductID   uint      `json:"product_id"`
	Product     Product   `json:"product" gorm:"foreignKey:ProductID"` // 关联Product
	CreatorID   uint      `json:"creator_id"`
	Manager     int       `json:"manager"`
	Developer   int       `json:"developer"`
	Tester      int       `json:"tester"`
	Description string    `json:"desc" gorm:"type:text"`
	Notify      JSONField `json:"notify" gorm:"type:jsonb"`
	PipelineDL  JSONField `json:"pipeline_dl" gorm:"type:jsonb"`
}

// TableName 指定Project的表名
func (Project) TableName() string {
	return "cmdb_project"
}

// Environment 环境模型
type Environment struct {
	BaseModel
	Name          string    `json:"name" gorm:"uniqueIndex"`
	Alias         string    `json:"alias"`
	TicketOn      int       `json:"ticket_on" gorm:"default:0"`
	MergeOn       int       `json:"merge_on" gorm:"default:0"`
	Template      JSONField `json:"template" gorm:"type:jsonb"`
	AllowCIBranch JSONField `json:"allow_ci_branch" gorm:"type:jsonb"`
	AllowCDBranch JSONField `json:"allow_cd_branch" gorm:"type:jsonb"`
	Extra         JSONField `json:"extra" gorm:"type:jsonb"`
	Description   string    `json:"desc" gorm:"type:text"`
	Sort          int       `json:"sort" gorm:"default:999"`
}

// TableName 指定Environment的表名
func (Environment) TableName() string {
	return "cmdb_environment"
}

// MicroApp 微服务应用模型
type MicroApp struct {
	BaseModel
	AppID        string    `json:"appid" gorm:"column:appid;uniqueIndex"`
	Name         string    `json:"name"`
	Alias        string    `json:"alias"`
	ProjectID    uint      `json:"project_id"`
	Project      Project   `json:"project" gorm:"foreignKey:ProjectID"` // 关联Project
	CreatorID    uint      `json:"creator_id"`
	Repo         JSONField `json:"repo" gorm:"type:jsonb"`
	Target       JSONField `json:"target" gorm:"type:jsonb"`
	TeamMembers  JSONField `json:"team_members" gorm:"type:jsonb"`
	Category     string    `json:"category"`
	Template     JSONField `json:"template" gorm:"type:jsonb"`
	Language     string    `json:"language" gorm:"default:java"`
	BuildCommand string    `json:"build_command"`
	MultipleApp  bool      `json:"multiple_app" gorm:"default:false"`
	MultipleIDs  JSONField `json:"multiple_ids" gorm:"type:jsonb"`
	Dockerfile   JSONField `json:"dockerfile" gorm:"type:jsonb"`
	Online       bool      `json:"online" gorm:"default:true"`
	Description  string    `json:"desc" gorm:"type:text"`
	Notify       JSONField `json:"notify" gorm:"type:jsonb"`
	CanEdit      JSONField `json:"can_edit" gorm:"type:jsonb"`
	IsK8s        string    `json:"is_k8s" gorm:"default:k8s"`
	Modules      JSONField `json:"modules" gorm:"type:jsonb"`
	Ports        JSONField `json:"ports" gorm:"type:jsonb"`
	ScanBranch   string    `json:"scan_branch" gorm:"default:test"`
	PipelineID   int       `json:"pipeline_id"`
	PipelineDL   JSONField `json:"pipeline_dl" gorm:"type:jsonb"`
}

// TableName 指定MicroApp的表名
func (MicroApp) TableName() string {
	return "cmdb_microapp"
}

// AppInfo 应用信息模型
type AppInfo struct {
	BaseModel
	UniqTag       string      `json:"uniq_tag" gorm:"uniqueIndex"`
	AppID         uint        `json:"app_id"`
	App           MicroApp    `json:"app" gorm:"foreignKey:AppID"` // 关联MicroApp
	EnvironmentID uint        `json:"environment_id"`
	Environment   Environment `json:"environment" gorm:"foreignKey:EnvironmentID"` // 关联Environment
	Branch        string      `json:"branch"`
	BuildCommand  string      `json:"build_command"`
	Version       string      `json:"version"`
	Template      JSONField   `json:"template" gorm:"type:jsonb"`
	PipelineID    int         `json:"pipeline_id"`
	PipelineDL    JSONField   `json:"pipeline_dl" gorm:"type:jsonb"`
	IsEnable      int         `json:"is_enable" gorm:"default:1"`
	Description   string      `json:"desc" gorm:"type:text"`
	CanEdit       JSONField   `json:"can_edit" gorm:"type:jsonb"`
	Online        int         `json:"online" gorm:"default:0"`
	Ports         JSONField   `json:"ports" gorm:"type:jsonb"`
	AllowCIBranch JSONField   `json:"allow_ci_branch" gorm:"type:jsonb"`
	AllowCDBranch JSONField   `json:"allow_cd_branch" gorm:"type:jsonb"`
}

// TableName 指定AppInfo的表名
func (AppInfo) TableName() string {
	return "cmdb_appinfo"
}
