package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"fmt"
	"time"
)

// BaseModel 所有模型的基础结构 - 使用标准的GORM命名
type BaseModel struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// TimeAbstract 时间抽象结构 - 兼容Django数据库，不使用软删除
type TimeAbstract struct {
	ID          uint      `json:"id" gorm:"primarykey"`
	CreatedTime time.Time `json:"created_time" gorm:"autoCreateTime"`
	UpdatedTime time.Time `json:"updated_time" gorm:"autoUpdateTime"`
}

// JSONField 自定义JSON类型，可以存储任意JSON值
type JSONField struct {
	Data interface{}
}

// 实现json.Marshaler接口
func (j JSONField) MarshalJSON() ([]byte, error) {
	if j.Data == nil {
		return []byte("null"), nil
	}
	return json.Marshal(j.Data)
}

// 实现json.Unmarshaler接口
func (j *JSONField) UnmarshalJSON(data []byte) error {
	return json.Unmarshal(data, &j.Data)
}

// 实现driver.Valuer接口，用于数据库写入
func (j JSONField) Value() (driver.Value, error) {
	if j.Data == nil {
		return "null", nil
	}

	// 如果已经是string类型，直接返回
	if str, ok := j.Data.(string); ok {
		// 检查是否是有效的JSON
		var temp interface{}
		if json.Unmarshal([]byte(str), &temp) == nil {
			// 重新序列化确保JSON格式正确
			bytes, err := json.Marshal(temp)
			if err != nil {
				return "null", nil
			}
			return string(bytes), nil
		}
		// 如果不是有效JSON，则序列化为JSON字符串
		bytes, err := json.Marshal(str)
		if err != nil {
			return "null", nil
		}
		return string(bytes), nil
	}

	// 序列化为JSON字符串
	bytes, err := json.Marshal(j.Data)
	if err != nil {
		return "null", nil
	}

	// 检查序列化结果，确保没有特殊字符导致PostgreSQL编码问题
	result := string(bytes)

	// 对于空值，返回明确的null
	if result == "" || result == "null" {
		return "null", nil
	}

	// 对于空对象或数组，确保格式正确
	if result == "{}" {
		return "{}", nil
	}
	if result == "[]" {
		return "[]", nil
	}

	// 过滤掉潜在的有问题字符
	// 检查是否包含非打印字符
	for _, c := range result {
		// 控制字符或不可打印字符
		if c < 32 || (c >= 127 && c <= 159) {
			// 发现无效字符，用空对象替换整个值
			if bytes[0] == '{' {
				return "{}", nil
			} else if bytes[0] == '[' {
				return "[]", nil
			}
			return "null", nil
		}
	}

	return result, nil
}

// 实现sql.Scanner接口，用于数据库读取
func (j *JSONField) Scan(value interface{}) error {
	if value == nil {
		j.Data = nil
		return nil
	}

	var bytes []byte
	switch v := value.(type) {
	case []byte:
		if len(v) == 0 {
			j.Data = nil
			return nil
		}
		bytes = v
	case string:
		if v == "" || v == "null" {
			j.Data = nil
			return nil
		}
		bytes = []byte(v)
	default:
		return fmt.Errorf("cannot scan %T into JSONField", value)
	}

	// 尝试解析JSON
	var temp interface{}
	if err := json.Unmarshal(bytes, &temp); err != nil {
		// 如果解析失败，直接存储原始字符串
		j.Data = string(bytes)
		return nil
	}

	j.Data = temp
	return nil
}

// JsonMap 自定义JSON Map类型
type JsonMap map[string]interface{}

// Value 实现driver.Valuer接口
func (j JsonMap) Value() (driver.Value, error) {
	if j == nil {
		return nil, nil
	}
	return json.Marshal(j)
}

// Scan 实现sql.Scanner接口
func (j *JsonMap) Scan(value interface{}) error {
	if value == nil {
		*j = nil
		return nil
	}
	var bytes []byte
	switch v := value.(type) {
	case []byte:
		bytes = v
	case string:
		bytes = []byte(v)
	default:
		return errors.New("类型错误")
	}
	return json.Unmarshal(bytes, j)
}

// StringSlice 自定义字符串切片类型
type StringSlice []string

// Value 实现driver.Valuer接口
func (s StringSlice) Value() (driver.Value, error) {
	if s == nil {
		return nil, nil
	}
	return json.Marshal(s)
}

// Scan 实现sql.Scanner接口
func (s *StringSlice) Scan(value interface{}) error {
	if value == nil {
		*s = nil
		return nil
	}
	var bytes []byte
	switch v := value.(type) {
	case []byte:
		bytes = v
	case string:
		bytes = []byte(v)
	default:
		return errors.New("类型错误")
	}
	return json.Unmarshal(bytes, s)
}
