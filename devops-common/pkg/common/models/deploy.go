package models

import (
	"time"
)

// 通用状态常量
const (
	StatusPending  = 0 // 未执行
	StatusSuccess  = 1 // 成功
	StatusFailed   = 2 // 失败
	StatusRunning  = 3 // 执行中
	StatusCanceled = 4 // 已取消
)

// 部署类型常量
const (
	DeployTypeNew      = 0 // 常规部署
	DeployTypeRollback = 2 // 版本回退
)

// BuildJob 构建任务模型
type BuildJob struct {
	BaseModel
	OrderID     string       `json:"order_id" gorm:"index"`
	AppID       string       `json:"appid" gorm:"column:appid;index"`
	AppInfoID   uint         `json:"appinfo_id" gorm:"column:appinfo_id;index"`
	DeployerID  uint         `json:"deployer_id" gorm:"column:deployer_id"`
	Deployer    *UserProfile `json:"deployer" gorm:"foreignKey:DeployerID"` // 关联UserProfile
	Status      int          `json:"status" gorm:"default:3"`               // 状态：0-未构建，1-构建成功，2-构建失败，3-构建中，4-已取消
	QueueNumber int          `json:"queue_number"`
	BuildNumber int          `json:"build_number"`
	Commits     JSONField    `json:"commits" gorm:"type:jsonb"`
	CommitTag   JSONField    `json:"commit_tag" gorm:"type:jsonb"`
	IsDeploy    int          `json:"is_deploy" gorm:"default:0"` // 0-仅构建，1-构建并部署
	Image       string       `json:"image"`
	SyncStatus  int          `json:"sync_status" gorm:"default:0"`
	Modules     string       `json:"modules"`
	BatchUUID   string       `json:"batch_uuid"`
}

// TableName 指定BuildJob的表名
func (BuildJob) TableName() string {
	return "deploy_buildjob"
}

// BuildJobResult 构建结果模型
type BuildJobResult struct {
	BaseModel
	JobID         uint      `json:"job_id" gorm:"index"`
	Result        JSONField `json:"result" gorm:"type:jsonb"`
	ConsoleOutput string    `json:"console_output" gorm:"type:text"`
}

// TableName 指定BuildJobResult的表名
func (BuildJobResult) TableName() string {
	return "deploy_buildjobresult"
}

// DeployJob 部署任务模型
type DeployJob struct {
	BaseModel
	UniqID          string       `json:"uniq_id" gorm:"uniqueIndex"`
	OrderID         string       `json:"order_id" gorm:"index"`
	AppID           string       `json:"appid" gorm:"column:appid;index"`
	AppInfoID       uint         `json:"appinfo_id" gorm:"column:appinfo_id;index"`
	DeployerID      uint         `json:"deployer_id"`
	Deployer        *UserProfile `json:"deployer" gorm:"foreignKey:DeployerID"` // 关联UserProfile
	Status          int          `json:"status" gorm:"default:0"`               // 状态：0-未部署，1-部署成功，2-部署失败，3-部署中，4-已取消
	Image           string       `json:"image"`
	Kubernetes      JSONField    `json:"kubernetes" gorm:"type:jsonb"`
	DeployType      int          `json:"deploy_type" gorm:"default:0"` // 0-常规部署，2-版本回退
	RollbackReason  int          `json:"rollback_reason"`
	RollbackComment string       `json:"rollback_comment" gorm:"type:text"`
	Modules         string       `json:"modules"`
	BatchUUID       string       `json:"batch_uuid"`
}

// TableName 指定DeployJob的表名
func (DeployJob) TableName() string {
	return "deploy_deployjob"
}

// DeployJobResult 部署结果模型
type DeployJobResult struct {
	BaseModel
	JobID  uint      `json:"job_id" gorm:"index"`
	Result JSONField `json:"result" gorm:"type:jsonb"`
}

// TableName 指定DeployJobResult的表名
func (DeployJobResult) TableName() string {
	return "deploy_deployjobresult"
}

// PublishApp 待发布应用模型
type PublishApp struct {
	BaseModel
	OrderID       string    `json:"order_id" gorm:"index"`
	AppID         string    `json:"appid" gorm:"column:appid;index"`
	AppInfoID     uint      `json:"appinfo_id" gorm:"index"`
	Name          string    `json:"name"`
	Alias         string    `json:"alias"`
	Project       string    `json:"project"`
	Product       string    `json:"product"`
	Category      string    `json:"category"`
	Environment   int       `json:"environment"`
	Branch        string    `json:"branch"`
	Image         string    `json:"image"`
	Commits       JSONField `json:"commits" gorm:"type:jsonb"`
	DeployType    string    `json:"deploy_type"`
	DeployTypeTag int       `json:"deploy_type_tag" gorm:"default:0"`
	Status        int       `json:"status" gorm:"default:0"` // 状态：0-未发布，1-发布成功，2-发布失败，3-发布中，4-已取消
	DeleteFlag    bool      `json:"delete_flag" gorm:"default:false"`
	Modules       string    `json:"modules"`
}

// TableName 指定PublishApp的表名
func (PublishApp) TableName() string {
	return "deploy_publishapp"
}

// PublishOrder 发布工单模型
type PublishOrder struct {
	BaseModel
	OrderID            string    `json:"order_id" gorm:"uniqueIndex"`
	DingTalkTID        string    `json:"dingtalk_tid"`
	Title              string    `json:"title"`
	Category           int       `json:"category" gorm:"default:0"` // 发布类型
	CreatorID          uint      `json:"creator_id"`
	NodeName           string    `json:"node_name"`
	Content            string    `json:"content" gorm:"type:text"`
	FormData           JSONField `json:"formdata" gorm:"type:jsonb"`
	Effect             string    `json:"effect" gorm:"type:text"`
	Environment        int       `json:"environment"`
	App                JSONField `json:"app" gorm:"type:jsonb"`
	Status             int       `json:"status" gorm:"default:0"` // 状态：0-未发布，1-发布成功，2-发布失败，3-发布中，4-已取消
	Result             string    `json:"result" gorm:"type:text"`
	ExpectTime         time.Time `json:"expect_time"`
	ExecutorID         uint      `json:"executor_id"`
	DeployTime         time.Time `json:"deploy_time"`
	Method             string    `json:"method" gorm:"default:manual"`
	TeamMembers        JSONField `json:"team_members" gorm:"type:jsonb"`
	ExtraDeployMembers JSONField `json:"extra_deploy_members" gorm:"type:jsonb"`
}

// TableName 指定PublishOrder的表名
func (PublishOrder) TableName() string {
	return "deploy_publishorder"
}

// CommitInfo 提交信息
type CommitInfo struct {
	CommitterName string `json:"committer_name"`
	CommittedDate string `json:"committed_date"`
	ShortID       string `json:"short_id"`
	Message       string `json:"message"`
}

// CommitTag 提交标签
type CommitTag struct {
	Label string `json:"label"`
	Name  string `json:"name"`
}

// CIRequest CI请求结构
type CIRequest struct {
	IsDeploy  bool       `json:"is_deploy"`  // 是否部署
	Modules   string     `json:"modules"`    // 模块
	ID        uint       `json:"id"`         // 应用ID
	Env       uint       `json:"env"`        // 环境ID
	Force     bool       `json:"force"`      // 是否强制构建
	CommitTag CommitTag  `json:"commit_tag"` // 提交标签
	Commits   CommitInfo `json:"commits"`    // 提交信息
	BatchUUID string     `json:"batch_uuid"` // 批次UUID
}

// KubernetesCluster K8s集群配置模型
type KubernetesCluster struct {
	BaseModel
	Name        string    `json:"name" gorm:"uniqueIndex"`
	Version     JSONField `json:"version" gorm:"type:jsonb"`
	Description string    `json:"desc" gorm:"type:text"`
	Config      JSONField `json:"config" gorm:"type:jsonb"` // 应该被加密
	ClusterType string    `json:"cluster_type" gorm:"default:normal"`
	Extra       JSONField `json:"extra" gorm:"type:jsonb"`
	Sort        int       `json:"sort" gorm:"default:999"`
	IDCID       uint      `json:"idc_id"`
}

// TableName 指定KubernetesCluster的表名
func (KubernetesCluster) TableName() string {
	return "cmdb_kubernetesclusters"
}

// KubernetesDeploy K8s部署关系模型
type KubernetesDeploy struct {
	BaseModel
	AppInfoID    uint              `json:"appinfo_id" gorm:"index"`
	KubernetesID uint              `json:"kubernetes_id" gorm:"index"`
	Kubernetes   KubernetesCluster `json:"kubernetes" gorm:"foreignKey:KubernetesID"` // 关联KubernetesCluster
	Online       int               `json:"online" gorm:"default:0"`
	Version      string            `json:"version"`
}

// TableName 指定KubernetesDeploy的表名
func (KubernetesDeploy) TableName() string {
	return "cmdb_kubernetesdeploy"
}
