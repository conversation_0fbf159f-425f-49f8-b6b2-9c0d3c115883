package response

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// Response 通用响应结构
type Response struct {
	Code    int         `json:"code"`    // 业务状态码
	Message string      `json:"message"` // 响应消息
	Data    interface{} `json:"data"`    // 响应数据
}

// 预定义状态码
const (
	CodeSuccess      = 200 // 成功
	CodeBadRequest   = 400 // 请求参数错误
	CodeUnauthorized = 401 // 未授权
	CodeForbidden    = 403 // 禁止访问
	CodeNotFound     = 404 // 资源不存在
	CodeServerError  = 500 // 服务器内部错误
)

// 状态码对应的默认消息
var codeMessages = map[int]string{
	CodeSuccess:      "操作成功",
	CodeBadRequest:   "请求参数错误",
	CodeUnauthorized: "未授权或登录已过期",
	CodeForbidden:    "无权限执行此操作",
	CodeNotFound:     "资源不存在",
	CodeServerError:  "服务器内部错误",
}

// Success 成功响应
func Success(c *gin.Context, data interface{}) {
	JSON(c, CodeSuccess, codeMessages[CodeSuccess], data)
}

// SuccessWithMessage 带自定义消息的成功响应
func SuccessWithMessage(c *gin.Context, message string, data interface{}) {
	JSON(c, CodeSuccess, message, data)
}

// Fail 失败响应
func Fail(c *gin.Context, code int, message string) {
	JSON(c, code, message, nil)
}

// FailWithDefaultMessage 使用默认消息的失败响应
func FailWithDefaultMessage(c *gin.Context, code int) {
	message, ok := codeMessages[code]
	if !ok {
		message = "未知错误"
	}
	JSON(c, code, message, nil)
}

// BadRequest 请求参数错误响应
func BadRequest(c *gin.Context, message string) {
	if message == "" {
		message = codeMessages[CodeBadRequest]
	}
	JSON(c, CodeBadRequest, message, nil)
}

// Unauthorized 未授权响应
func Unauthorized(c *gin.Context, message string) {
	if message == "" {
		message = codeMessages[CodeUnauthorized]
	}
	JSON(c, CodeUnauthorized, message, nil)
}

// Forbidden 禁止访问响应
func Forbidden(c *gin.Context, message string) {
	if message == "" {
		message = codeMessages[CodeForbidden]
	}
	JSON(c, CodeForbidden, message, nil)
}

// NotFound 资源不存在响应
func NotFound(c *gin.Context, message string) {
	if message == "" {
		message = codeMessages[CodeNotFound]
	}
	JSON(c, CodeNotFound, message, nil)
}

// ServerError 服务器内部错误响应
func ServerError(c *gin.Context, message string) {
	if message == "" {
		message = codeMessages[CodeServerError]
	}
	JSON(c, CodeServerError, message, nil)
}

// JSON 发送JSON响应
func JSON(c *gin.Context, code int, message string, data interface{}) {
	httpStatus := http.StatusOK

	// 根据业务状态码设置对应的HTTP状态码
	switch code {
	case CodeBadRequest:
		httpStatus = http.StatusBadRequest
	case CodeUnauthorized:
		httpStatus = http.StatusUnauthorized
	case CodeForbidden:
		httpStatus = http.StatusForbidden
	case CodeNotFound:
		httpStatus = http.StatusNotFound
	case CodeServerError:
		httpStatus = http.StatusInternalServerError
	}

	c.JSON(httpStatus, Response{
		Code:    code,
		Message: message,
		Data:    data,
	})
}
