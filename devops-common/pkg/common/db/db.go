package db

import (
	"github.com/devops-common/pkg/common/config"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// Database 是数据库操作的封装
type Database struct {
	DB     *gorm.DB
	Logger *logrus.Logger
	Config *config.DatabaseConfig
}

// NewDatabase 创建新的数据库实例
func NewDatabase(cfg *config.DatabaseConfig, logger *logrus.Logger) (*Database, error) {
	// 初始化数据库连接
	db, err := config.InitDatabase(cfg, logger)
	if err != nil {
		return nil, err
	}

	return &Database{
		DB:     db,
		Logger: logger,
		Config: cfg,
	}, nil
}

// AutoMigrate 自动迁移数据库表结构
func (d *Database) AutoMigrate(models ...interface{}) error {
	d.Logger.Info("开始数据库迁移...")

	for _, model := range models {
		if err := d.DB.AutoMigrate(model); err != nil {
			d.Logger.Errorf("迁移模型失败 %T: %v", model, err)
			return err
		}
	}

	d.Logger.Info("数据库迁移完成")
	return nil
}

// Transaction 执行数据库事务
func (d *Database) Transaction(fn func(tx *gorm.DB) error) error {
	return d.DB.Transaction(fn)
}

// Ping 检查数据库连接
func (d *Database) Ping() error {
	sqlDB, err := d.DB.DB()
	if err != nil {
		return err
	}
	return sqlDB.Ping()
}

// Close 关闭数据库连接
func (d *Database) Close() error {
	sqlDB, err := d.DB.DB()
	if err != nil {
		return err
	}
	return sqlDB.Close()
}

// BatchInsert 批量插入数据
func (d *Database) BatchInsert(entities interface{}, batchSize int) error {
	return d.DB.CreateInBatches(entities, batchSize).Error
}

// GetDB 获取GORM DB实例
func (d *Database) GetDB() *gorm.DB {
	return d.DB
}
