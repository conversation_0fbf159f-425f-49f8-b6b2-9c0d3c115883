package logger

import (
	"io"
	"os"
	"path/filepath"

	"github.com/devops-common/pkg/common/config"
	"github.com/sirupsen/logrus"
)

// 创建日志实例
func NewLogger(cfg *config.LogConfig, serviceName string) *logrus.Logger {
	log := logrus.New()

	// 设置日志级别
	level, err := logrus.ParseLevel(cfg.Level)
	if err != nil {
		level = logrus.InfoLevel
	}
	log.SetLevel(level)

	// 设置日志格式
	if cfg.Format == "json" {
		log.SetFormatter(&logrus.JSONFormatter{
			TimestampFormat: "2006-01-02 15:04:05",
		})
	} else {
		log.SetFormatter(&logrus.TextFormatter{
			FullTimestamp:   true,
			TimestampFormat: "2006-01-02 15:04:05",
			ForceColors:     true,
		})
	}

	// 设置日志输出
	var output io.Writer
	switch cfg.Output {
	case "stdout":
		output = os.Stdout
	case "stderr":
		output = os.Stderr
	default:
		// 如果是文件路径，则创建或打开日志文件
		if cfg.Output != "" {
			// 确保日志目录存在
			logDir := filepath.Dir(cfg.Output)
			if _, err := os.Stat(logDir); os.IsNotExist(err) {
				os.MkdirAll(logDir, 0755)
			}

			// 打开日志文件
			file, err := os.OpenFile(cfg.Output, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
			if err == nil {
				output = file
			} else {
				// 如果打开文件失败，回退到标准输出
				output = os.Stdout
				log.Warnf("无法打开日志文件 %s，使用标准输出: %v", cfg.Output, err)
			}
		} else {
			// 默认使用服务名作为日志文件名
			logFile := serviceName + ".log"
			file, err := os.OpenFile(logFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
			if err == nil {
				output = file
			} else {
				output = os.Stdout
				log.Warnf("无法创建默认日志文件 %s，使用标准输出: %v", logFile, err)
			}
		}
	}

	log.SetOutput(output)
	log.Infof("日志系统初始化完成，级别: %s, 格式: %s", cfg.Level, cfg.Format)
	return log
}
