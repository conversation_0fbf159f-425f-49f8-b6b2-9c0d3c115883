package config

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
	"gorm.io/driver/mysql"
	"gorm.io/driver/postgres"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// BaseConfig 是所有服务的基础配置结构
type BaseConfig struct {
	Server   ServerConfig   `mapstructure:"server"`
	Database DatabaseConfig `mapstructure:"database"`
	Nats     NatsConfig     `mapstructure:"nats"`
	Log      LogConfig      `mapstructure:"log"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Host         string `mapstructure:"host"`
	Port         int    `mapstructure:"port"`
	ReadTimeout  int    `mapstructure:"read_timeout"`
	WriteTimeout int    `mapstructure:"write_timeout"`
	IdleTimeout  int    `mapstructure:"idle_timeout"`
	Mode         string `mapstructure:"mode"` // gin模式: debug, release, test
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Type           string `mapstructure:"type"`
	Host           string `mapstructure:"host"`
	Port           int    `mapstructure:"port"`
	Username       string `mapstructure:"username"`
	Password       string `mapstructure:"password"`
	DBName         string `mapstructure:"dbname"`
	SSLMode        string `mapstructure:"sslmode"`
	TimeZone       string `mapstructure:"timezone"`
	MaxConnections int    `mapstructure:"max_connections"`
	IdleTime       int    `mapstructure:"idle_time"`
	LogLevel       string `mapstructure:"log_level"`
	Charset        string `mapstructure:"charset"`
}

// NatsConfig NATS配置
type NatsConfig struct {
	URL     string `mapstructure:"url"`
	Subject string `mapstructure:"subject"`
}

// LogConfig 日志配置
type LogConfig struct {
	Level  string `mapstructure:"level"`
	Format string `mapstructure:"format"`
	Output string `mapstructure:"output"`
}

// LoadConfig 加载配置文件
func LoadConfig(configPath string, serviceName string, config interface{}) error {
	// 设置配置文件搜索路径
	if configPath != "" {
		viper.SetConfigFile(configPath)
	} else {
		// 获取当前工作目录
		pwd, _ := os.Getwd()

		// 搜索多个可能的配置文件路径
		viper.AddConfigPath(pwd)
		viper.AddConfigPath(filepath.Join(pwd, "config"))
		viper.AddConfigPath(filepath.Join(pwd, serviceName))
		viper.AddConfigPath(filepath.Join(pwd, serviceName+"/config"))
		viper.SetConfigName("config")
		viper.SetConfigType("yaml")
	}

	// 设置环境变量前缀
	viper.SetEnvPrefix(strings.ToUpper(serviceName))
	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	viper.AutomaticEnv()

	// 设置默认值
	setBaseDefaults(serviceName)

	// 读取配置文件
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); ok {
			fmt.Printf("警告: 未找到配置文件，使用默认配置和环境变量\n")
		} else {
			return fmt.Errorf("读取配置文件失败: %w", err)
		}
	}

	// 解析配置到结构体
	if err := viper.Unmarshal(config); err != nil {
		return fmt.Errorf("解析配置失败: %w", err)
	}

	return nil
}

// setBaseDefaults 设置基础默认配置值
func setBaseDefaults(serviceName string) {
	// 服务器默认配置
	viper.SetDefault("server.host", "0.0.0.0")
	viper.SetDefault("server.port", 8080)
	viper.SetDefault("server.read_timeout", 60)
	viper.SetDefault("server.write_timeout", 60)
	viper.SetDefault("server.idle_timeout", 60)
	viper.SetDefault("server.mode", "release")

	// 数据库默认配置
	viper.SetDefault("database.type", "sqlite")
	viper.SetDefault("database.host", "localhost")
	viper.SetDefault("database.port", 3306)
	viper.SetDefault("database.username", "root")
	viper.SetDefault("database.password", "")
	viper.SetDefault("database.dbname", serviceName+".db")
	viper.SetDefault("database.sslmode", "disable")
	viper.SetDefault("database.timezone", "Asia/Shanghai")
	viper.SetDefault("database.max_connections", 100)
	viper.SetDefault("database.idle_time", 10)
	viper.SetDefault("database.log_level", "info")
	viper.SetDefault("database.charset", "utf8mb4")

	// NATS默认配置
	viper.SetDefault("nats.url", "nats://localhost:4222")
	viper.SetDefault("nats.subject", serviceName+".events")

	// 日志默认配置
	viper.SetDefault("log.level", "info")
	viper.SetDefault("log.format", "json")
	viper.SetDefault("log.output", "stdout")
}

// ValidateBaseConfig 验证基础配置
func ValidateBaseConfig(config *BaseConfig) error {
	// 验证服务器配置
	if config.Server.Port <= 0 || config.Server.Port > 65535 {
		return fmt.Errorf("无效的服务端口: %d", config.Server.Port)
	}

	// 验证数据库配置
	if config.Database.Type == "" {
		return fmt.Errorf("数据库类型不能为空")
	}

	supportedTypes := []string{"mysql", "postgres", "sqlite"}
	supported := false
	for _, t := range supportedTypes {
		if config.Database.Type == t {
			supported = true
			break
		}
	}
	if !supported {
		return fmt.Errorf("不支持的数据库类型: %s", config.Database.Type)
	}

	return nil
}

// GetDSN 获取数据库连接字符串
func (c *DatabaseConfig) GetDSN() string {
	switch c.Type {
	case "mysql":
		return fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
			c.Username, c.Password, c.Host, c.Port, c.DBName)
	case "postgres":
		return fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
			c.Host, c.Port, c.Username, c.Password, c.DBName, c.SSLMode)
	case "sqlite":
		return c.DBName
	default:
		return ""
	}
}

// InitDatabase 初始化数据库连接
func InitDatabase(cfg *DatabaseConfig, log *logrus.Logger) (*gorm.DB, error) {
	var dialector gorm.Dialector

	// 设置GORM日志级别
	logLevel := logger.Info
	switch cfg.LogLevel {
	case "silent":
		logLevel = logger.Silent
	case "error":
		logLevel = logger.Error
	case "warn":
		logLevel = logger.Warn
	case "info":
		logLevel = logger.Info
	}

	gormLogger := logger.New(
		log,
		logger.Config{
			SlowThreshold:             time.Second,
			LogLevel:                  logLevel,
			IgnoreRecordNotFoundError: true,
			Colorful:                  false,
		},
	)

	// 根据驱动类型构建dialector
	switch cfg.Type {
	case "mysql":
		dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&parseTime=True&loc=Local",
			cfg.Username, cfg.Password, cfg.Host, cfg.Port, cfg.DBName, cfg.Charset)
		dialector = mysql.Open(dsn)
	case "postgres":
		dsn := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%d sslmode=%s TimeZone=%s",
			cfg.Host, cfg.Port, cfg.Username, cfg.Password, cfg.DBName, cfg.SSLMode, cfg.TimeZone)
		dialector = postgres.Open(dsn)
	case "sqlite":
		dialector = sqlite.Open(cfg.DBName)
	default:
		return nil, fmt.Errorf("不支持的数据库类型: %s", cfg.Type)
	}

	// 连接数据库
	db, err := gorm.Open(dialector, &gorm.Config{
		Logger: gormLogger,
	})
	if err != nil {
		return nil, fmt.Errorf("连接数据库失败: %w", err)
	}

	// 获取底层sql.DB实例并设置连接池参数
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("获取数据库实例失败: %w", err)
	}

	// 设置连接池参数
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(cfg.MaxConnections)
	sqlDB.SetConnMaxLifetime(time.Duration(cfg.IdleTime) * time.Second)

	log.Infof("✅ 数据库连接成功 [%s]", cfg.Type)
	return db, nil
}
