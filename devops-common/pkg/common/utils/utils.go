package utils

import (
	"crypto/md5"
	"crypto/rand"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"math/big"
	"os"
	"reflect"
	"regexp"
	"strconv"
	"strings"
	"time"
)

// GenerateRandomString 生成指定长度的随机字符串
func GenerateRandomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	result := make([]byte, length)
	charsetLength := big.NewInt(int64(len(charset)))

	for i := 0; i < length; i++ {
		n, _ := rand.Int(rand.Reader, charsetLength)
		result[i] = charset[n.Int64()]
	}

	return string(result)
}

// GenerateID 生成带前缀的唯一ID
func GenerateID(prefix string) string {
	timestamp := time.Now().Format("20060102150405")
	random := GenerateRandomString(6)
	return fmt.Sprintf("%s%s%s", prefix, timestamp, random)
}

// StructToMap 将结构体转换为map
func StructToMap(obj interface{}) (map[string]interface{}, error) {
	data, err := json.Marshal(obj)
	if err != nil {
		return nil, err
	}

	var result map[string]interface{}
	if err := json.Unmarshal(data, &result); err != nil {
		return nil, err
	}

	return result, nil
}

// FileExists 检查文件是否存在
func FileExists(path string) bool {
	_, err := os.Stat(path)
	return !os.IsNotExist(err)
}

// ParseBool 解析字符串为布尔值，支持更多的格式
func ParseBool(str string) (bool, error) {
	if str == "" {
		return false, nil
	}

	// 直接转换
	if b, err := strconv.ParseBool(str); err == nil {
		return b, nil
	}

	// 支持更多的格式
	str = strings.TrimSpace(strings.ToLower(str))
	switch str {
	case "y", "yes", "on", "1", "true", "t", "enabled", "enable":
		return true, nil
	case "n", "no", "off", "0", "false", "f", "disabled", "disable":
		return false, nil
	default:
		return false, fmt.Errorf("无法解析布尔值: %s", str)
	}
}

// GetEnv 获取环境变量，如果不存在则返回默认值
func GetEnv(key, defaultValue string) string {
	if value, exists := os.LookupEnv(key); exists {
		return value
	}
	return defaultValue
}

// GetEnvAsInt 获取环境变量并转换为整数，如果不存在或转换失败则返回默认值
func GetEnvAsInt(key string, defaultValue int) int {
	valueStr := GetEnv(key, "")
	if valueStr == "" {
		return defaultValue
	}
	if value, err := strconv.Atoi(valueStr); err == nil {
		return value
	}
	return defaultValue
}

// GetEnvAsBool 获取环境变量并转换为布尔值，如果不存在或转换失败则返回默认值
func GetEnvAsBool(key string, defaultValue bool) bool {
	valueStr := GetEnv(key, "")
	if valueStr == "" {
		return defaultValue
	}
	if value, err := ParseBool(valueStr); err == nil {
		return value
	}
	return defaultValue
}

// MD5 计算字符串的MD5哈希值
func MD5(text string) string {
	hash := md5.New()
	hash.Write([]byte(text))
	return hex.EncodeToString(hash.Sum(nil))
}

// ValidateEmail 验证邮箱格式
func ValidateEmail(email string) bool {
	pattern := `^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`
	match, _ := regexp.MatchString(pattern, email)
	return match
}

// CopyFile 复制文件
func CopyFile(src, dst string) error {
	sourceFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer sourceFile.Close()

	destFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer destFile.Close()

	_, err = io.Copy(destFile, sourceFile)
	if err != nil {
		return err
	}

	return nil
}

// FieldExists 检查结构体是否存在指定字段
func FieldExists(structValue interface{}, fieldName string) bool {
	rv := reflect.ValueOf(structValue)
	if rv.Kind() == reflect.Ptr {
		rv = rv.Elem()
	}
	if rv.Kind() != reflect.Struct {
		return false
	}
	return rv.FieldByName(fieldName).IsValid()
}

// FormatTime 格式化时间
func FormatTime(t time.Time) string {
	return t.Format("2006-01-02 15:04:05")
}

// StringsContains 检查字符串切片是否包含某个字符串
func StringsContains(slice []string, str string) bool {
	for _, item := range slice {
		if item == str {
			return true
		}
	}
	return false
}

// MapToJSON 将map转换为JSON字符串
func MapToJSON(m map[string]interface{}) (string, error) {
	jsonData, err := json.Marshal(m)
	if err != nil {
		return "", err
	}
	return string(jsonData), nil
}

// JSONToMap 将JSON字符串转换为map
func JSONToMap(jsonStr string) (map[string]interface{}, error) {
	var result map[string]interface{}
	err := json.Unmarshal([]byte(jsonStr), &result)
	return result, err
}
