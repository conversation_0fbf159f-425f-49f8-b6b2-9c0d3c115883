package server

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/devops-common/pkg/common/config"
	"github.com/devops-common/pkg/common/middleware"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// Server 表示HTTP服务器
type Server struct {
	Router *gin.Engine
	Logger *logrus.Logger
	Config *config.ServerConfig
	srv    *http.Server
}

// NewServer 创建新的服务器实例
func NewServer(cfg *config.ServerConfig, logger *logrus.Logger) *Server {
	// 设置Gin模式
	gin.SetMode(cfg.Mode)

	// 创建路由
	r := gin.New()

	// 应用通用中间件
	middleware.InitMiddlewares(r, logger)

	return &Server{
		Router: r,
		Logger: logger,
		Config: cfg,
	}
}

// Start 启动HTTP服务器
func (s *Server) Start() {
	// 创建HTTP服务器
	s.srv = &http.Server{
		Addr:         fmt.Sprintf("%s:%d", s.Config.Host, s.Config.Port),
		Handler:      s.Router,
		ReadTimeout:  time.Duration(s.Config.ReadTimeout) * time.Second,
		WriteTimeout: time.Duration(s.Config.WriteTimeout) * time.Second,
		IdleTimeout:  time.Duration(s.Config.IdleTimeout) * time.Second,
	}

	// 添加健康检查路由
	s.Router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status": "ok",
			"time":   time.Now().Format(time.RFC3339),
		})
	})

	// 在goroutine中启动服务器
	go func() {
		s.Logger.Infof("🌐 服务启动在 %s:%d", s.Config.Host, s.Config.Port)
		s.Logger.Infof("🔗 健康检查: http://%s:%d/health", s.Config.Host, s.Config.Port)

		if err := s.srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			s.Logger.Fatalf("❌ 启动服务器失败: %v", err)
		}
	}()

	// 优雅关闭
	s.gracefulShutdown()
}

// gracefulShutdown 优雅关闭服务器
func (s *Server) gracefulShutdown() {
	// 等待中断信号
	quit := make(chan os.Signal, 1)
	// 监听 SIGINT (Ctrl+C) 和 SIGTERM (kill) 信号
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	s.Logger.Info("🛑 正在关闭服务...")

	// 创建一个5秒的上下文用于超时控制
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 尝试优雅关闭服务器
	if err := s.srv.Shutdown(ctx); err != nil {
		s.Logger.Fatalf("❌ 服务器强制关闭: %v", err)
	}

	s.Logger.Info("✅ 服务已安全关闭")
}

// GetRouter 获取Gin路由实例
func (s *Server) GetRouter() *gin.Engine {
	return s.Router
}

// GetLogger 获取日志实例
func (s *Server) GetLogger() *logrus.Logger {
	return s.Logger
}
