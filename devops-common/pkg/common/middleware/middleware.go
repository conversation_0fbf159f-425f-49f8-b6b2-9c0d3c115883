package middleware

import (
	"fmt"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// LoggerMiddleware 返回一个Gin日志中间件
func LoggerMiddleware(log *logrus.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 开始时间
		start := time.Now()

		// 处理请求
		c.Next()

		// 结束时间
		end := time.Now()

		// 执行时间
		latency := end.Sub(start)

		// 获取请求信息
		path := c.Request.URL.Path
		method := c.Request.Method
		clientIP := c.ClientIP()
		statusCode := c.Writer.Status()

		// 只记录非健康检查的请求
		if path != "/health" && path != "/ping" {
			log.WithFields(logrus.Fields{
				"status":  statusCode,
				"latency": latency,
				"method":  method,
				"path":    path,
				"ip":      clientIP,
				"time":    end.Format("2006-01-02 15:04:05"),
			}).Info("API请求")
		}
	}
}

// CORSMiddleware 返回一个处理跨域请求的中间件
func CORSMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, PATCH, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")
		c.Header("Access-Control-Expose-Headers", "Content-Length")
		c.Header("Access-Control-Allow-Credentials", "true")

		// 处理预检请求
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	}
}

// RecoveryMiddleware 返回一个恢复中间件，用于处理panic
func RecoveryMiddleware(log *logrus.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			if err := recover(); err != nil {
				// 记录错误
				log.Errorf("服务发生异常: %v", err)

				// 返回错误响应
				c.JSON(500, gin.H{
					"code": 500,
					"msg":  fmt.Sprintf("服务器内部错误: %v", err),
				})

				// 中止后续中间件
				c.Abort()
			}
		}()

		c.Next()
	}
}

// RequestIDMiddleware 为每个请求生成唯一ID
func RequestIDMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 生成请求ID，如果请求头中已有则使用现有ID
		requestID := c.GetHeader("X-Request-ID")
		if requestID == "" {
			requestID = fmt.Sprintf("%d", time.Now().UnixNano())
			c.Request.Header.Set("X-Request-ID", requestID)
		}

		// 设置响应头
		c.Header("X-Request-ID", requestID)

		// 设置到上下文
		c.Set("RequestID", requestID)

		c.Next()
	}
}

// InitMiddlewares 初始化通用中间件
func InitMiddlewares(r *gin.Engine, log *logrus.Logger) {
	// 添加通用中间件
	r.Use(RecoveryMiddleware(log))
	r.Use(LoggerMiddleware(log))
	r.Use(CORSMiddleware())
	r.Use(RequestIDMiddleware())
}
