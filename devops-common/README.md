# DevOps微服务平台公共组件

这个仓库包含了DevOps微服务平台所使用的公共组件，用于简化各个微服务的开发和维护。

## 主要功能

这个公共组件库提供了以下功能：

- **配置管理**：统一的配置加载和验证
- **日志处理**：统一的日志格式和输出
- **中间件**：通用的Gin中间件（日志、CORS、错误处理等）
- **响应处理**：统一的API响应格式
- **数据库操作**：简化的数据库连接和操作
- **服务器**：标准化的HTTP服务器启动和优雅关闭
- **工具函数**：常用的辅助函数和加密工具

## 使用方法

### 1. 配置管理

```go
import (
	"github.com/devops-common/pkg/common/config"
	"github.com/sirupsen/logrus"
)

// 自定义配置结构体，嵌入基础配置
type MyServiceConfig struct {
	config.BaseConfig
	// 自定义配置项
	Custom CustomConfig `mapstructure:"custom"`
}

// 加载配置
func main() {
	var cfg MyServiceConfig
	if err := config.LoadConfig("", "myservice", &cfg); err != nil {
		panic(err)
	}
	
	// 验证基础配置
	if err := config.ValidateBaseConfig(&cfg.BaseConfig); err != nil {
		panic(err)
	}
	
	// 使用配置...
}
```

### 2. 日志处理

```go
import (
	"github.com/devops-common/pkg/common/logger"
)

func main() {
	// 创建日志实例
	log := logger.NewLogger(&cfg.Log, "myservice")
	
	log.Info("服务启动")
	log.WithFields(logrus.Fields{
		"module": "api",
	}).Debug("调试信息")
}
```

### 3. HTTP服务器

```go
import (
	"github.com/devops-common/pkg/common/server"
)

func main() {
	// 创建服务器
	srv := server.NewServer(&cfg.Server, log)
	
	// 注册路由
	router := srv.GetRouter()
	router.GET("/api/test", func(c *gin.Context) {
		c.JSON(200, gin.H{"message": "hello"})
	})
	
	// 启动服务器（阻塞，包含优雅关闭）
	srv.Start()
}
```

### 4. 数据库操作

```go
import (
	"github.com/devops-common/pkg/common/db"
)

func main() {
	// 创建数据库实例
	database, err := db.NewDatabase(&cfg.Database, log)
	if err != nil {
		log.Fatalf("初始化数据库失败: %v", err)
	}
	
	// 迁移表结构
	if err := database.AutoMigrate(&User{}, &Role{}); err != nil {
		log.Fatalf("数据库迁移失败: %v", err)
	}
	
	// 使用事务
	if err := database.Transaction(func(tx *gorm.DB) error {
		// 事务操作...
		return nil
	}); err != nil {
		log.Errorf("事务执行失败: %v", err)
	}
}
```

### 5. 统一响应

```go
import (
	"github.com/devops-common/pkg/common/response"
)

func GetUserHandler(c *gin.Context) {
	user, err := userService.GetUser(c.Param("id"))
	if err != nil {
		response.ServerError(c, err.Error())
		return
	}
	
	if user == nil {
		response.NotFound(c, "用户不存在")
		return
	}
	
	response.Success(c, user)
}
```

### 6. 工具函数

```go
import (
	"github.com/devops-common/pkg/common/utils"
)

func main() {
	// 生成随机字符串
	randomStr := utils.GenerateRandomString(10)
	
	// 生成唯一ID
	id := utils.GenerateID("ORDER")
	
	// 检查文件是否存在
	if utils.FileExists("/path/to/file") {
		// 处理文件...
	}
	
	// 验证邮箱
	if utils.ValidateEmail("<EMAIL>") {
		// 有效邮箱...
	}
	
	// 加密解密数据
	crypto, err := utils.NewEncryptedField([]string{"your-key"}, "salt", "info")
	if err != nil {
		log.Fatalf("初始化加密工具失败: %v", err)
	}
	
	encrypted, _ := crypto.EncryptString("sensitive data")
	decrypted, _ := crypto.DecryptString(encrypted)
}
```

## 目录结构

```
pkg/
└── common/
    ├── config/     - 配置管理
    ├── logger/     - 日志处理
    ├── middleware/ - 中间件
    ├── response/   - 响应处理
    ├── db/         - 数据库操作
    ├── server/     - HTTP服务器
    └── utils/      - 工具函数
```

## 依赖

- Go 1.20+
- Gin Web框架
- GORM ORM库
- Logrus日志库
- Viper配置库
- Fernet加密库

## 贡献

欢迎提交PR和Issue来完善这个公共组件库。

