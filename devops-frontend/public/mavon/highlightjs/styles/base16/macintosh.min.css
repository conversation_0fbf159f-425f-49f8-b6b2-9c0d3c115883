/*!
  Theme: Macintosh
  Author: <PERSON> (http://www.kreativekorp.com)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/pre code.hljs{display:block;overflow-x:auto;padding:1em}code.hljs{padding:3px 5px}.hljs{color:silver;background:#000}.hljs ::selection,.hljs::selection{background-color:#404040;color:silver}.hljs-comment,.hljs-tag{color:grey}.hljs-operator,.hljs-punctuation,.hljs-subst{color:silver}.hljs-operator{opacity:.7}.hljs-bullet,.hljs-deletion,.hljs-name,.hljs-selector-tag,.hljs-template-variable,.hljs-variable{color:#dd0907}.hljs-attr,.hljs-link,.hljs-literal,.hljs-number,.hljs-symbol,.hljs-variable.constant_{color:#ff6403}.hljs-class .hljs-title,.hljs-title,.hljs-title.class_{color:#fbf305}.hljs-strong{font-weight:700;color:#fbf305}.hljs-addition,.hljs-code,.hljs-string,.hljs-title.class_.inherited__{color:#1fb714}.hljs-built_in,.hljs-doctag,.hljs-keyword.hljs-atrule,.hljs-quote,.hljs-regexp{color:#02abea}.hljs-attribute,.hljs-function .hljs-title,.hljs-section,.hljs-title.function_,.ruby .hljs-property{color:#0000d3}.diff .hljs-meta,.hljs-keyword,.hljs-template-tag,.hljs-type{color:#4700a5}.hljs-emphasis{color:#4700a5;font-style:italic}.hljs-meta,.hljs-meta .hljs-keyword,.hljs-meta .hljs-string{color:#90713a}.hljs-meta .hljs-keyword,.hljs-meta-keyword{font-weight:700}