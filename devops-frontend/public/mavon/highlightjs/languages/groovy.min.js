/*! `groovy` grammar compiled for Highlight.js 11.3.1 */
(()=>{var e=(()=>{"use strict";function e(e,n={}){return n.variants=e,n}
return n=>{
const a=n.regex,t="[A-Za-z0-9_$]+",s=e([n.C_LINE_COMMENT_MODE,n.C_BLOCK_COMMENT_MODE,n.COMMENT("/\\*\\*","\\*/",{
relevance:0,contains:[{begin:/\w+@/,relevance:0},{className:"doctag",
begin:"@[A-Za-z]+"}]})]),r={className:"regexp",begin:/~?\/[^\/\n]+\//,
contains:[n.BACKSLASH_ESCAPE]
},i=e([n.BINARY_NUMBER_MODE,n.C_NUMBER_MODE]),l=e([{begin:/"""/,end:/"""/},{
begin:/'''/,end:/'''/},{begin:"\\$/",end:"/\\$",relevance:10
},n.APOS_STRING_MODE,n.QUOTE_STRING_MODE],{className:"string"});return{
name:"Groovy",keywords:{built_in:"this super",literal:"true false null",
keyword:"byte short char int long boolean float double void def as in assert trait abstract static volatile transient public private protected synchronized final class interface enum if else for while switch case break default continue throw throws try catch finally implements extends new import package return instanceof"
},contains:[n.SHEBANG({binary:"groovy",relevance:10}),s,l,r,i,{
className:"class",beginKeywords:"class interface trait enum",end:/\{/,
illegal:":",contains:[{beginKeywords:"extends implements"
},n.UNDERSCORE_TITLE_MODE]},{className:"meta",begin:"@[A-Za-z]+",relevance:0},{
className:"attr",begin:t+"[ \t]*:",relevance:0},{begin:/\?/,end:/:/,relevance:0,
contains:[s,l,r,i,"self"]},{className:"symbol",
begin:"^[ \t]*"+a.lookahead(t+":"),excludeBegin:!0,end:t+":",relevance:0}],
illegal:/#|<\//}}})();hljs.registerLanguage("groovy",e)})();