# Harbor URL和项目名称修复说明

## 问题概述

在前端请求 `builds.POST("/:app_info_id/ci", api.BuildCI)` 时，Tekton的 `check-harbor-project` step 出现了以下问题：

### 🐛 原始问题

1. **镜像名称包含协议前缀**：
   ```
   IMAGE=https://harbor.fundpark.com/project-5/gonotice:ae4b8548-20250523110406
   ```

2. **项目名称解析失败**：
   ```bash
   echo https://harbor.fundpark.com/project-5/gonotice:ae4b8548-20250523110406 | cut -d/ -f2
   PROJECT_NAME=  # 空值
   ```

3. **Harbor URL解析错误**：
   ```bash
   echo https://harbor.fundpark.com/project-5/gonotice:ae4b8548-20250523110406 | cut -d/ -f1
   HARBOR_URL=https:  # 缺少域名
   ```

4. **最终的API请求错误**：
   ```bash
   curl ... https://https:/api/v2.0/projects/
   HTTP_CODE=000  # 失败
   ```

5. **数据库查询错误**：
   ```
   查询MicroApp(ID=12)失败: failed to encode args[0]: unable to encode 0xc into text format
   ```

## 🔧 修复方案

### 1. 修复镜像名称格式

**修改前**：
```go
// 可能生成包含协议前缀的镜像名称
imageName := fmt.Sprintf("https://%s/project-5/%s:%s", harborHost, app.Name, imageTag)
```

**修改后**：
```go
// 清理Harbor URL，移除协议前缀
harborHost := s.harborURL
harborHost = strings.TrimPrefix(harborHost, "http://")
harborHost = strings.TrimPrefix(harborHost, "https://")
harborHost = strings.TrimSuffix(harborHost, "/")

// 构建正确的镜像名称（不包含协议前缀）
imageName := fmt.Sprintf("%s/%s/%s:%s", harborHost, projectName, app.Name, imageTag)
```

### 2. 修复项目名称格式

**修改前**：
```
项目名称: project-5 (不符合规范)
```

**修改后**：
```go
// 项目名称格式: environmentName.lower() + "-" + productName.lower()
projectName := fmt.Sprintf("%s-%s", 
    strings.ToLower(environment.Name), 
    strings.ToLower(productName))
```

### 3. 修复数据库查询问题

**修改前**：
```go
// 使用复杂的Preload可能导致编码错误
if err := s.db.Preload("Project.Product").First(&microApp, appID).Error; err != nil {
```

**修改后**：
```go
// 分步加载，避免复杂的关联查询
if err := s.db.First(&microApp, appID).Error; err != nil {
    // 处理错误
}

// 分别加载Project和Product信息
if microApp.ProjectID > 0 {
    var project models.Project
    if err := s.db.First(&project, microApp.ProjectID).Error; err == nil {
        microApp.Project = project
        
        if project.ProductID > 0 {
            var product models.Product
            if err := s.db.First(&product, project.ProductID).Error; err == nil {
                microApp.Project.Product = product
            }
        }
    }
}
```

### 4. 添加安全的默认值处理

```go
// 获取产品信息（已经通过分步加载）
productName := "default"
if app.Project.Product.Name != "" {
    productName = app.Project.Product.Name
} else {
    s.logger.Warnf("应用 %s 的产品信息为空，使用默认值: %s", app.Name, productName)
}
```

## ✅ 修复结果

### 正确的镜像名称格式
```
harbor.fundpark.com/dev-myproduct/gonotice:ae4b8548-20250523110406
```

### 正确的Harbor API调用
```bash
# 项目名称解析
PROJECT_NAME=dev-myproduct  ✅

# Harbor URL解析  
HARBOR_URL=harbor.fundpark.com  ✅

# API请求
curl -u admin:password https://harbor.fundpark.com/api/v2.0/projects/dev-myproduct
HTTP_CODE=200  ✅
```

## 🧪 验证工具

添加了多个测试工具来验证修复：

1. **镜像名称构造测试**：
   ```bash
   make test-image-name
   ```

2. **Harbor解析逻辑测试**：
   ```bash
   make test-harbor-parsing
   ```

3. **完整编译验证**：
   ```bash
   make build
   ```

## 📋 项目名称规范

现在项目名称严格遵循以下格式：
```
{environment_name.lower()}-{product_name.lower()}
```

示例：
- `dev-myproduct` (开发环境的MyProduct产品)
- `test-anotherproduct` (测试环境的AnotherProduct产品)  
- `prod-productname` (生产环境的ProductName产品)

## 🚀 使用说明

修复后的系统能够：

1. ✅ 正确生成不含协议前缀的镜像名称
2. ✅ 按规范格式生成项目名称
3. ✅ 正确解析Harbor URL和项目名称
4. ✅ 成功调用Harbor API检查项目
5. ✅ 避免数据库查询错误
6. ✅ 提供安全的默认值处理

这些修复确保了CI/CD流水线能够正常运行，Harbor项目检查能够成功执行。 