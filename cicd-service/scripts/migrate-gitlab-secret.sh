#!/bin/bash

# GitLab Secret格式迁移脚本
# 将旧格式 (.git-credentials, .gitconfig) 迁移到新格式 (username, password, token)

set -e

echo "🔄 GitLab Secret格式迁移脚本"
echo "==============================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查旧Secret
check_old_secret() {
    echo -e "${BLUE}📋 检查现有Secret格式...${NC}"
    
    if ! kubectl get secret gitlab-auth >/dev/null 2>&1; then
        echo -e "${RED}❌ Secret 'gitlab-auth' 不存在${NC}"
        return 1
    fi
    
    # 检查是否是旧格式
    if kubectl get secret gitlab-auth -o jsonpath='{.data}' | grep -q "\.git-credentials"; then
        echo -e "${YELLOW}⚠️  检测到旧格式Secret（.git-credentials格式）${NC}"
        return 0
    elif kubectl get secret gitlab-auth -o jsonpath='{.data}' | grep -q "username"; then
        echo -e "${GREEN}✅ 已是新格式Secret${NC}"
        return 2
    else
        echo -e "${RED}❌ 未知的Secret格式${NC}"
        return 1
    fi
}

# 解析旧格式认证信息
parse_old_credentials() {
    echo -e "${BLUE}🔍 解析旧格式认证信息...${NC}"
    
    # 获取.git-credentials内容
    GIT_CREDENTIALS=$(kubectl get secret gitlab-auth -o jsonpath='{.data\.git-credentials}' | base64 -d)
    
    if [ -z "$GIT_CREDENTIALS" ]; then
        echo -e "${RED}❌ 无法获取.git-credentials内容${NC}"
        return 1
    fi
    
    echo -e "${BLUE}📄 .git-credentials内容: ${GIT_CREDENTIALS}${NC}"
    
    # 解析URL格式: ******************************
    if [[ "$GIT_CREDENTIALS" =~ https://([^:]+):([^@]+)@([^/]+) ]]; then
        EXTRACTED_USERNAME="${BASH_REMATCH[1]}"
        EXTRACTED_PASSWORD="${BASH_REMATCH[2]}"
        EXTRACTED_HOST="${BASH_REMATCH[3]}"
        
        echo -e "${GREEN}✅ 成功解析认证信息:${NC}"
        echo -e "  用户名: ${EXTRACTED_USERNAME}"
        echo -e "  密码: ${EXTRACTED_PASSWORD:0:4}****${EXTRACTED_PASSWORD: -2}"
        echo -e "  主机: ${EXTRACTED_HOST}"
        
        return 0
    else
        echo -e "${RED}❌ 无法解析.git-credentials格式${NC}"
        return 1
    fi
}

# 创建新格式Secret
create_new_secret() {
    echo -e "${BLUE}🔧 创建新格式Secret...${NC}"
    
    # 确认是否要迁移
    echo -e "${YELLOW}即将使用以下信息创建新格式Secret:${NC}"
    echo -e "  用户名: ${EXTRACTED_USERNAME}"
    echo -e "  密码: ${EXTRACTED_PASSWORD:0:4}****${EXTRACTED_PASSWORD: -2}"
    echo -e "  Token: [空] (如需要可后续添加)"
    echo
    
    read -p "确认迁移? (y/n): " confirm
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        echo -e "${YELLOW}❌ 迁移已取消${NC}"
        return 1
    fi
    
    # 备份旧Secret
    echo -e "${BLUE}💾 备份旧Secret...${NC}"
    kubectl get secret gitlab-auth -o yaml > gitlab-auth-backup-$(date +%Y%m%d-%H%M%S).yaml
    echo -e "${GREEN}✅ 备份保存完成${NC}"
    
    # 创建新格式Secret
    echo -e "${BLUE}🔑 创建新格式Secret...${NC}"
    kubectl create secret generic gitlab-auth \
        --from-literal=username="$EXTRACTED_USERNAME" \
        --from-literal=password="$EXTRACTED_PASSWORD" \
        --from-literal=token="" \
        --dry-run=client -o yaml | kubectl apply -f -
    
    echo -e "${GREEN}✅ 新格式Secret创建完成${NC}"
}

# 验证新Secret
verify_new_secret() {
    echo -e "${BLUE}🧪 验证新Secret格式...${NC}"
    
    # 检查key是否存在
    for key in username password token; do
        if kubectl get secret gitlab-auth -o jsonpath="{.data.${key}}" >/dev/null 2>&1; then
            value=$(kubectl get secret gitlab-auth -o jsonpath="{.data.${key}}" | base64 -d)
            if [ -n "$value" ]; then
                echo -e "  ${key}: ${GREEN}[已设置]${NC}"
            else
                echo -e "  ${key}: ${YELLOW}[空值]${NC}"
            fi
        else
            echo -e "  ${key}: ${RED}[不存在]${NC}"
        fi
    done
    
    echo -e "${GREEN}✅ Secret格式验证完成${NC}"
}

# 测试新Secret
test_new_secret() {
    echo -e "${BLUE}🚀 测试新Secret...${NC}"
    
    # 创建测试Pod
    cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: Pod
metadata:
  name: test-new-gitlab-auth
spec:
  restartPolicy: Never
  containers:
  - name: test
    image: alpine/git:2.40.1
    env:
    - name: GIT_USERNAME
      valueFrom:
        secretKeyRef:
          name: gitlab-auth
          key: username
          optional: true
    - name: GIT_PASSWORD
      valueFrom:
        secretKeyRef:
          name: gitlab-auth
          key: password
          optional: true
    - name: GIT_TOKEN
      valueFrom:
        secretKeyRef:
          name: gitlab-auth
          key: token
          optional: true
    command: ["/bin/sh"]
    args:
    - -c
    - |
      echo "🔍 检查环境变量映射:"
      echo "  GIT_TOKEN: \${GIT_TOKEN:+[设置]} \${GIT_TOKEN:-[未设置]}"
      echo "  GIT_USERNAME: \${GIT_USERNAME:+[设置]} \${GIT_USERNAME:-[未设置]}"
      echo "  GIT_PASSWORD: \${GIT_PASSWORD:+[设置]} \${GIT_PASSWORD:-[未设置]}"
      
      if [ -n "\${GIT_USERNAME}" ] && [ -n "\${GIT_PASSWORD}" ]; then
        echo "✅ 认证信息映射成功!"
        echo "🧪 测试git ls-remote..."
        git ls-remote https://\${GIT_USERNAME}:\${GIT_PASSWORD}@source.fundpark.com/devops/gonotice.git HEAD
        echo "🎉 认证测试成功!"
      else
        echo "❌ 认证信息映射失败"
        exit 1
      fi
EOF

    echo -e "${BLUE}⏳ 等待测试Pod运行...${NC}"
    sleep 5
    
    echo -e "${BLUE}📋 测试结果:${NC}"
    kubectl logs test-new-gitlab-auth
    
    # 清理测试Pod
    kubectl delete pod test-new-gitlab-auth
}

# 主程序
main() {
    echo -e "${BLUE}🔍 开始Secret格式检查...${NC}"
    
    check_old_secret
    result=$?
    
    case $result in
        0)
            # 旧格式，需要迁移
            echo -e "${YELLOW}🔄 开始迁移流程...${NC}"
            
            if parse_old_credentials; then
                create_new_secret
                verify_new_secret
                test_new_secret
                echo -e "${GREEN}🎉 Secret迁移完成！${NC}"
                echo
                echo -e "${BLUE}📝 后续步骤:${NC}"
                echo "1. 部署修复后的gitlab-clone Task: kubectl apply -f k8s/tekton/gitlab-clone-task.yaml"
                echo "2. 测试Pipeline运行"
                echo "3. 如需GitLab Personal Access Token，可更新Secret中的token字段"
            else
                echo -e "${RED}❌ 迁移失败${NC}"
                exit 1
            fi
            ;;
        2)
            # 新格式
            echo -e "${GREEN}✅ Secret已是新格式，无需迁移${NC}"
            verify_new_secret
            echo
            echo -e "${BLUE}💡 如果仍有问题，请检查:${NC}"
            echo "1. Secret是否在正确的namespace"
            echo "2. gitlab-clone Task是否已更新"
            echo "3. 运行认证测试: make test-gitlab-auth"
            ;;
        *)
            # 错误情况
            echo -e "${RED}❌ Secret检查失败${NC}"
            exit 1
            ;;
    esac
}

# 如果直接运行脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi 