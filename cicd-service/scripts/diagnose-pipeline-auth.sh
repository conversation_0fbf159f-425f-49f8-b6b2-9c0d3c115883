#!/bin/bash

# PipelineRun认证问题诊断脚本

set -e

echo "🔍 PipelineRun认证问题诊断"
echo "=========================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 1. 检查Secret状态
check_secret() {
    echo -e "${BLUE}📋 1. 检查Secret状态${NC}"
    
    if kubectl get secret gitlab-auth >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Secret存在${NC}"
        
        # 检查字段
        for key in username password token; do
            if kubectl get secret gitlab-auth -o jsonpath="{.data.${key}}" >/dev/null 2>&1; then
                value=$(kubectl get secret gitlab-auth -o jsonpath="{.data.${key}}" | base64 -d)
                if [ -n "$value" ]; then
                    if [ "$key" = "password" ] || [ "$key" = "token" ]; then
                        echo -e "  ${key}: ${GREEN}[已设置]${NC}"
                    else
                        echo -e "  ${key}: ${GREEN}${value}${NC}"
                    fi
                else
                    echo -e "  ${key}: ${YELLOW}[空值]${NC}"
                fi
            else
                echo -e "  ${key}: ${RED}[缺失]${NC}"
            fi
        done
    else
        echo -e "${RED}❌ Secret不存在${NC}"
        return 1
    fi
    echo
}

# 2. 检查Task版本
check_task() {
    echo -e "${BLUE}📋 2. 检查gitlab-clone Task${NC}"
    
    if kubectl get task gitlab-clone >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Task存在${NC}"
        
        # 检查Task中的环境变量配置
        echo -e "${BLUE}🔍 检查环境变量配置:${NC}"
        kubectl get task gitlab-clone -o yaml | grep -A 20 "env:" | grep -E "(name:|valueFrom:|secretKeyRef:|key:)" || echo "未找到环境变量配置"
    else
        echo -e "${RED}❌ gitlab-clone Task不存在${NC}"
        return 1
    fi
    echo
}

# 3. 检查最近的PipelineRun
check_pipelinerun() {
    echo -e "${BLUE}📋 3. 检查最近的PipelineRun${NC}"
    
    LATEST_PIPELINERUN=$(kubectl get pipelinerun --sort-by=.metadata.creationTimestamp -o jsonpath='{.items[-1].metadata.name}' 2>/dev/null)
    
    if [ -n "$LATEST_PIPELINERUN" ]; then
        echo -e "${GREEN}✅ 找到PipelineRun: ${LATEST_PIPELINERUN}${NC}"
        
        # 检查workspace配置
        echo -e "${BLUE}🔍 检查workspace配置:${NC}"
        kubectl get pipelinerun "$LATEST_PIPELINERUN" -o yaml | grep -A 10 "workspaces:" | head -15
        
        # 检查状态
        echo -e "${BLUE}🔍 检查运行状态:${NC}"
        kubectl get pipelinerun "$LATEST_PIPELINERUN" -o jsonpath='{.status.conditions[0].message}' 2>/dev/null || echo "无状态信息"
    else
        echo -e "${YELLOW}⚠️  未找到PipelineRun${NC}"
    fi
    echo
}

# 4. 创建测试Pod验证环境变量映射
test_env_mapping() {
    echo -e "${BLUE}📋 4. 创建测试Pod验证环境变量映射${NC}"
    
    cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: Pod
metadata:
  name: test-gitlab-auth-env
spec:
  restartPolicy: Never
  containers:
  - name: test
    image: alpine:3.18
    env:
    - name: GIT_USERNAME
      valueFrom:
        secretKeyRef:
          name: gitlab-auth
          key: username
          optional: true
    - name: GIT_PASSWORD
      valueFrom:
        secretKeyRef:
          name: gitlab-auth
          key: password
          optional: true
    - name: GIT_TOKEN
      valueFrom:
        secretKeyRef:
          name: gitlab-auth
          key: token
          optional: true
    command: ["/bin/sh"]
    args:
    - -c
    - |
      echo "🔍 环境变量测试结果:"
      echo "  GIT_TOKEN: \${GIT_TOKEN:+[设置]} \${GIT_TOKEN:-[未设置]}"
      echo "  GIT_USERNAME: \${GIT_USERNAME:+[设置]} \${GIT_USERNAME:-[未设置]}"
      echo "  GIT_PASSWORD: \${GIT_PASSWORD:+[设置]} \${GIT_PASSWORD:-[未设置]}"
      
      if [ -n "\${GIT_USERNAME}" ] && [ -n "\${GIT_PASSWORD}" ]; then
        echo "✅ 环境变量映射成功!"
      else
        echo "❌ 环境变量映射失败"
        echo "调试信息:"
        echo "  USERNAME值: '\${GIT_USERNAME}'"
        echo "  PASSWORD值长度: \${#GIT_PASSWORD}"
        echo "  TOKEN值: '\${GIT_TOKEN}'"
      fi
EOF

    echo -e "${BLUE}⏳ 等待测试Pod运行...${NC}"
    sleep 5
    
    echo -e "${BLUE}📋 测试结果:${NC}"
    kubectl logs test-gitlab-auth-env 2>/dev/null || echo "Pod日志获取失败"
    
    # 清理测试Pod
    kubectl delete pod test-gitlab-auth-env >/dev/null 2>&1 || true
    echo
}

# 5. 检查namespace问题
check_namespace() {
    echo -e "${BLUE}📋 5. 检查namespace配置${NC}"
    
    SECRET_NS=$(kubectl get secret gitlab-auth -o jsonpath='{.metadata.namespace}' 2>/dev/null || echo "未找到")
    echo -e "Secret namespace: ${SECRET_NS}"
    
    if kubectl get task gitlab-clone >/dev/null 2>&1; then
        TASK_NS=$(kubectl get task gitlab-clone -o jsonpath='{.metadata.namespace}' 2>/dev/null || echo "未找到")
        echo -e "Task namespace: ${TASK_NS}"
    fi
    
    echo -e "当前默认namespace: $(kubectl config view --minify -o jsonpath='{..namespace}' 2>/dev/null || echo 'default')"
    echo
}

# 6. 提供修复建议
provide_fixes() {
    echo -e "${BLUE}📋 6. 修复建议${NC}"
    
    echo -e "${YELLOW}如果环境变量映射失败，请尝试以下修复:${NC}"
    echo
    echo -e "${GREEN}修复1: 重新部署gitlab-clone Task${NC}"
    echo "kubectl apply -f k8s/tekton/gitlab-clone-task.yaml"
    echo
    echo -e "${GREEN}修复2: 确保Secret格式正确${NC}"
    echo "kubectl create secret generic gitlab-auth \\"
    echo "  --from-literal=username=\"git\" \\"
    echo "  --from-literal=password=\"T-_xS8UP5yWmA8H2pk5s\" \\"
    echo "  --from-literal=token=\"\" \\"
    echo "  --dry-run=client -o yaml | kubectl apply -f -"
    echo
    echo -e "${GREEN}修复3: 检查PipelineRun的workspace配置${NC}"
    echo "确保PipelineRun包含以下workspace配置:"
    echo "workspaces:"
    echo "- name: gitlab-credentials"
    echo "  secret:"
    echo "    secretName: gitlab-auth"
    echo
    echo -e "${GREEN}修复4: 使用部署脚本重新配置${NC}"
    echo "make deploy-gitlab-secret"
}

# 主程序
main() {
    check_secret
    check_task  
    check_pipelinerun
    test_env_mapping
    check_namespace
    provide_fixes
    
    echo -e "${GREEN}🎯 诊断完成！${NC}"
    echo -e "${BLUE}如需进一步帮助，请运行: make test-gitlab-auth${NC}"
}

# 如果直接运行脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi 