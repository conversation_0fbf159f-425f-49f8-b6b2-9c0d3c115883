#!/bin/bash

# GitLab Secret部署脚本
# 帮助用户选择和部署合适的GitLab认证配置

set -e

echo "🚀 GitLab Secret部署脚本"
echo "========================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 显示当前Secret状态
show_current_secret() {
    echo -e "${BLUE}📋 当前Secret状态:${NC}"
    
    if kubectl get secret gitlab-auth >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Secret 'gitlab-auth' 存在${NC}"
        
        # 检查字段
        for key in username password token; do
            if kubectl get secret gitlab-auth -o jsonpath="{.data.${key}}" >/dev/null 2>&1; then
                value=$(kubectl get secret gitlab-auth -o jsonpath="{.data.${key}}" | base64 -d)
                if [ -n "$value" ]; then
                    if [ "$key" = "password" ] || [ "$key" = "token" ]; then
                        echo -e "  ${key}: ${GREEN}[已设置]${NC}"
                    else
                        echo -e "  ${key}: ${GREEN}${value}${NC}"
                    fi
                else
                    echo -e "  ${key}: ${YELLOW}[空值]${NC}"
                fi
            else
                echo -e "  ${key}: ${RED}[不存在]${NC}"
            fi
        done
    else
        echo -e "${RED}❌ Secret 'gitlab-auth' 不存在${NC}"
    fi
    echo
}

# 部署用户名密码配置
deploy_password_auth() {
    echo -e "${BLUE}🔑 配置用户名密码认证...${NC}"
    
    read -p "请输入GitLab用户名 (默认: git): " username
    username=${username:-git}
    
    read -s -p "请输入GitLab密码: " password
    echo
    
    if [ -z "$password" ]; then
        echo -e "${RED}❌ 密码不能为空${NC}"
        return 1
    fi
    
    echo -e "${BLUE}🔧 创建用户名密码认证Secret...${NC}"
    kubectl create secret generic gitlab-auth \
        --from-literal=username="$username" \
        --from-literal=password="$password" \
        --from-literal=token="" \
        --dry-run=client -o yaml | kubectl apply -f -
    
    echo -e "${GREEN}✅ 用户名密码认证配置完成${NC}"
}

# 部署Token配置
deploy_token_auth() {
    echo -e "${BLUE}🎫 配置GitLab Personal Access Token认证...${NC}"
    
    echo -e "${YELLOW}GitLab Personal Access Token生成步骤:${NC}"
    echo "1. 登录GitLab → 用户设置 → Access Tokens"
    echo "2. 创建新Token，选择 'read_repository' 权限"
    echo "3. 复制生成的Token（格式：glpat-xxxxxxxxxxxxxxxxxxxxx）"
    echo
    
    read -s -p "请输入GitLab Personal Access Token: " token
    echo
    
    if [ -z "$token" ]; then
        echo -e "${RED}❌ Token不能为空${NC}"
        return 1
    fi
    
    # 检查Token格式
    if [[ ! "$token" =~ ^glpat- ]]; then
        echo -e "${YELLOW}⚠️  警告：Token格式不是标准的GitLab格式（应以glpat-开头）${NC}"
        read -p "是否继续? (y/n): " continue_choice
        if [[ ! "$continue_choice" =~ ^[Yy]$ ]]; then
            echo -e "${YELLOW}❌ 操作已取消${NC}"
            return 1
        fi
    fi
    
    echo -e "${BLUE}🔧 创建Token认证Secret...${NC}"
    kubectl create secret generic gitlab-auth \
        --from-literal=token="$token" \
        --from-literal=username="" \
        --from-literal=password="" \
        --dry-run=client -o yaml | kubectl apply -f -
    
    echo -e "${GREEN}✅ Token认证配置完成${NC}"
}

# 部署混合配置
deploy_hybrid_auth() {
    echo -e "${BLUE}🔀 配置混合认证（Token + 用户名密码备选）...${NC}"
    
    read -s -p "请输入GitLab Personal Access Token: " token
    echo
    
    read -p "请输入GitLab用户名 (默认: git): " username
    username=${username:-git}
    
    read -s -p "请输入GitLab密码: " password
    echo
    
    if [ -z "$token" ] || [ -z "$password" ]; then
        echo -e "${RED}❌ Token和密码都不能为空${NC}"
        return 1
    fi
    
    echo -e "${BLUE}🔧 创建混合认证Secret...${NC}"
    kubectl create secret generic gitlab-auth \
        --from-literal=token="$token" \
        --from-literal=username="$username" \
        --from-literal=password="$password" \
        --dry-run=client -o yaml | kubectl apply -f -
    
    echo -e "${GREEN}✅ 混合认证配置完成${NC}"
}

# 从YAML文件部署
deploy_from_yaml() {
    echo -e "${BLUE}📄 从YAML文件部署...${NC}"
    
    if [ -f "k8s/tekton/gitlab-auth-secret.yaml" ]; then
        echo -e "${BLUE}找到配置文件: k8s/tekton/gitlab-auth-secret.yaml${NC}"
        read -p "是否部署此文件? (y/n): " confirm
        if [[ "$confirm" =~ ^[Yy]$ ]]; then
            kubectl apply -f k8s/tekton/gitlab-auth-secret.yaml
            echo -e "${GREEN}✅ YAML文件部署完成${NC}"
        else
            echo -e "${YELLOW}❌ 部署已取消${NC}"
        fi
    else
        echo -e "${RED}❌ 找不到 k8s/tekton/gitlab-auth-secret.yaml 文件${NC}"
        return 1
    fi
}

# 测试认证
test_auth() {
    echo -e "${BLUE}🧪 测试GitLab认证...${NC}"
    
    # 使用现有的测试工具
    if command -v make >/dev/null 2>&1; then
        make test-gitlab-auth
    else
        echo -e "${YELLOW}⚠️  make命令不可用，请手动运行: go run cmd/test-gitlab-auth/main.go${NC}"
    fi
}

# 主菜单
main_menu() {
    echo -e "${BLUE}请选择认证配置方式:${NC}"
    echo "1. 用户名 + 密码认证（适用于企业内部GitLab）"
    echo "2. GitLab Personal Access Token认证（推荐）"
    echo "3. 混合认证（Token + 密码备选）"
    echo "4. 从YAML文件部署"
    echo "5. 测试当前认证配置"
    echo "6. 显示当前Secret状态"
    echo "7. 退出"
    
    read -p "请输入选项 (1-7): " choice
    
    case $choice in
        1)
            deploy_password_auth
            ;;
        2)
            deploy_token_auth
            ;;
        3)
            deploy_hybrid_auth
            ;;
        4)
            deploy_from_yaml
            ;;
        5)
            test_auth
            ;;
        6)
            show_current_secret
            ;;
        7)
            echo -e "${GREEN}👋 再见!${NC}"
            exit 0
            ;;
        *)
            echo -e "${RED}❌ 无效选项${NC}"
            main_menu
            ;;
    esac
}

# 检查依赖
check_dependencies() {
    echo -e "${BLUE}🔍 检查依赖...${NC}"
    
    if ! command -v kubectl &> /dev/null; then
        echo -e "${RED}❌ kubectl 未安装${NC}"
        exit 1
    fi
    
    # 检查Kubernetes连接
    if ! kubectl cluster-info &> /dev/null; then
        echo -e "${RED}❌ 无法连接到Kubernetes集群${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 依赖检查通过${NC}"
}

# 显示使用说明
show_usage() {
    echo -e "${BLUE}📖 使用说明:${NC}"
    echo "此脚本帮助您配置GitLab认证Secret，支持三种认证方式:"
    echo "- 用户名 + 密码：适用于企业内部GitLab"
    echo "- Personal Access Token：推荐方式，安全性更高"
    echo "- 混合模式：Token优先，密码备选"
    echo
}

# 主程序
main() {
    show_usage
    check_dependencies
    show_current_secret
    
    while true; do
        main_menu
        echo
        read -p "是否继续配置? (y/n): " continue_choice
        if [[ ! "$continue_choice" =~ ^[Yy]$ ]]; then
            echo -e "${GREEN}👋 再见!${NC}"
            break
        fi
        echo
        show_current_secret
    done
}

# 如果直接运行脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi 