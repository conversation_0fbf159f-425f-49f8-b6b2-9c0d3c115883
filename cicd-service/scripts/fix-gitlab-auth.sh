#!/bin/bash

# GitLab认证问题快速修复脚本
# 用于诊断和解决 "No such device or address" 错误

set -e

echo "🔧 GitLab认证问题诊断和修复脚本"
echo "============================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查函数
check_secret() {
    echo -e "${BLUE}📋 检查GitLab认证Secret...${NC}"
    
    if kubectl get secret gitlab-auth >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Secret 'gitlab-auth' 存在${NC}"
        
        echo -e "${BLUE}🔍 Secret内容:${NC}"
        kubectl get secret gitlab-auth -o jsonpath='{.data}' | jq -r 'keys[]' | while read key; do
            value=$(kubectl get secret gitlab-auth -o jsonpath="{.data.${key}}" | base64 -d)
            if [ "$key" = "password" ] || [ "$key" = "token" ]; then
                # 隐藏敏感信息
                if [ -n "$value" ]; then
                    echo -e "  ${key}: ${GREEN}[已设置]${NC}"
                else
                    echo -e "  ${key}: ${RED}[空值]${NC}"
                fi
            else
                echo -e "  ${key}: ${value}"
            fi
        done
    else
        echo -e "${RED}❌ Secret 'gitlab-auth' 不存在${NC}"
        return 1
    fi
}

# 创建或更新Secret
create_secret() {
    echo -e "${BLUE}🔧 创建/更新GitLab认证Secret...${NC}"
    
    read -p "请输入GitLab用户名 (默认: git): " username
    username=${username:-git}
    
    read -s -p "请输入GitLab密码或Token: " password
    echo
    
    if [ -z "$password" ]; then
        echo -e "${RED}❌ 密码/Token不能为空${NC}"
        return 1
    fi
    
    # 询问是否是Token
    read -p "这是GitLab Personal Access Token吗? (y/n): " is_token
    
    if [[ "$is_token" =~ ^[Yy]$ ]]; then
        echo -e "${BLUE}🔑 创建Token认证Secret...${NC}"
        kubectl create secret generic gitlab-auth \
            --from-literal=token="$password" \
            --from-literal=username="" \
            --from-literal=password="" \
            --dry-run=client -o yaml | kubectl apply -f -
    else
        echo -e "${BLUE}🔑 创建用户名密码认证Secret...${NC}"
        kubectl create secret generic gitlab-auth \
            --from-literal=username="$username" \
            --from-literal=password="$password" \
            --from-literal=token="" \
            --dry-run=client -o yaml | kubectl apply -f -
    fi
    
    echo -e "${GREEN}✅ Secret创建/更新完成${NC}"
}

# 测试认证
test_auth() {
    echo -e "${BLUE}🧪 测试GitLab认证...${NC}"
    
    # 创建测试Pod
    cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: Pod
metadata:
  name: test-gitlab-auth
spec:
  restartPolicy: Never
  containers:
  - name: test
    image: alpine/git:2.40.1
    env:
    - name: GIT_USERNAME
      valueFrom:
        secretKeyRef:
          name: gitlab-auth
          key: username
          optional: true
    - name: GIT_PASSWORD
      valueFrom:
        secretKeyRef:
          name: gitlab-auth
          key: password
          optional: true
    - name: GIT_TOKEN
      valueFrom:
        secretKeyRef:
          name: gitlab-auth
          key: token
          optional: true
    command: ["/bin/sh"]
    args:
    - -c
    - |
      echo "🔍 检查认证信息:"
      echo "  GIT_TOKEN: \${GIT_TOKEN:+[设置]} \${GIT_TOKEN:-[未设置]}"
      echo "  GIT_USERNAME: \${GIT_USERNAME:+[设置]} \${GIT_USERNAME:-[未设置]}"
      echo "  GIT_PASSWORD: \${GIT_PASSWORD:+[设置]} \${GIT_PASSWORD:-[未设置]}"
      
      # 测试基本的git命令
      git config --global user.email "<EMAIL>"
      git config --global user.name "Test Pipeline"
      
      echo "📥 测试git ls-remote..."
      if [ -n "\${GIT_TOKEN}" ] && [ "\${GIT_TOKEN}" != "" ]; then
        echo "🔑 使用Token认证"
        git ls-remote https://oauth2:\${GIT_TOKEN}@source.fundpark.com/devops/gonotice.git HEAD
      elif [ -n "\${GIT_USERNAME}" ] && [ -n "\${GIT_PASSWORD}" ]; then
        echo "🔑 使用用户名密码认证"
        git ls-remote https://\${GIT_USERNAME}:\${GIT_PASSWORD}@source.fundpark.com/devops/gonotice.git HEAD
      else
        echo "❌ 认证信息不完整"
        exit 1
      fi
      
      echo "✅ 认证测试成功!"
EOF

    echo -e "${BLUE}⏳ 等待测试Pod运行...${NC}"
    sleep 5
    
    echo -e "${BLUE}📋 测试结果:${NC}"
    kubectl logs test-gitlab-auth
    
    # 清理测试Pod
    kubectl delete pod test-gitlab-auth
}

# 部署修复后的Task
deploy_fixed_task() {
    echo -e "${BLUE}🚀 部署修复后的gitlab-clone Task...${NC}"
    
    if [ -f "k8s/tekton/gitlab-clone-task.yaml" ]; then
        kubectl apply -f k8s/tekton/gitlab-clone-task.yaml
        echo -e "${GREEN}✅ gitlab-clone Task部署完成${NC}"
    else
        echo -e "${RED}❌ 找不到 k8s/tekton/gitlab-clone-task.yaml 文件${NC}"
        return 1
    fi
}

# 主菜单
main_menu() {
    echo -e "${BLUE}请选择操作:${NC}"
    echo "1. 检查现有Secret状态"
    echo "2. 创建/更新GitLab认证Secret"
    echo "3. 测试认证是否正常"
    echo "4. 部署修复后的gitlab-clone Task"
    echo "5. 执行完整修复流程"
    echo "6. 退出"
    
    read -p "请输入选项 (1-6): " choice
    
    case $choice in
        1)
            check_secret
            ;;
        2)
            create_secret
            ;;
        3)
            test_auth
            ;;
        4)
            deploy_fixed_task
            ;;
        5)
            echo -e "${BLUE}🔄 执行完整修复流程...${NC}"
            create_secret
            deploy_fixed_task
            test_auth
            echo -e "${GREEN}🎉 修复完成!${NC}"
            ;;
        6)
            echo -e "${GREEN}👋 再见!${NC}"
            exit 0
            ;;
        *)
            echo -e "${RED}❌ 无效选项${NC}"
            main_menu
            ;;
    esac
}

# 检查依赖
check_dependencies() {
    echo -e "${BLUE}🔍 检查依赖...${NC}"
    
    if ! command -v kubectl &> /dev/null; then
        echo -e "${RED}❌ kubectl 未安装${NC}"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        echo -e "${YELLOW}⚠️  jq 未安装，某些功能可能受限${NC}"
    fi
    
    # 检查Kubernetes连接
    if ! kubectl cluster-info &> /dev/null; then
        echo -e "${RED}❌ 无法连接到Kubernetes集群${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 依赖检查通过${NC}"
}

# 显示使用说明
show_usage() {
    echo -e "${BLUE}📖 使用说明:${NC}"
    echo "此脚本用于解决GitLab克隆时的认证问题:"
    echo "- 错误: 'could not read Username for https://source.fundpark.com: No such device or address'"
    echo "- 原因: Secret配置不正确或Task认证逻辑有问题"
    echo "- 解决: 正确配置Secret并部署修复后的Task"
    echo
}

# 主程序
main() {
    show_usage
    check_dependencies
    
    while true; do
        echo
        main_menu
        echo
        read -p "是否继续? (y/n): " continue_choice
        if [[ ! "$continue_choice" =~ ^[Yy]$ ]]; then
            echo -e "${GREEN}👋 再见!${NC}"
            break
        fi
    done
}

# 如果直接运行脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi 