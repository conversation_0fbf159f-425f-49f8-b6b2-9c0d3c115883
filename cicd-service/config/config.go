package config

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"github.com/spf13/viper"
)

// Config 是整个应用的配置结构
type Config struct {
	Server         ServerConfig         `mapstructure:"server"`
	Database       DatabaseConfig       `mapstructure:"database"`
	Logging        LoggingConfig        `mapstructure:"logging"`
	Harbor         HarborConfig         `mapstructure:"harbor"`
	Gitlab         GitlabConfig         `mapstructure:"gitlab"`
	Ansible        AnsibleConfig        `mapstructure:"ansible"`
	Tekton         TektonConfig         `mapstructure:"tekton"`
	Nats           NatsConfig           `mapstructure:"nats"`
	Encryption     EncryptionConfig     `mapstructure:"encryption"`
	UCenterService UCenterServiceConfig `mapstructure:"ucenter_service"`
}

// ServerConfig 保存HTTP服务器相关配置
type ServerConfig struct {
	Port         int    `mapstructure:"port"`
	Host         string `mapstructure:"host"`
	ReadTimeout  int    `mapstructure:"read_timeout"`
	WriteTimeout int    `mapstructure:"write_timeout"`
	IdleTimeout  int    `mapstructure:"idle_timeout"`
}

// DatabaseConfig 保存数据库相关配置
type DatabaseConfig struct {
	Type     string `mapstructure:"type"`
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	Username string `mapstructure:"username"`
	Password string `mapstructure:"password"`
	DBName   string `mapstructure:"dbname"`
	SSLMode  string `mapstructure:"sslmode"`
	MaxConns int    `mapstructure:"max_connections"`
	IdleTime int    `mapstructure:"idle_time"`
}

// LoggingConfig 保存日志相关配置
type LoggingConfig struct {
	Level      string `mapstructure:"level"`
	Format     string `mapstructure:"format"`
	Output     string `mapstructure:"output"`
	TimeFormat string `mapstructure:"time_format"`
}

// HarborConfig 保存Harbor镜像仓库相关配置
type HarborConfig struct {
	URL                string `mapstructure:"url"`
	Username           string `mapstructure:"username"`
	Password           string `mapstructure:"password"`
	PullUsernameSecret string `mapstructure:"pull_username_secret"`
	PullUsername       string `mapstructure:"pull_username"`
	PullPassword       string `mapstructure:"pull_password"`
}

// GitlabConfig 保存GitLab相关配置
type GitlabConfig struct {
	URL      string `mapstructure:"url"`
	Username string `mapstructure:"username"`
	Token    string `mapstructure:"token"`
}

// AnsibleConfig 保存Ansible相关配置
type AnsibleConfig struct {
	Path         string `mapstructure:"path"`
	PlaybookDir  string `mapstructure:"playbook_dir"`
	InventoryDir string `mapstructure:"inventory_dir"`
	LogDir       string `mapstructure:"log_dir"`
}

// TektonConfig 保存Tekton相关配置
type TektonConfig struct {
	Namespace    string `mapstructure:"namespace"`
	PipelineName string `mapstructure:"pipeline_name"`
}

// NatsConfig 保存NATS消息队列相关配置
type NatsConfig struct {
	URL     string `mapstructure:"url"`
	Subject string `mapstructure:"subject"`
}

// EncryptionConfig 保存加密相关配置
type EncryptionConfig struct {
	Key  string `mapstructure:"key"`
	Salt string `mapstructure:"salt"`
	Info string `mapstructure:"info"`
}

// UCenterServiceConfig 保存用户中心服务相关配置
type UCenterServiceConfig struct {
	URL              string `mapstructure:"url"`
	Timeout          int    `mapstructure:"timeout"`
	SkipAuthenticate bool   `mapstructure:"skip_authenticate"`
}

// LoadConfig 加载配置文件
func LoadConfig(configPath string) (*Config, error) {
	var config Config

	// 设置默认配置
	setDefaultConfig()

	// 处理配置文件路径
	if configPath != "" {
		// 检查是否是完整的文件路径
		fileInfo, err := os.Stat(configPath)
		if err == nil && !fileInfo.IsDir() {
			// 是完整的文件路径，直接设置
			viper.SetConfigFile(configPath)
		} else {
			// 是目录路径或不存在，作为搜索路径添加
			viper.AddConfigPath(configPath)
			viper.SetConfigName("config")
			viper.SetConfigType("yaml")
		}
	} else {
		// 默认配置
		viper.SetConfigName("config")
		viper.SetConfigType("yaml")
		// 尝试常见路径
		viper.AddConfigPath(".")
		viper.AddConfigPath("./config")
		viper.AddConfigPath("/etc/cicd-service")
		viper.AddConfigPath("$HOME/.cicd-service")
	}

	// 从环境变量加载配置
	viper.SetEnvPrefix("CICD")
	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	viper.AutomaticEnv()

	// 读取配置文件
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, fmt.Errorf("读取配置文件失败: %v", err)
		}
		fmt.Println("警告: 未找到配置文件，使用默认配置和环境变量")
	}

	// 解析配置到结构体
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("解析配置失败: %v", err)
	}

	return &config, nil
}

// setDefaultConfig 设置默认配置值
func setDefaultConfig() {
	// 服务器默认配置
	viper.SetDefault("server.port", 8080)
	viper.SetDefault("server.host", "0.0.0.0")
	viper.SetDefault("server.read_timeout", 60)
	viper.SetDefault("server.write_timeout", 60)
	viper.SetDefault("server.idle_timeout", 60)

	// 数据库默认配置
	viper.SetDefault("database.type", "mysql")
	viper.SetDefault("database.host", "localhost")
	viper.SetDefault("database.port", 3306)
	viper.SetDefault("database.username", "root")
	viper.SetDefault("database.password", "")
	viper.SetDefault("database.dbname", "cicd")
	viper.SetDefault("database.sslmode", "disable")
	viper.SetDefault("database.max_connections", 100)
	viper.SetDefault("database.idle_time", 10)

	// 日志默认配置
	viper.SetDefault("logging.level", "info")
	viper.SetDefault("logging.format", "text")
	viper.SetDefault("logging.output", "stdout")
	viper.SetDefault("logging.time_format", "2006-01-02 15:04:05")

	// Harbor默认配置
	viper.SetDefault("harbor.url", "harbor.fundpark.com")
	viper.SetDefault("harbor.username", "admin")
	viper.SetDefault("harbor.password", "Harbor12345")

	// GitLab默认配置
	viper.SetDefault("gitlab.url", "http://source.fundpark.com")
	viper.SetDefault("gitlab.username", "")
	viper.SetDefault("gitlab.token", "")

	// Ansible默认配置
	viper.SetDefault("ansible.path", "/usr/bin/ansible")
	viper.SetDefault("ansible.playbook_dir", "./playbooks")
	viper.SetDefault("ansible.inventory_dir", "./inventories")
	viper.SetDefault("ansible.log_dir", "./logs")

	// Tekton默认配置
	viper.SetDefault("tekton.namespace", "default")
	viper.SetDefault("tekton.pipeline_name", "build-pipeline")

	// NATS默认配置
	viper.SetDefault("nats.url", "nats://localhost:4222")
	viper.SetDefault("nats.subject", "cicd.events")

	// Encryption默认配置
	viper.SetDefault("encryption.key", "")
	viper.SetDefault("encryption.salt", "")
	viper.SetDefault("encryption.info", "")

	// UCenterService默认配置
	viper.SetDefault("ucenter_service.url", "http://ucenter-service:9982")
	viper.SetDefault("ucenter_service.timeout", 5)
	viper.SetDefault("ucenter_service.skip_authenticate", false)
}

// GetDSN 获取数据库连接字符串
func (c *DatabaseConfig) GetDSN() string {
	switch c.Type {
	case "mysql":
		return fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
			c.Username, c.Password, c.Host, c.Port, c.DBName)
	case "postgres":
		return fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
			c.Host, c.Port, c.Username, c.Password, c.DBName, c.SSLMode)
	case "sqlite":
		return c.DBName
	default:
		return ""
	}
}

// ValidateConfig 验证配置是否合法
func ValidateConfig(config *Config) error {
	// 验证服务器配置
	if config.Server.Port <= 0 || config.Server.Port > 65535 {
		return fmt.Errorf("无效的服务器端口: %d", config.Server.Port)
	}

	// 验证数据库配置
	if config.Database.Type == "" {
		return fmt.Errorf("未指定数据库类型")
	}
	if config.Database.Type != "sqlite" && config.Database.Host == "" {
		return fmt.Errorf("未指定数据库主机")
	}

	// 验证日志配置
	validLogLevels := map[string]bool{
		"trace": true, "debug": true, "info": true,
		"warn": true, "error": true, "fatal": true, "panic": true,
	}
	if !validLogLevels[strings.ToLower(config.Logging.Level)] {
		return fmt.Errorf("无效的日志级别: %s", config.Logging.Level)
	}

	// 验证Harbor配置
	if config.Harbor.URL == "" {
		return fmt.Errorf("未指定Harbor URL")
	}

	// 验证GitLab配置
	if config.Gitlab.URL == "" {
		return fmt.Errorf("未指定GitLab URL")
	}

	// 验证Ansible配置
	if config.Ansible.Path != "" {
		// 检查Ansible可执行文件是否存在
		if _, err := os.Stat(config.Ansible.Path); os.IsNotExist(err) {
			fmt.Printf("警告: Ansible可执行文件不存在: %s，Ansible功能将不可用\n", config.Ansible.Path)
		}
	}

	// 验证Ansible Playbook目录
	if config.Ansible.PlaybookDir != "" {
		playbookDir := config.Ansible.PlaybookDir
		if !filepath.IsAbs(playbookDir) {
			// 如果是相对路径，转换为绝对路径
			absPath, err := filepath.Abs(playbookDir)
			if err == nil {
				playbookDir = absPath
			}
		}
		if _, err := os.Stat(playbookDir); os.IsNotExist(err) {
			fmt.Printf("警告: Ansible Playbook目录不存在: %s，将使用默认目录\n", playbookDir)
			// 创建目录
			if err := os.MkdirAll(config.Ansible.PlaybookDir, 0755); err != nil {
				fmt.Printf("警告: 无法创建Ansible Playbook目录: %s\n", err)
			}
		}
	}

	// 验证Ansible日志目录
	if config.Ansible.LogDir != "" {
		logDir := config.Ansible.LogDir
		if !filepath.IsAbs(logDir) {
			// 如果是相对路径，转换为绝对路径
			absPath, err := filepath.Abs(logDir)
			if err == nil {
				logDir = absPath
			}
		}
		if _, err := os.Stat(logDir); os.IsNotExist(err) {
			fmt.Printf("警告: Ansible日志目录不存在: %s，将创建目录\n", logDir)
			// 创建目录
			if err := os.MkdirAll(config.Ansible.LogDir, 0755); err != nil {
				fmt.Printf("警告: 无法创建Ansible日志目录: %s\n", err)
			}
		}
	}

	return nil
}
