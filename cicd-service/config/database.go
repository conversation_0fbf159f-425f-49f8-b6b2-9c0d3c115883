package config

import (
	"fmt"
	"strings"
	"time"

	"github.com/devops-microservices/cicd-service/models"
	"github.com/sirupsen/logrus"
	"gorm.io/driver/mysql"
	"gorm.io/driver/postgres"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// InitDatabase 初始化数据库连接
func InitDatabase(config *DatabaseConfig, log logrus.FieldLogger) (*gorm.DB, error) {
	var db *gorm.DB
	var err error

	// 配置GORM日志
	gormLogger := logger.New(
		&GormLogrusWriter{Logger: log},
		logger.Config{
			SlowThreshold:             time.Second, // 慢SQL阈值
			LogLevel:                  logger.Info, // 日志级别
			IgnoreRecordNotFoundError: true,        // 忽略ErrRecordNotFound错误
			Colorful:                  false,       // 禁用彩色打印
		},
	)

	// 根据数据库类型创建不同的连接
	switch config.Type {
	case "mysql":
		dsn := config.GetDSN()
		db, err = gorm.Open(mysql.Open(dsn), &gorm.Config{
			Logger: gormLogger,
		})
	case "postgres":
		dsn := config.GetDSN()
		db, err = gorm.Open(postgres.Open(dsn), &gorm.Config{
			Logger: gormLogger,
		})
	case "sqlite":
		db, err = gorm.Open(sqlite.Open(config.DBName), &gorm.Config{
			Logger: gormLogger,
		})
	default:
		return nil, fmt.Errorf("不支持的数据库类型: %s", config.Type)
	}

	if err != nil {
		return nil, fmt.Errorf("连接数据库失败: %v", err)
	}

	// 配置连接池
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("获取底层DB连接失败: %v", err)
	}

	// 设置最大连接数
	sqlDB.SetMaxOpenConns(config.MaxConns)
	// 设置最大空闲连接数
	sqlDB.SetMaxIdleConns(config.MaxConns / 4)
	// 设置连接最大空闲时间
	sqlDB.SetConnMaxIdleTime(time.Duration(config.IdleTime) * time.Minute)

	return db, nil
}

// MigrateDatabase 执行数据库迁移
func MigrateDatabase(db *gorm.DB, log logrus.FieldLogger) error {
	log.Info("开始数据库迁移...")

	// 自动迁移模式
	models := []interface{}{
		&models.BuildJob{},
		&models.BuildJobResult{},
		&models.DeployJob{},
		&models.DeployJobResult{},
		&models.PublishOrder{},
		&models.PublishApp{},
		&models.MicroApp{},
		&models.AppInfo{},
		&models.DevLanguage{},
	}

	// 逐个迁移，避免一个错误导致整个过程失败
	for _, model := range models {
		modelName := fmt.Sprintf("%T", model)
		log.Infof("正在迁移表: %s", modelName)

		err := db.AutoMigrate(model)
		if err != nil {
			errMsg := err.Error()
			// 如果是约束不存在的错误，可以忽略继续
			if strings.Contains(errMsg, "constraint") && strings.Contains(errMsg, "does not exist") {
				log.Warnf("忽略约束不存在错误: %v", errMsg)
				continue
			}
			return fmt.Errorf("数据库迁移失败: %v", err)
		}

		log.Infof("成功迁移表: %s", modelName)
	}

	log.Info("数据库迁移完成")
	return nil
}

// GormLogrusWriter 用于将GORM日志转发到Logrus
type GormLogrusWriter struct {
	Logger logrus.FieldLogger
}

// Printf 实现gorm logger.Writer接口
func (w *GormLogrusWriter) Printf(format string, args ...interface{}) {
	w.Logger.Debugf(format, args...)
}
