# CICD服务配置示例文件
# 将此文件复制为 config.yaml 并根据实际环境进行修改

# 服务器配置
server:
  # 服务监听端口
  port: 9980
  # 服务监听地址
  host: "0.0.0.0"
  # 读取超时时间(秒)
  read_timeout: 60
  # 写入超时时间(秒)
  write_timeout: 60
  # 空闲连接超时时间(秒)
  idle_timeout: 60

# 数据库配置
database:
  # 数据库类型: mysql, postgres, sqlite
  type: "postgres"
  # 数据库主机地址
  host: "localhost"
  # 数据库端口
  port: 5432
  # 数据库用户名
  username: "devops"
  # 数据库密码
  password: "devops123"
  # 数据库名称
  dbname: "cicd"
  # SSL模式 (postgres专用)
  sslmode: "disable"
  # 最大连接数
  max_connections: 100
  # 连接空闲超时时间(分钟)
  idle_time: 10

# 日志配置
logging:
  # 日志级别: trace, debug, info, warn, error, fatal, panic
  level: "debug"
  # 日志格式: text, json
  format: "text"
  # 日志输出目标: stdout, stderr, file
  output: "stdout"
  # 日志时间格式
  time_format: "2006-01-02 15:04:05"

# Harbor镜像仓库配置
harbor:
  # Harbor地址
  url: "https://harbor.example.com"
  # Harbor用户名
  username: "admin"
  # Harbor密码
  password: "Harbor12345"
  # Harbor项目创建时的用户信息（用于创建loginharbor secret）
  pull_username_secret: "harbor-pull-secret"
  pull_username: "pull"
  pull_password: "password"

# Gitlab配置
gitlab:
  # Gitlab地址
  url: "http://gitlab.example.com"
  # GitLab用户名
  username: "cicd"
  # GitLab访问令牌
  token: "YOUR_GITLAB_TOKEN"

# Ansible配置
ansible:
  # Ansible可执行文件路径
  path: "/usr/bin/ansible"
  # Playbook目录
  playbook_dir: "./playbooks"
  # Inventory目录
  inventory_dir: "./inventories"
  # 日志目录
  log_dir: "./logs"

# Tekton配置
tekton:
  # Tekton所在的Kubernetes命名空间
  namespace: "tekton-pipelines"
  # 默认的Pipeline名称
  pipeline_name: "build-pipeline"

# NATS消息队列配置
nats:
  # NATS服务器地址
  url: "nats://localhost:4222"
  # 消息主题
  subject: "cicd.events"

# 用户中心服务配置
ucenter_service:
  # 用户中心服务地址
  url: "http://ucenter-service:9982"
  # 请求超时时间(秒)
  timeout: 5
  # 是否跳过认证(开发测试环境可设为true)
  skip_authenticate: false

# 加密配置
encryption:
  # Fernet加密密钥，用于解密Kubernetes配置等敏感信息
  # 应该与Django设置中的SECRET_KEY相同
  key: "7f=5@e+a=b(ghm-l*mtc_ile60xuvxqi(l5y$3&gfpk1!)3_4v"
  # HKDF盐值（可选，默认使用django-fernet-fields-hkdf-salt）
  salt: "django-fernet-fields-hkdf-salt"
  # HKDF信息（可选，默认使用django-fernet-fields）
  info: "django-fernet-fields" 