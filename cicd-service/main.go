package main

import (
	"context"
	"flag"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"runtime"
	"runtime/pprof"
	"sync"
	"syscall"
	"time"

	"github.com/devops-microservices/cicd-service/config"
	"github.com/devops-microservices/cicd-service/controllers"
	"github.com/devops-microservices/cicd-service/routes"
	"github.com/devops-microservices/cicd-service/services"
	"github.com/gin-gonic/gin"
	"github.com/nats-io/nats.go"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

var (
	// 性能分析标志
	cpuprofile = flag.String("cpuprofile", "", "写入CPU性能分析文件")
	memprofile = flag.String("memprofile", "", "写入内存性能分析文件")

	// 启动优化标志
	skipMigration = flag.Bool("skip-migration", false, "跳过数据库迁移检查（加快启动）")
	lazyInit      = flag.Bool("lazy-init", false, "延迟初始化非关键组件")
)

func main() {
	// 解析命令行参数
	var port int
	var configPath string
	flag.IntVar(&port, "port", 0, "服务端口，默认使用配置文件中的端口")
	flag.StringVar(&configPath, "config", "", "配置文件路径，默认使用工作目录下的config.yaml")
	flag.Parse()

	// 启动性能分析
	if *cpuprofile != "" {
		f, err := os.Create(*cpuprofile)
		if err != nil {
			fmt.Fprintf(os.Stderr, "无法创建CPU性能分析文件: %v\n", err)
			os.Exit(1)
		}
		defer f.Close()
		if err := pprof.StartCPUProfile(f); err != nil {
			fmt.Fprintf(os.Stderr, "无法启动CPU性能分析: %v\n", err)
			os.Exit(1)
		}
		defer pprof.StopCPUProfile()
	}

	startTime := time.Now()

	// 初始化日志
	log := initLogger()
	log.Info("🚀 初始化CICD服务")

	// 加载配置
	cfg, err := config.LoadConfig(configPath)
	if err != nil {
		log.Fatalf("❌ 加载配置失败: %v", err)
	}

	// 验证配置
	if err := config.ValidateConfig(cfg); err != nil {
		log.Fatalf("❌ 配置验证失败: %v", err)
	}

	// 如果命令行指定了端口，则覆盖配置文件中的端口
	if port > 0 {
		log.Infof("🔧 使用命令行指定的端口: %d", port)
		cfg.Server.Port = port
	}

	// 使用sync.WaitGroup进行并发初始化
	var wg sync.WaitGroup
	var (
		db           *gorm.DB
		kubeClient   *services.K8sClient
		tektonClient *services.TektonClientImpl
		natsConn     *nats.Conn
	)

	// 初始化数据库
	wg.Add(1)
	go func() {
		defer wg.Done()
		log.Info("📊 初始化数据库...")
		var err error
		db, err = config.InitDatabase(&cfg.Database, log)
		if err != nil {
			log.Fatalf("❌ 初始化数据库失败: %v", err)
		}

		// 根据标志决定是否跳过迁移
		if !*skipMigration {
			if err := config.MigrateDatabase(db, log); err != nil {
				log.Warnf("⚠️ 数据库迁移失败: %v", err)
			}
		} else {
			log.Info("⏭️ 跳过数据库迁移检查")
		}
	}()

	// 初始化Kubernetes客户端
	if !*lazyInit {
		wg.Add(1)
		go func() {
			defer wg.Done()
			log.Info("☸️ 初始化Kubernetes客户端...")
			var err error
			kubeClient, err = services.NewK8sClient(log)
			if err != nil {
				log.Errorf("❌ 初始化Kubernetes客户端失败: %v", err)
				// 不要致命错误，允许在没有K8s的环境中运行
			}
		}()
	}

	// 等待核心组件初始化完成
	wg.Wait()

	// 延迟初始化非核心组件
	if *lazyInit {
		log.Info("⏰ 使用延迟初始化模式")
		go func() {
			time.Sleep(2 * time.Second) // 等待服务器启动
			log.Info("☸️ 延迟初始化Kubernetes客户端...")
			var err error
			kubeClient, err = services.NewK8sClient(log)
			if err != nil {
				log.Errorf("❌ 延迟初始化Kubernetes客户端失败: %v", err)
			}
		}()
	}

	// 初始化Tekton客户端
	if kubeClient != nil {
		log.Info("🔧 初始化Tekton客户端...")
		var err error
		tektonClient, err = services.NewTektonClientWrapper(kubeClient.Config, log)
		if err != nil {
			log.Errorf("❌ 初始化Tekton客户端失败: %v", err)
		}
	}

	// 连接NATS（非阻塞）
	if cfg.Nats.URL != "" {
		go func() {
			log.Info("📡 连接NATS...")
			var err error
			natsConn, err = nats.Connect(cfg.Nats.URL)
			if err != nil {
				log.Warnf("⚠️ 连接NATS失败: %v", err)
			} else {
				log.Infof("✅ 已连接NATS: %s", cfg.Nats.URL)
			}
		}()
	}

	// 初始化Ansible服务
	ansibleConfig := &services.AnsibleConfig{
		AnsiblePath:  cfg.Ansible.Path,
		PlaybookDir:  cfg.Ansible.PlaybookDir,
		InventoryDir: cfg.Ansible.InventoryDir,
		LogDir:       cfg.Ansible.LogDir,
	}
	ansibleService := services.NewAnsibleService(log, ansibleConfig)

	// 初始化CICD服务
	cicdService := services.NewCICDService(tektonClient, kubeClient, natsConn, cfg.Nats.Subject, log, db)
	cicdService.SetConfig(cfg.Harbor.URL, cfg.Harbor.Username, cfg.Harbor.Password)
	cicdService.SetGitlabConfig(cfg.Gitlab.URL, cfg.Gitlab.Token, cfg.Gitlab.Username)

	// 初始化发布服务
	publishService := services.NewPublishService(cicdService, db, log)

	// 初始化Gin
	router := initGin(log)

	// 注册所有API路由（原有路由 + 新API路由）
	routes.SetupRoutes(router, db, log, cicdService, publishService, cfg)

	// 注册Ansible路由
	controllers.RegisterAnsibleRoutes(router, ansibleService, log)

	// 启动HTTP服务器
	srv := &http.Server{
		Addr:         fmt.Sprintf("%s:%d", cfg.Server.Host, cfg.Server.Port),
		Handler:      router,
		ReadTimeout:  time.Duration(cfg.Server.ReadTimeout) * time.Second,
		WriteTimeout: time.Duration(cfg.Server.WriteTimeout) * time.Second,
		IdleTimeout:  time.Duration(cfg.Server.IdleTimeout) * time.Second,
	}

	// 计算启动时间
	startupTime := time.Since(startTime)
	log.Infof("⚡ 服务启动耗时: %v", startupTime)

	// 优雅关闭
	go func() {
		log.Infof("🌐 CICD服务启动在 %s:%d", cfg.Server.Host, cfg.Server.Port)
		log.Infof("📊 内存使用: %s", formatBytes(getMemUsage()))
		log.Infof("🔗 健康检查: http://%s:%d/health", cfg.Server.Host, cfg.Server.Port)

		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("❌ 启动HTTP服务器失败: %v", err)
		}
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Info("🛑 正在关闭服务...")

	// 写入内存性能分析
	if *memprofile != "" {
		f, err := os.Create(*memprofile)
		if err != nil {
			log.Errorf("无法创建内存性能分析文件: %v", err)
		} else {
			defer f.Close()
			runtime.GC() // 触发GC以获得更准确的内存使用情况
			if err := pprof.WriteHeapProfile(f); err != nil {
				log.Errorf("无法写入内存性能分析: %v", err)
			}
		}
	}

	// 给服务器10秒钟处理剩余请求
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer shutdownCancel()

	if err := srv.Shutdown(shutdownCtx); err != nil {
		log.Fatalf("❌ 服务器强制关闭: %v", err)
	}

	// 关闭NATS连接
	if natsConn != nil {
		natsConn.Close()
	}

	log.Info("✅ 服务已安全关闭")
}

// initLogger 初始化日志
func initLogger() *logrus.Logger {
	log := logrus.New()
	log.SetFormatter(&logrus.TextFormatter{
		FullTimestamp:   true,
		TimestampFormat: "2006-01-02 15:04:05",
		ForceColors:     true,
	})
	log.SetOutput(os.Stdout)
	log.SetLevel(logrus.InfoLevel)
	return log
}

// initGin 初始化Gin
func initGin(log *logrus.Logger) *gin.Engine {
	gin.SetMode(gin.ReleaseMode)
	router := gin.New()

	// 使用自定义Logger中间件
	router.Use(func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path
		method := c.Request.Method

		c.Next()

		latency := time.Since(start)
		statusCode := c.Writer.Status()

		// 只记录非健康检查的请求
		if path != "/health" {
			log.WithFields(logrus.Fields{
				"status":  statusCode,
				"latency": latency,
				"method":  method,
				"path":    path,
				"ip":      c.ClientIP(),
			}).Info("API请求")
		}
	})

	// 使用Recovery中间件
	router.Use(gin.Recovery())

	return router
}

// getMemUsage 获取内存使用情况
func getMemUsage() uint64 {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	return m.Alloc
}

// formatBytes 格式化字节数
func formatBytes(bytes uint64) string {
	const unit = 1024
	if bytes < unit {
		return fmt.Sprintf("%d B", bytes)
	}
	div, exp := uint64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(bytes)/float64(div), "KMGTPE"[exp])
}
