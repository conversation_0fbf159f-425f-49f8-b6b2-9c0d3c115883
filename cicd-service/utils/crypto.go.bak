package utils

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/hmac"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"fmt"
	"time"

	"golang.org/x/crypto/hkdf"
)

// AesCipher AES加密解密器（兼容性保留）
type AesCipher struct {
	key []byte
}

// NewAesCipher 创建新的AES加密器（兼容性保留）
func NewAesCipher(key []byte) *AesCipher {
	return &AesCipher{key: key}
}

// Encrypt 加密数据（兼容性保留）
func (c *AesCipher) Encrypt(data []byte) ([]byte, error) {
	block, err := aes.NewCipher(c.key)
	if err != nil {
		return nil, err
	}

	// 使用ECB模式（与原代码兼容）
	encrypted := make([]byte, len(data))
	copy(encrypted, data)

	// 简单的ECB加密实现
	blockSize := block.BlockSize()
	for i := 0; i < len(encrypted); i += blockSize {
		end := i + blockSize
		if end > len(encrypted) {
			// 填充
			pad := blockSize - (len(encrypted) % blockSize)
			for j := 0; j < pad; j++ {
				encrypted = append(encrypted, byte(pad))
			}
			end = len(encrypted)
		}
		block.Encrypt(encrypted[i:end], encrypted[i:end])
	}

	return encrypted, nil
}

// Decrypt 解密数据（兼容性保留）
func (c *AesCipher) Decrypt(data []byte) ([]byte, error) {
	block, err := aes.NewCipher(c.key)
	if err != nil {
		return nil, err
	}

	decrypted := make([]byte, len(data))
	copy(decrypted, data)

	// 简单的ECB解密实现
	blockSize := block.BlockSize()
	for i := 0; i < len(decrypted); i += blockSize {
		block.Decrypt(decrypted[i:i+blockSize], decrypted[i:i+blockSize])
	}

	// 移除填充
	if len(decrypted) > 0 {
		pad := int(decrypted[len(decrypted)-1])
		if pad <= blockSize && pad <= len(decrypted) {
			decrypted = decrypted[:len(decrypted)-pad]
		}
	}

	return decrypted, nil
}

// FernetCipher Fernet加密解密器
type FernetCipher struct {
	signingKey []byte // 用于HMAC的密钥
	cryptKey   []byte // 用于AES的密钥
}

// NewFernetCipher 创建新的Fernet加密器
// 注意：这里的key应该是从HKDF派生出来的32字节密钥（未经Base64编码）
func NewFernetCipher(key []byte) *FernetCipher {
	if len(key) != 32 {
		panic(fmt.Sprintf("Fernet key must be 32 bytes, got %d", len(key)))
	}

	return &FernetCipher{
		signingKey: key[16:32], // 后16字节用于HMAC
		cryptKey:   key[0:16],  // 前16字节用于AES
	}
}

// NewFernetCipherFromBase64 从Base64编码的密钥创建Fernet加密器
// 这个方法接受Python fernet_fields.hkdf.derive_fernet_key() 返回的Base64编码密钥
func NewFernetCipherFromBase64(base64Key string) (*FernetCipher, error) {
	key, err := base64URLSafeBase64Decode(base64Key)
	if err != nil {
		return nil, fmt.Errorf("failed to decode base64 key: %v", err)
	}
	return NewFernetCipher(key), nil
}

// Encrypt 使用Fernet算法加密数据
func (f *FernetCipher) Encrypt(data []byte) (string, error) {
	// 1. 版本号 (1字节)
	version := byte(0x80)

	// 2. 时间戳 (8字节)
	timestamp := uint64(time.Now().Unix())
	timestampBytes := make([]byte, 8)
	for i := 7; i >= 0; i-- {
		timestampBytes[i] = byte(timestamp & 0xff)
		timestamp >>= 8
	}

	// 3. IV (16字节)
	iv := make([]byte, 16)
	_, err := rand.Read(iv)
	if err != nil {
		return "", fmt.Errorf("failed to generate IV: %v", err)
	}

	// 4. 加密数据
	block, err := aes.NewCipher(f.cryptKey)
	if err != nil {
		return "", fmt.Errorf("failed to create AES cipher: %v", err)
	}

	mode := cipher.NewCBCEncrypter(block, iv)

	// PKCS7填充
	paddedData := pkcs7Pad(data, aes.BlockSize)
	ciphertext := make([]byte, len(paddedData))
	mode.CryptBlocks(ciphertext, paddedData)

	// 5. 构建基本消息
	basicParts := [][]byte{
		{version},
		timestampBytes,
		iv,
		ciphertext,
	}

	var basicMessage []byte
	for _, part := range basicParts {
		basicMessage = append(basicMessage, part...)
	}

	// 6. 计算HMAC
	h := hmac.New(sha256.New, f.signingKey)
	h.Write(basicMessage)
	signature := h.Sum(nil)

	// 7. 构建完整token
	token := append(basicMessage, signature...)

	// 8. Base64编码
	return base64URLSafeBase64Encode(token), nil
}

// Decrypt 使用Fernet算法解密数据
func (f *FernetCipher) Decrypt(token string) ([]byte, error) {
	// 1. Base64解码
	data, err := base64URLSafeBase64Decode(token)
	if err != nil {
		return nil, fmt.Errorf("failed to decode base64: %v", err)
	}

	// 2. 检查最小长度 (1 + 8 + 16 + 32 = 57字节)
	if len(data) < 57 {
		return nil, fmt.Errorf("invalid token length: %d", len(data))
	}

	// 3. 分离组件
	version := data[0]
	timestamp := data[1:9]
	iv := data[9:25]
	ciphertext := data[25 : len(data)-32]
	signature := data[len(data)-32:]

	// 4. 验证版本
	if version != 0x80 {
		return nil, fmt.Errorf("invalid token version: %x", version)
	}

	// 5. 验证HMAC
	basicMessage := data[:len(data)-32]
	h := hmac.New(sha256.New, f.signingKey)
	h.Write(basicMessage)
	expectedSignature := h.Sum(nil)

	if !hmac.Equal(signature, expectedSignature) {
		return nil, fmt.Errorf("invalid token signature")
	}

	// 6. 解密
	block, err := aes.NewCipher(f.cryptKey)
	if err != nil {
		return nil, fmt.Errorf("failed to create AES cipher: %v", err)
	}

	mode := cipher.NewCBCDecrypter(block, iv)
	decrypted := make([]byte, len(ciphertext))
	mode.CryptBlocks(decrypted, ciphertext)

	// 7. 移除PKCS7填充
	unpadded, err := pkcs7Unpad(decrypted)
	if err != nil {
		return nil, fmt.Errorf("failed to unpad: %v", err)
	}

	// 解析时间戳（可选验证）
	_ = timestamp

	return unpadded, nil
}

// DeriveFernetKey 使用HKDF派生Fernet密钥，完全兼容Python实现
func DeriveFernetKey(inputKey []byte, salt []byte, info []byte) ([]byte, error) {
	// 使用HKDF-SHA256派生32字节密钥
	hkdfReader := hkdf.New(sha256.New, inputKey, salt, info)
	key := make([]byte, 32)
	_, err := hkdfReader.Read(key)
	if err != nil {
		return nil, fmt.Errorf("HKDF key derivation failed: %v", err)
	}
	return key, nil
}

// DeriveFernetKeyBase64 派生密钥并返回Base64编码，完全兼容Python fernet_fields.hkdf.derive_fernet_key
func DeriveFernetKeyBase64(inputKey []byte, salt []byte, info []byte) (string, error) {
	key, err := DeriveFernetKey(inputKey, salt, info)
	if err != nil {
		return "", err
	}
	return base64URLSafeBase64Encode(key), nil
}

// DecryptKubernetesConfig 解密Kubernetes配置，兼容Django fernet_fields
func DecryptKubernetesConfig(encryptedConfig, secretKey, salt string) (string, error) {
	// 1. 处理输入密钥 - 模拟Django的force_bytes行为
	inputKeyBytes := []byte(secretKey) // 直接转换为字节，相当于UTF-8编码

	// 2. 使用HKDF派生密钥（与Python完全一致）
	saltBytes := []byte(salt)
	infoBytes := []byte("django-fernet-fields")

	derivedKey, err := DeriveFernetKey(inputKeyBytes, saltBytes, infoBytes)
	if err != nil {
		return "", fmt.Errorf("failed to derive key: %v", err)
	}

	// 3. 创建Fernet cipher
	fernet := NewFernetCipher(derivedKey)

	// 4. 解密
	decrypted, err := fernet.Decrypt(encryptedConfig)
	if err != nil {
		return "", fmt.Errorf("failed to decrypt config: %v", err)
	}

	return string(decrypted), nil
}

// PKCS7填充
func pkcs7Pad(data []byte, blockSize int) []byte {
	padding := blockSize - (len(data) % blockSize)
	padText := make([]byte, padding)
	for i := range padText {
		padText[i] = byte(padding)
	}
	return append(data, padText...)
}

// PKCS7去填充
func pkcs7Unpad(data []byte) ([]byte, error) {
	if len(data) == 0 {
		return nil, fmt.Errorf("empty data")
	}

	padding := int(data[len(data)-1])
	if padding > len(data) || padding == 0 {
		return nil, fmt.Errorf("invalid padding")
	}

	// 验证填充
	for i := len(data) - padding; i < len(data); i++ {
		if data[i] != byte(padding) {
			return nil, fmt.Errorf("invalid padding")
		}
	}

	return data[:len(data)-padding], nil
}

// Base64 URL安全编码（带填充，兼容Python cryptography.fernet）
func base64URLSafeBase64Encode(data []byte) string {
	return base64.URLEncoding.EncodeToString(data)
}

// Base64 URL安全解码（带填充，兼容Python cryptography.fernet）
func base64URLSafeBase64Decode(s string) ([]byte, error) {
	return base64.URLEncoding.DecodeString(s)
}
