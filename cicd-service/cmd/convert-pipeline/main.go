package main

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"path/filepath"

	"sigs.k8s.io/yaml"
)

func main() {
	if len(os.Args) < 2 {
		fmt.Printf("用法: %s <pipeline.yaml文件路径>\n", os.Args[0])
		fmt.Println("示例: go run cmd/convert-pipeline/main.go k8s/tekton/build-pipeline.yaml")
		os.Exit(1)
	}

	yamlFile := os.Args[1]

	// 检查文件是否存在
	if _, err := os.Stat(yamlFile); os.IsNotExist(err) {
		log.Fatalf("❌ 文件不存在: %s", yamlFile)
	}

	fmt.Printf("🔄 转换Pipeline YAML文件: %s\n", yamlFile)

	// 读取YAML文件
	yamlContent, err := ioutil.ReadFile(yamlFile)
	if err != nil {
		log.Fatalf("❌ 读取YAML文件失败: %v", err)
	}

	// 将YAML转换为Go对象
	var pipelineData map[string]interface{}
	if err := yaml.Unmarshal(yamlContent, &pipelineData); err != nil {
		log.Fatalf("❌ 解析YAML失败: %v", err)
	}

	// 转换为JSON
	jsonData, err := json.MarshalIndent(pipelineData, "", "  ")
	if err != nil {
		log.Fatalf("❌ 转换为JSON失败: %v", err)
	}

	// 输出结果
	fmt.Println("✅ 转换成功！以下是pipeline_dl字段的值：")
	fmt.Println()
	fmt.Println("==== JSON格式 ====")
	fmt.Println(string(jsonData))
	fmt.Println()

	// 生成可以直接复制的格式
	fmt.Println("==== 数据库插入格式 ====")
	escapedJSON := fmt.Sprintf("%q", string(jsonData))
	fmt.Printf(`{"pipeline_dl": %s}`, escapedJSON)
	fmt.Println()
	fmt.Println()

	// 生成SQL插入语句示例
	fmt.Println("==== SQL更新语句示例 ====")
	fmt.Printf("UPDATE microapp SET pipeline_dl = %s WHERE id = <your_app_id>;\n", escapedJSON)
	fmt.Println()

	// 保存到文件
	outputFile := fmt.Sprintf("%s.json", filepath.Base(yamlFile))
	if err := ioutil.WriteFile(outputFile, jsonData, 0644); err != nil {
		log.Printf("⚠️ 保存JSON文件失败: %v", err)
	} else {
		fmt.Printf("📁 JSON文件已保存: %s\n", outputFile)
	}

	// 验证pipeline格式
	validatePipeline(pipelineData)
}

// validatePipeline 验证pipeline格式是否正确
func validatePipeline(pipelineData map[string]interface{}) {
	fmt.Println("==== Pipeline验证 ====")

	// 检查apiVersion
	if apiVersion, ok := pipelineData["apiVersion"].(string); ok {
		fmt.Printf("✅ API版本: %s\n", apiVersion)
	} else {
		fmt.Println("⚠️ 未找到apiVersion字段")
	}

	// 检查kind
	if kind, ok := pipelineData["kind"].(string); ok {
		fmt.Printf("✅ 资源类型: %s\n", kind)
		if kind != "Pipeline" {
			fmt.Printf("⚠️ 资源类型不是Pipeline: %s\n", kind)
		}
	} else {
		fmt.Println("⚠️ 未找到kind字段")
	}

	// 检查metadata
	if metadata, ok := pipelineData["metadata"].(map[string]interface{}); ok {
		if name, ok := metadata["name"].(string); ok {
			fmt.Printf("✅ Pipeline名称: %s\n", name)
		} else {
			fmt.Println("⚠️ 未找到Pipeline名称")
		}
	} else {
		fmt.Println("⚠️ 未找到metadata字段")
	}

	// 检查spec
	if spec, ok := pipelineData["spec"].(map[string]interface{}); ok {
		// 检查tasks
		if tasks, ok := spec["tasks"].([]interface{}); ok {
			fmt.Printf("✅ 任务数量: %d\n", len(tasks))
			for i, task := range tasks {
				if taskMap, ok := task.(map[string]interface{}); ok {
					if name, ok := taskMap["name"].(string); ok {
						fmt.Printf("  - 任务%d: %s\n", i+1, name)
					}
				}
			}
		} else {
			fmt.Println("⚠️ 未找到tasks字段")
		}

		// 检查workspaces
		if workspaces, ok := spec["workspaces"].([]interface{}); ok {
			fmt.Printf("✅ 工作空间数量: %d\n", len(workspaces))
		}

		// 检查params
		if params, ok := spec["params"].([]interface{}); ok {
			fmt.Printf("✅ 参数数量: %d\n", len(params))
		}
	} else {
		fmt.Println("⚠️ 未找到spec字段")
	}

	fmt.Println("✅ Pipeline验证完成")
}
