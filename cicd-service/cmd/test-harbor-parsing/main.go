package main

import (
	"fmt"
	"strings"
)

// 模拟检查Harbor项目的shell脚本逻辑
func checkHarborProject(imageName string, harborUsername string, harborPassword string) {
	fmt.Printf("🔍 检查Harbor项目: %s\n", imageName)

	// 解析镜像名称 - 修复后的逻辑
	// 移除可能的协议前缀
	cleanImageName := imageName
	cleanImageName = strings.TrimPrefix(cleanImageName, "https://")
	cleanImageName = strings.TrimPrefix(cleanImageName, "http://")

	fmt.Printf("清理后的镜像名称: %s\n", cleanImageName)

	// 分割镜像名称: harbor-host/project-name/app-name:tag
	parts := strings.Split(cleanImageName, "/")

	if len(parts) < 3 {
		fmt.Printf("❌ 镜像名称格式错误，期望格式: harbor-host/project-name/app-name:tag\n")
		return
	}

	harborHost := parts[0]
	projectName := parts[1]
	appNameWithTag := parts[2]

	// 提取应用名称（去掉tag）
	appParts := strings.Split(appNameWithTag, ":")
	appName := appParts[0]
	tag := ""
	if len(appParts) > 1 {
		tag = appParts[1]
	}

	fmt.Printf("Harbor Host: %s\n", harborHost)
	fmt.Printf("项目名称: %s\n", projectName)
	fmt.Printf("应用名称: %s\n", appName)
	fmt.Printf("标签: %s\n", tag)

	// 构建Harbor API URL
	harborAPIURL := fmt.Sprintf("https://%s/api/v2.0/projects/%s", harborHost, projectName)
	fmt.Printf("Harbor API URL: %s\n", harborAPIURL)

	// 模拟curl命令
	curlCmd := fmt.Sprintf("curl -s -o /dev/null -w '%%{http_code}' -u %s:%s %s",
		harborUsername, harborPassword, harborAPIURL)
	fmt.Printf("检查命令: %s\n", curlCmd)

	// 模拟检查结果
	fmt.Println("✅ 项目检查逻辑正确")
}

func main() {
	fmt.Println("🧪 测试Harbor项目检查逻辑")

	// 测试用例 - 修复后的镜像名称格式
	testCases := []struct {
		imageName string
		desc      string
	}{
		{
			imageName: "harbor.fundpark.com/dev-myproduct/gonotice:ae4b8548-20250523110406",
			desc:      "正确的镜像名称（无协议前缀）",
		},
		{
			imageName: "https://harbor.fundpark.com/dev-myproduct/gonotice:ae4b8548-20250523110406",
			desc:      "错误的镜像名称（包含协议前缀）- 应该被修复",
		},
		{
			imageName: "harbor.fundpark.com/project-5/gonotice:ae4b8548-20250523110406",
			desc:      "原始错误的项目名称格式",
		},
	}

	harborUsername := "admin"
	harborPassword := "4DNSNszww1B^9DbfENLe"

	for i, testCase := range testCases {
		fmt.Printf("\n📋 测试用例 %d: %s\n", i+1, testCase.desc)
		checkHarborProject(testCase.imageName, harborUsername, harborPassword)
		fmt.Println(strings.Repeat("-", 80))
	}

	fmt.Println("\n✅ 测试完成！")
	fmt.Println("\n🔧 修复总结:")
	fmt.Println("1. ✅ 移除镜像名称中的协议前缀（https://）")
	fmt.Println("2. ✅ 项目名称格式改为: environmentName.lower() + \"-\" + productName.lower()")
	fmt.Println("3. ✅ Harbor API URL正确构建")
	fmt.Println("4. ✅ 解决了数据库查询的Preload问题")
}
