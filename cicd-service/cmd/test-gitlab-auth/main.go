package main

import (
	"fmt"
	"os"
	"strings"
)

// 模拟认证逻辑测试
func testGitLabAuth(token, username, password, repoURL string) {
	fmt.Printf("🧪 测试认证配置:\n")
	fmt.Printf("  Token: %s\n", maskValue(token))
	fmt.Printf("  Username: %s\n", maskValue(username))
	fmt.Printf("  Password: %s\n", maskValue(password))
	fmt.Printf("  Repo URL: %s\n", repoURL)
	fmt.Println()

	var authenticatedURL string
	var authMethod string

	// 模拟gitlab-clone-task.yaml中的认证逻辑
	if token != "" && strings.TrimSpace(token) != "" {
		fmt.Println("🔑 使用Token认证")
		authMethod = "Token"

		// 提取主机名
		repoHost := extractHost(repoURL)
		repoPath := extractPath(repoURL)
		authenticatedURL = fmt.Sprintf("https://oauth2:%s@%s%s", token, repoHost, repoPath)

		fmt.Printf("🔗 认证URL: https://oauth2:[TOKEN]@%s%s\n", repoHost, repoPath)
	} else if username != "" && strings.TrimSpace(username) != "" && password != "" && strings.TrimSpace(password) != "" {
		fmt.Println("🔑 使用用户名密码认证")
		authMethod = "Username+Password"

		// 提取主机名
		repoHost := extractHost(repoURL)
		repoPath := extractPath(repoURL)
		authenticatedURL = fmt.Sprintf("https://%s:%s@%s%s", username, password, repoHost, repoPath)

		fmt.Printf("🔗 认证URL: https://%s:[PASSWORD]@%s%s\n", username, repoHost, repoPath)
	} else {
		fmt.Println("⚠️  未配置认证信息，尝试公开仓库访问")
		authMethod = "Public"
		authenticatedURL = repoURL
		fmt.Printf("🔗 公开URL: %s\n", repoURL)
	}

	fmt.Printf("✅ 选择的认证方式: %s\n", authMethod)
	fmt.Printf("🎯 最终URL长度: %d字符\n", len(authenticatedURL))
	fmt.Println(strings.Repeat("-", 80))
}

func maskValue(value string) string {
	if value == "" {
		return "[空]"
	}
	if len(value) <= 4 {
		return "[设置]"
	}
	return value[:2] + "****" + value[len(value)-2:]
}

func extractHost(url string) string {
	// 移除协议
	cleaned := strings.TrimPrefix(url, "https://")
	cleaned = strings.TrimPrefix(cleaned, "http://")

	// 提取主机名
	if idx := strings.Index(cleaned, "/"); idx > 0 {
		return cleaned[:idx]
	}
	return cleaned
}

func extractPath(url string) string {
	// 移除协议
	cleaned := strings.TrimPrefix(url, "https://")
	cleaned = strings.TrimPrefix(cleaned, "http://")

	// 提取路径
	if idx := strings.Index(cleaned, "/"); idx > 0 {
		return cleaned[idx:]
	}
	return ""
}

func main() {
	fmt.Println("🧪 GitLab认证逻辑测试")
	fmt.Println()

	// 测试用例
	testCases := []struct {
		name     string
		token    string
		username string
		password string
		repoURL  string
	}{
		{
			name:     "空Token + 有效用户名密码",
			token:    "",
			username: "git",
			password: "T-_xS8UP5yWmA8H2pk5s",
			repoURL:  "https://source.fundpark.com/devops/gonotice.git",
		},
		{
			name:     "有效Token + 用户名密码",
			token:    "glpat-xxxxxxxxxxxxxxxxxxxx",
			username: "git",
			password: "T-_xS8UP5yWmA8H2pk5s",
			repoURL:  "https://source.fundpark.com/devops/gonotice.git",
		},
		{
			name:     "仅用户名密码",
			token:    "",
			username: "git",
			password: "T-_xS8UP5yWmA8H2pk5s",
			repoURL:  "https://source.fundpark.com/devops/gonotice.git",
		},
		{
			name:     "空认证信息",
			token:    "",
			username: "",
			password: "",
			repoURL:  "https://github.com/public/repo.git",
		},
		{
			name:     "仅空格Token",
			token:    "   ",
			username: "git",
			password: "T-_xS8UP5yWmA8H2pk5s",
			repoURL:  "https://source.fundpark.com/devops/gonotice.git",
		},
	}

	for i, tc := range testCases {
		fmt.Printf("📋 测试用例 %d: %s\n", i+1, tc.name)
		testGitLabAuth(tc.token, tc.username, tc.password, tc.repoURL)
		fmt.Println()
	}

	fmt.Println("🔧 Secret配置建议:")
	fmt.Printf(`
对于当前环境 (source.fundpark.com)：

apiVersion: v1
kind: Secret
metadata:
  name: gitlab-auth
stringData:
  username: "git"
  password: "T-_xS8UP5yWmA8H2pk5s"
  # token: ""    # 如果有GitLab Personal Access Token，填入这里
`)

	fmt.Println("🎯 认证优先级:")
	fmt.Println("  1. GitLab Personal Access Token (推荐)")
	fmt.Println("  2. 用户名 + 密码")
	fmt.Println("  3. 公开仓库访问")

	fmt.Println()
	fmt.Println("🚨 常见问题解决:")
	fmt.Println("  - 如果遇到 'No such device or address' 错误")
	fmt.Println("  - 检查Secret是否正确部署：kubectl get secret gitlab-auth -o yaml")
	fmt.Println("  - 检查认证信息是否正确填写")
	fmt.Println("  - 确保环境变量正确映射到Secret")

	// 如果从环境变量运行，显示实际值
	if token := os.Getenv("GIT_TOKEN"); token != "" ||
		os.Getenv("GIT_USERNAME") != "" || os.Getenv("GIT_PASSWORD") != "" {
		fmt.Println()
		fmt.Println("🔍 当前环境变量:")
		fmt.Printf("  GIT_TOKEN: %s\n", maskValue(os.Getenv("GIT_TOKEN")))
		fmt.Printf("  GIT_USERNAME: %s\n", maskValue(os.Getenv("GIT_USERNAME")))
		fmt.Printf("  GIT_PASSWORD: %s\n", maskValue(os.Getenv("GIT_PASSWORD")))
	}
}
