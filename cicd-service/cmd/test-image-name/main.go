package main

import (
	"fmt"
	"strings"
	"time"
)

// 模拟Harbor URL配置
var harborURL = "https://harbor.fundpark.com"

// 模拟环境和产品信息
type TestData struct {
	EnvironmentName string
	ProductName     string
	AppName         string
	CommitShortID   string
}

// ensureHarborURLPrefix 确保镜像名称包含Harbor URL前缀（不包含协议）
func ensureHarborURLPrefix(image string, harborURL string) string {
	// 清理Harbor URL，移除协议前缀
	harborHost := harborURL
	harborHost = strings.TrimPrefix(harborHost, "http://")
	harborHost = strings.TrimPrefix(harborHost, "https://")
	harborHost = strings.TrimSuffix(harborHost, "/")

	// 检查是否已包含Registry URL
	if !strings.Contains(image, "/") || !strings.Contains(strings.Split(image, "/")[0], ".") {
		// 如果镜像名称不包含Harbor URL前缀，则添加
		if !strings.HasPrefix(image, harborHost) {
			image = fmt.Sprintf("%s/%s", harborHost, image)
			fmt.Printf("添加Harbor URL前缀到镜像名称: %s\n", image)
		}
	}

	return image
}

// 构建镜像名称
func buildImageName(data TestData) string {
	// 项目名称格式: environmentName.lower() + "-" + productName.lower()
	projectName := fmt.Sprintf("%s-%s", strings.ToLower(data.EnvironmentName), strings.ToLower(data.ProductName))

	// 镜像标签
	imageTag := fmt.Sprintf("%s-%s", data.CommitShortID, time.Now().Format("20060102150405"))

	// 清理Harbor URL，移除协议前缀
	harborHost := harborURL
	harborHost = strings.TrimPrefix(harborHost, "http://")
	harborHost = strings.TrimPrefix(harborHost, "https://")
	harborHost = strings.TrimSuffix(harborHost, "/")

	// 构建完整镜像名称
	imageName := fmt.Sprintf("%s/%s/%s:%s", harborHost, projectName, data.AppName, imageTag)

	return imageName
}

// 测试Harbor项目检查逻辑（模拟Tekton脚本中的解析）
func testHarborProjectParsing(imageName string) {
	fmt.Printf("\n🧪 测试Harbor项目解析逻辑\n")
	fmt.Printf("原始镜像名称: %s\n", imageName)

	// 模拟shell脚本解析逻辑
	parts := strings.Split(imageName, "/")

	if len(parts) >= 2 {
		projectName := parts[1]
		fmt.Printf("解析到的项目名称: %s\n", projectName)
	} else {
		fmt.Printf("❌ 解析项目名称失败，分割结果: %v\n", parts)
	}

	// 模拟Harbor URL解析
	urlParts := strings.Split(imageName, "/")
	if len(urlParts) >= 1 {
		harborHost := urlParts[0]
		fmt.Printf("解析到的Harbor Host: %s\n", harborHost)

		// 构建Harbor API URL
		harborAPIURL := fmt.Sprintf("https://%s/api/v2.0/projects/", harborHost)
		fmt.Printf("Harbor API URL: %s\n", harborAPIURL)
	}
}

func main() {
	fmt.Println("🧪 测试镜像名称构造和解析逻辑")

	// 测试数据
	testCases := []TestData{
		{
			EnvironmentName: "dev",
			ProductName:     "MyProduct",
			AppName:         "gonotice",
			CommitShortID:   "ae4b8548",
		},
		{
			EnvironmentName: "test",
			ProductName:     "AnotherProduct",
			AppName:         "webapp",
			CommitShortID:   "1234abcd",
		},
		{
			EnvironmentName: "prod",
			ProductName:     "ProductName",
			AppName:         "api-service",
			CommitShortID:   "9876wxyz",
		},
	}

	for i, data := range testCases {
		fmt.Printf("\n📋 测试用例 %d:\n", i+1)
		fmt.Printf("  环境: %s\n", data.EnvironmentName)
		fmt.Printf("  产品: %s\n", data.ProductName)
		fmt.Printf("  应用: %s\n", data.AppName)
		fmt.Printf("  提交ID: %s\n", data.CommitShortID)

		// 构建镜像名称
		imageName := buildImageName(data)
		fmt.Printf("  构建的镜像名称: %s\n", imageName)

		// 测试解析逻辑
		testHarborProjectParsing(imageName)
	}

	fmt.Println("\n✅ 测试完成！")

	// 额外测试：验证ensureHarborURLPrefix函数
	fmt.Println("\n🔧 测试ensureHarborURLPrefix函数:")
	testImages := []string{
		"myapp:latest",
		"project/myapp:v1.0",
		"harbor.fundpark.com/project/myapp:v1.0",
	}

	for _, img := range testImages {
		result := ensureHarborURLPrefix(img, harborURL)
		fmt.Printf("  输入: %s -> 输出: %s\n", img, result)
	}
}
