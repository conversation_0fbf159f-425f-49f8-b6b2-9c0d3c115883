package main

import (
	"fmt"
	"strings"
	"time"
)

// 模拟AppInfo结构
type AppInfo struct {
	UniqTag string
	ID      uint
}

// 模拟PipelineRun名称生成逻辑
func generatePipelineRunName(appInfoObj *AppInfo) string {
	return fmt.Sprintf("build-%s-%d", strings.Replace(appInfoObj.UniqTag, ".", "-", -1), time.Now().Unix())
}

// 模拟workspace路径生成
func generateWorkspacePath(pipelineRunName string) string {
	return fmt.Sprintf("/data/nfsshare/%s", pipelineRunName)
}

func main() {
	fmt.Println("🧪 测试Workspace配置")

	// 测试用例
	testCases := []AppInfo{
		{
			UniqTag: "gonotice.dev.fundpark.com",
			ID:      12,
		},
		{
			UniqTag: "webapp.test.example.com",
			ID:      15,
		},
		{
			UniqTag: "api-service.prod.company.com",
			ID:      8,
		},
	}

	for i, appInfo := range testCases {
		fmt.Printf("\n📋 测试用例 %d:\n", i+1)
		fmt.Printf("  应用UniqTag: %s\n", appInfo.UniqTag)
		fmt.Printf("  应用ID: %d\n", appInfo.ID)

		// 生成PipelineRun名称
		pipelineRunName := generatePipelineRunName(&appInfo)
		fmt.Printf("  PipelineRun名称: %s\n", pipelineRunName)

		// 生成workspace路径
		workspacePath := generateWorkspacePath(pipelineRunName)
		fmt.Printf("  Workspace路径: %s\n", workspacePath)

		fmt.Println(strings.Repeat("-", 80))
	}

	fmt.Println("\n✅ 测试完成！")
	fmt.Println("\n🔧 Workspace配置总结:")
	fmt.Println("1. ✅ 每个PipelineRun使用唯一的子目录")
	fmt.Println("2. ✅ 避免在NFS挂载目录下生成混乱的0和build-context目录")
	fmt.Println("3. ✅ 路径格式: /data/nfsshare/{pipelineRun.Name}")
	fmt.Println("4. ✅ PipelineRun名称格式: build-{uniqTag}-{timestamp}")

	fmt.Println("\n📁 预期的NFS目录结构:")
	fmt.Println("/data/nfsshare/")
	fmt.Println("├── build-gonotice-dev-fundpark-com-1716456789/")
	fmt.Println("│   ├── source/")
	fmt.Println("│   │   └── 12/")
	fmt.Println("│   │       ├── Dockerfile")
	fmt.Println("│   │       └── ...")
	fmt.Println("│   └── ...")
	fmt.Println("├── build-webapp-test-example-com-1716456790/")
	fmt.Println("│   ├── source/")
	fmt.Println("│   └── ...")
	fmt.Println("└── build-api-service-prod-company-com-1716456791/")
	fmt.Println("    ├── source/")
	fmt.Println("    └── ...")
}
