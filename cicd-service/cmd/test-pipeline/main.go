package main

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"os"

	"github.com/devops-microservices/cicd-service/services"

	"github.com/sirupsen/logrus"
)

func main() {
	fmt.Println("🧪 测试Pipeline创建功能")

	// 读取转换后的JSON文件
	jsonFile := "build-pipeline.yaml.json"
	if len(os.Args) > 1 {
		jsonFile = os.Args[1]
	}

	if _, err := os.Stat(jsonFile); os.IsNotExist(err) {
		log.Fatalf("❌ JSON文件不存在: %s", jsonFile)
	}

	fmt.Printf("📖 读取Pipeline配置文件: %s\n", jsonFile)

	// 读取JSON文件
	jsonContent, err := ioutil.ReadFile(jsonFile)
	if err != nil {
		log.Fatalf("❌ 读取JSON文件失败: %v", err)
	}

	// 解析为map
	var pipelineConfig map[string]interface{}
	if err := json.Unmarshal(jsonContent, &pipelineConfig); err != nil {
		log.Fatalf("❌ 解析JSON失败: %v", err)
	}

	// 创建日志记录器
	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)

	// 创建CICDService实例
	cicdService := &services.CICDService{}

	// 测试Pipeline创建
	pipelineName := "test-build-pipeline"
	namespace := "default"

	fmt.Printf("🔧 测试创建Pipeline: %s (namespace: %s)\n", pipelineName, namespace)

	tektonPipeline, err := cicdService.CreateTektonPipelineFromConfig(pipelineConfig, pipelineName, namespace)
	if err != nil {
		log.Fatalf("❌ 创建Pipeline失败: %v", err)
	}

	fmt.Println("✅ Pipeline创建成功！")
	fmt.Printf("  - 名称: %s\n", tektonPipeline.Name)
	fmt.Printf("  - 命名空间: %s\n", tektonPipeline.Namespace)
	fmt.Printf("  - 描述: %s\n", tektonPipeline.Spec.Description)
	fmt.Printf("  - 参数数量: %d\n", len(tektonPipeline.Spec.Params))
	fmt.Printf("  - 任务数量: %d\n", len(tektonPipeline.Spec.Tasks))
	fmt.Printf("  - 工作空间数量: %d\n", len(tektonPipeline.Spec.Workspaces))

	// 详细信息
	fmt.Println("\n📋 Pipeline详细信息:")

	fmt.Println("  参数列表:")
	for _, param := range tektonPipeline.Spec.Params {
		defaultValue := ""
		if param.Default != nil {
			defaultValue = fmt.Sprintf(" (默认: %v)", param.Default.StringVal)
		}
		fmt.Printf("    - %s (%s): %s%s\n", param.Name, param.Type, param.Description, defaultValue)
	}

	fmt.Println("  任务列表:")
	for _, task := range tektonPipeline.Spec.Tasks {
		taskRef := ""
		if task.TaskRef != nil {
			taskRef = task.TaskRef.Name
		}
		runAfter := ""
		if len(task.RunAfter) > 0 {
			runAfter = fmt.Sprintf(" (依赖: %v)", task.RunAfter)
		}
		fmt.Printf("    - %s (taskRef: %s)%s\n", task.Name, taskRef, runAfter)
		fmt.Printf("      参数: %d, 工作空间: %d\n", len(task.Params), len(task.Workspaces))
	}

	fmt.Println("  工作空间列表:")
	for _, workspace := range tektonPipeline.Spec.Workspaces {
		fmt.Printf("    - %s: %s\n", workspace.Name, workspace.Description)
	}

	fmt.Println("\n🎉 测试完成！Pipeline对象创建成功且结构正确。")
}
