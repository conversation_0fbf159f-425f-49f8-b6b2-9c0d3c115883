# 回滚到指定镜像功能实现总结

## 🎯 功能概述

新增了回滚到指定镜像的API端点，这个功能：
- ✅ **指定镜像回滚**：回滚到用户指定的镜像版本
- ✅ **多集群支持**：支持多个Kubernetes集群同时回滚
- ✅ **部署流程复用**：复用现有部署流程，但标记为回滚操作
- ✅ **回滚原因记录**：记录回滚原因和备注信息

## 📋 功能特性

### 1. API端点
- **POST** `/api/v1/cicd/rollback/to-image` - 创建回滚到指定镜像任务

### 2. 请求参数
```json
{
    "app_info_id": 34,                                    // 应用信息ID
    "kubernetes": [2],                                   // Kubernetes集群ID列表
    "image": "harbor.example.com/prod/app:v1.2.3",     // 目标镜像
    "hosts": [],                                         // 主机列表（预留）
    "batch_uuid": "4efc1b89-31aa-4d10-8c5b-76cfabb92d5c", // 批次UUID
    "force": true,                                       // 是否强制
    "rollback": true,                                    // 回滚标识
    "rollback_reason": "0",                             // 回滚原因码
    "rollback_comment": "修复生产环境bug"                // 回滚备注
}
```

### 3. 核心特点
- **类似部署流程**：复用`ExecuteDeployment`方法
- **回滚标记**：`deploy_type = 2`（DeployTypeRollback）
- **多集群支持**：支持同时回滚多个Kubernetes集群
- **完整记录**：记录回滚原因和备注到数据库

## 🔧 技术实现

### 请求结构体
```go
type RollbackToImageRequest struct {
    AppInfoID       uint     `json:"app_info_id" binding:"required"`
    Kubernetes      []uint   `json:"kubernetes"`
    Image           string   `json:"image" binding:"required"`
    Hosts           []uint   `json:"hosts"`
    BatchUUID       string   `json:"batch_uuid" binding:"required"`
    Force           bool     `json:"force"`
    Rollback        bool     `json:"rollback"`
    RollbackReason  string   `json:"rollback_reason"`
    RollbackComment string   `json:"rollback_comment"`
}
```

### 控制器方法
```go
func (c *DeployController) CreateRollbackToImage(ctx *gin.Context)
```

### 部署任务配置
```go
deployJob := models.DeployJob{
    UniqID:          fmt.Sprintf("%s-%d-%d-rollback-image", req.BatchUUID, req.AppInfoID, kubernetesID),
    AppID:           app.AppID,
    AppInfoID:       req.AppInfoID,
    Status:          3, // 部署中
    Image:           req.Image,
    DeployType:      models.DeployTypeRollback, // 版本回退
    RollbackReason:  rollbackReasonInt,
    RollbackComment: req.RollbackComment,
    BatchUUID:       req.BatchUUID,
    DeployerID:      deployerID,
}
```

### Kubernetes配置
```go
kubernetesConfig := map[string]interface{}{
    "kubernetes_id":    kubernetesID,
    "namespace":        fmt.Sprintf("%s-%s", strings.ToLower(environment.Name), strings.ToLower(app.Project.Product.Name)),
    "force":            req.Force,
    "rollback":         req.Rollback,
    "rollback_reason":  req.RollbackReason,
    "rollback_comment": req.RollbackComment,
    "image":            req.Image,
}
```

## 🔄 处理流程

```
回滚到指定镜像流程:
1. 接收回滚请求参数
2. 验证app_info_id和image参数
3. 获取应用、环境信息
4. 解析回滚原因为整数
5. 为每个Kubernetes集群创建回滚任务
6. 设置deploy_type为回滚类型
7. 记录回滚原因和备注
8. 异步执行ExecuteDeployment
9. 返回任务创建结果
```

## 📊 API使用示例

### 请求示例
```bash
POST /api/v1/cicd/rollback/to-image
Content-Type: application/json

{
    "app_info_id": 34,
    "kubernetes": [2],
    "image": "harbor.example.com/prod/user-service:v1.2.3",
    "hosts": [],
    "batch_uuid": "4efc1b89-31aa-4d10-8c5b-76cfabb92d5c",
    "force": true,
    "rollback": true,
    "rollback_reason": "1",
    "rollback_comment": "生产环境bug修复，回滚到稳定版本"
}
```

### 响应示例
```json
{
    "code": 20000,
    "message": "成功创建回滚到指定镜像任务",
    "data": {
        "deploy_jobs": [
            {
                "id": 123,
                "uniq_id": "4efc1b89-31aa-4d10-8c5b-76cfabb92d5c-34-2-rollback-image",
                "app_id": "user-service",
                "app_info_id": 34,
                "status": 3,
                "image": "harbor.example.com/prod/user-service:v1.2.3",
                "deploy_type": 2,
                "rollback_reason": 1,
                "rollback_comment": "生产环境bug修复，回滚到稳定版本",
                "batch_uuid": "4efc1b89-31aa-4d10-8c5b-76cfabb92d5c"
            }
        ],
        "batch_uuid": "4efc1b89-31aa-4d10-8c5b-76cfabb92d5c",
        "target_image": "harbor.example.com/prod/user-service:v1.2.3",
        "rollback_reason": "1",
        "rollback_comment": "生产环境bug修复，回滚到稳定版本",
        "app_name": "user-service",
        "environment": "production"
    }
}
```

## 🆚 与其他回滚功能的区别

### 1. 快速回滚（已有功能）
- **路径**: `POST /api/v1/cicd/rollback`
- **用途**: 快速回滚到上一版本或指定revision
- **机制**: 基于Kubernetes Deployment revision
- **参数**: `app_info_id`, `kubernetes_id`, `to_revision`

### 2. 回滚到指定镜像（新功能）
- **路径**: `POST /api/v1/cicd/rollback/to-image`
- **用途**: 回滚到指定的镜像版本
- **机制**: 类似部署流程，更新镜像
- **参数**: `app_info_id`, `kubernetes[]`, `image`, `batch_uuid`

### 3. 获取回滚历史（已有功能）
- **路径**: `GET /api/v1/cicd/rollback/history`
- **用途**: 查看可回滚的历史版本
- **机制**: 查询ReplicaSet历史
- **参数**: `app_info_id`, `kubernetes_id`

## 🎯 使用场景

### 1. 指定版本回滚
```json
{
    "app_info_id": 34,
    "kubernetes": [1, 2],
    "image": "harbor.example.com/prod/app:v2.1.0",
    "rollback_reason": "1",
    "rollback_comment": "回滚到已知稳定版本v2.1.0"
}
```

### 2. 紧急修复回滚
```json
{
    "app_info_id": 45,
    "kubernetes": [1],
    "image": "harbor.example.com/prod/payment:v1.5.2",
    "force": true,
    "rollback_reason": "0",
    "rollback_comment": "支付服务紧急修复，强制回滚"
}
```

### 3. 多集群同步回滚
```json
{
    "app_info_id": 56,
    "kubernetes": [1, 2, 3],
    "image": "harbor.example.com/prod/gateway:v3.0.1",
    "rollback_reason": "2",
    "rollback_comment": "网关服务多集群同步回滚"
}
```

## ✅ 验证结果

### 编译测试
```bash
cd cicd-service
go build .  # ✅ 编译成功
```

### 功能特点
1. ✅ **参数验证**：完整的请求参数验证
2. ✅ **多集群支持**：支持Kubernetes集群数组
3. ✅ **回滚标记**：正确设置deploy_type为回滚
4. ✅ **原因记录**：记录回滚原因和备注
5. ✅ **异步执行**：使用goroutine异步执行
6. ✅ **错误处理**：完善的错误处理和响应

## 📋 相关文件

- ✅ `controllers/deploy_controller.go` - 新增CreateRollbackToImage方法
- ✅ `routes/routes.go` - 新增/to-image路由
- ✅ `models/models.go` - 使用现有DeployJob模型

## 🎊 总结

新的回滚到指定镜像功能已完全集成：
- 🎯 **精确回滚**：支持回滚到任意指定镜像
- 🔄 **流程复用**：复用现有部署流程保证一致性
- 📊 **多集群支持**：一次请求可回滚多个集群
- 📝 **完整记录**：详细记录回滚原因和过程
- 🚀 **即用即得**：与现有系统完美集成

这个功能为DevOps平台提供了更灵活的回滚能力，满足生产环境中精确版本控制的需求！ 