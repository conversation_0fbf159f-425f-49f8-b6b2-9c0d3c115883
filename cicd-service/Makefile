# DevOps CICD Service Makefile
# 提供快速构建和开发工具

# Go 参数
GOCMD=go
GOBUILD=$(GOCMD) build
GOCLEAN=$(GOCMD) clean
GOTEST=$(GOCMD) test
GOGET=$(GOCMD) get
GOMOD=$(GOCMD) mod

# 应用信息
BINARY_NAME=cicd-service
BINARY_UNIX=$(BINARY_NAME)_unix
BUILD_DIR=build
MAIN_PATH=.

# 构建参数
LDFLAGS=-ldflags "-s -w"
BUILD_TAGS=-tags netgo

# 默认目标
.PHONY: all
all: clean build

# 快速构建 - 最常用的命令
.PHONY: build
build:
	@echo "🔨 构建应用..."
	@mkdir -p $(BUILD_DIR)
	CGO_ENABLED=0 GOOS=linux $(GOBUILD) $(LDFLAGS) $(BUILD_TAGS) -o $(BUILD_DIR)/$(BINARY_NAME) $(MAIN_PATH)
	@echo "✅ 构建完成: $(BUILD_DIR)/$(BINARY_NAME)"

# 快速启动 - 使用预构建的二进制文件
.PHONY: run
run: build
	@echo "🚀 启动服务..."
	@./$(BUILD_DIR)/$(BINARY_NAME)

# 开发模式启动 - 带实时重载
.PHONY: dev
dev:
	@echo "🔧 开发模式启动..."
	@if command -v air > /dev/null; then \
		air; \
	else \
		echo "正在安装 air 工具..."; \
		$(GOGET) -u github.com/cosmtrek/air@latest; \
		air; \
	fi

# 快速启动（如果二进制文件存在则直接运行）
.PHONY: start
start:
	@if [ -f $(BUILD_DIR)/$(BINARY_NAME) ]; then \
		echo "🚀 使用已存在的二进制文件启动..."; \
		./$(BUILD_DIR)/$(BINARY_NAME); \
	else \
		echo "🔨 二进制文件不存在，先构建..."; \
		$(MAKE) run; \
	fi

# 构建优化版本
.PHONY: build-optimized
build-optimized:
	@echo "🔨 构建优化版本..."
	@mkdir -p $(BUILD_DIR)
	CGO_ENABLED=0 GOOS=linux $(GOBUILD) $(LDFLAGS) $(BUILD_TAGS) -a -installsuffix cgo -o $(BUILD_DIR)/$(BINARY_NAME) $(MAIN_PATH)
	@echo "✅ 优化构建完成: $(BUILD_DIR)/$(BINARY_NAME)"

# 并行构建
.PHONY: build-parallel
build-parallel:
	@echo "🔨 并行构建..."
	@mkdir -p $(BUILD_DIR)
	CGO_ENABLED=0 GOOS=linux $(GOBUILD) $(LDFLAGS) $(BUILD_TAGS) -p 4 -o $(BUILD_DIR)/$(BINARY_NAME) $(MAIN_PATH)
	@echo "✅ 并行构建完成: $(BUILD_DIR)/$(BINARY_NAME)"

# 清理
.PHONY: clean
clean:
	@echo "🧹 清理构建文件..."
	@$(GOCLEAN)
	@rm -rf $(BUILD_DIR)
	@echo "✅ 清理完成"

# 依赖管理
.PHONY: deps
deps:
	@echo "📦 下载依赖..."
	$(GOMOD) download
	$(GOMOD) tidy
	@echo "✅ 依赖更新完成"

# 预热 - 下载所有依赖并构建缓存
.PHONY: warm
warm:
	@echo "🔥 预热构建缓存..."
	$(GOMOD) download
	$(GOCMD) build -i $(MAIN_PATH)
	@echo "✅ 预热完成"

# 测试
.PHONY: test
test:
	@echo "🧪 运行测试..."
	$(GOTEST) -v ./...

# 代码检查
.PHONY: lint
lint:
	@echo "🔍 代码检查..."
	@if command -v golangci-lint > /dev/null; then \
		golangci-lint run; \
	else \
		echo "请安装 golangci-lint"; \
	fi

# Docker 构建
.PHONY: docker-build
docker-build:
	@echo "🐳 构建Docker镜像..."
	docker build -t $(BINARY_NAME):latest .
	@echo "✅ Docker镜像构建完成"

# 安装开发工具
.PHONY: install-tools
install-tools:
	@echo "🔧 安装开发工具..."
	$(GOGET) -u github.com/cosmtrek/air@latest
	$(GOGET) -u github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	@echo "✅ 开发工具安装完成"

# 性能分析
.PHONY: profile
profile: build
	@echo "📊 启动性能分析..."
	@./$(BUILD_DIR)/$(BINARY_NAME) -cpuprofile=cpu.prof -memprofile=mem.prof

# Pipeline相关命令
.PHONY: convert-pipeline
convert-pipeline:
	@echo "🔄 转换Pipeline YAML到JSON..."
	@if [ -z "$(YAML_FILE)" ]; then \
		echo "用法: make convert-pipeline YAML_FILE=<pipeline.yaml>"; \
		echo "示例: make convert-pipeline YAML_FILE=k8s/tekton/build-pipeline.yaml"; \
		exit 1; \
	fi
	@go run cmd/convert-pipeline/main.go $(YAML_FILE)

# 快速转换build-pipeline
.PHONY: convert-build-pipeline
convert-build-pipeline:
	@echo "🔄 转换build-pipeline.yaml..."
	@go run cmd/convert-pipeline/main.go k8s/tekton/build-pipeline.yaml

# 测试Pipeline创建功能
.PHONY: test-pipeline
test-pipeline: convert-build-pipeline
	@echo "🧪 测试Pipeline创建功能..."
	@go run cmd/test-pipeline/main.go

# 测试指定的Pipeline文件
.PHONY: test-pipeline-file
test-pipeline-file:
	@echo "🧪 测试指定Pipeline文件..."
	@if [ -z "$(JSON_FILE)" ]; then \
		echo "用法: make test-pipeline-file JSON_FILE=<pipeline.json>"; \
		echo "示例: make test-pipeline-file JSON_FILE=build-pipeline.yaml.json"; \
		exit 1; \
	fi
	@go run cmd/test-pipeline/main.go $(JSON_FILE)

# 测试镜像名称构造逻辑
.PHONY: test-image-name
test-image-name:
	@echo "🧪 测试镜像名称构造逻辑..."
	@go run cmd/test-image-name/main.go

# 测试Harbor解析逻辑
.PHONY: test-harbor-parsing
test-harbor-parsing:
	@echo "🔍 测试Harbor解析逻辑..."
	@go run cmd/test-harbor-parsing/main.go

# 测试Workspace配置
.PHONY: test-workspace
test-workspace:
	@echo "📁 测试Workspace配置..."
	@go run cmd/test-workspace/main.go

# 测试workspace简化配置
.PHONY: test-workspace-simplified
test-workspace-simplified:
	@echo "🎯 测试Workspace简化配置..."
	@echo "✅ Pipeline配置验证:"
	@echo "  - fetch-source任务不再需要serviceName参数"
	@echo "  - build-image任务不再需要serviceName参数"
	@echo "  - 目录结构: /data/nfsshare/{pipelineRun.Name}/source/"
	@go run cmd/test-workspace/main.go

# 测试workspace极简配置
.PHONY: test-workspace-ultra-simplified
test-workspace-ultra-simplified:
	@echo "🚀 测试Workspace极简配置..."
	@go run cmd/test-workspace-simplified/main.go

# 测试GitLab认证逻辑
.PHONY: test-gitlab-auth
test-gitlab-auth:
	@echo "🔐 测试GitLab认证逻辑..."
	@go run cmd/test-gitlab-auth/main.go

# 诊断PipelineRun认证问题
.PHONY: diagnose-pipeline-auth
diagnose-pipeline-auth:
	@echo "🔍 诊断PipelineRun认证问题..."
	@./scripts/diagnose-pipeline-auth.sh

# 修复GitLab认证问题
.PHONY: fix-gitlab-auth
fix-gitlab-auth:
	@echo "🔧 运行GitLab认证修复脚本..."
	@./scripts/fix-gitlab-auth.sh

# 迁移GitLab Secret格式
.PHONY: migrate-gitlab-secret
migrate-gitlab-secret:
	@echo "🔄 迁移GitLab Secret格式..."
	@./scripts/migrate-gitlab-secret.sh

# 部署GitLab Secret
.PHONY: deploy-gitlab-secret
deploy-gitlab-secret:
	@echo "🚀 部署GitLab Secret..."
	@./scripts/deploy-gitlab-secret.sh

# 帮助信息
.PHONY: help
help:
	@echo "DevOps CICD Service 构建工具"
	@echo ""
	@echo "常用命令:"
	@echo "  make start          - 快速启动服务（推荐）"
	@echo "  make run            - 构建并启动服务"
	@echo "  make build          - 只构建二进制文件"
	@echo "  make dev            - 开发模式（实时重载）"
	@echo ""
	@echo "优化命令:"
	@echo "  make build-parallel - 并行构建"
	@echo "  make warm           - 预热构建缓存"
	@echo "  make deps           - 更新依赖"
	@echo ""
	@echo "Pipeline工具:"
	@echo "  make convert-build-pipeline    - 转换build-pipeline.yaml"
	@echo "  make convert-pipeline YAML_FILE=<file> - 转换指定YAML文件"
	@echo "  make test-pipeline             - 测试Pipeline创建功能"
	@echo "  make test-pipeline-file JSON_FILE=<file> - 测试指定JSON文件"
	@echo "  make test-image-name           - 测试镜像名称构造逻辑"
	@echo "  make test-harbor-parsing       - 测试Harbor解析逻辑"
	@echo "  make test-workspace            - 测试Workspace配置"
	@echo "  make test-workspace-simplified - 测试Workspace简化配置"
	@echo "  make test-workspace-ultra-simplified - 测试Workspace极简配置"
	@echo "  make test-gitlab-auth          - 测试GitLab认证逻辑"
	@echo "  make diagnose-pipeline-auth    - 诊断PipelineRun认证问题"
	@echo "  make fix-gitlab-auth           - 修复GitLab认证问题"
	@echo "  make migrate-gitlab-secret     - 迁移GitLab Secret格式"
	@echo "  make deploy-gitlab-secret      - 部署GitLab Secret"
	@echo ""
	@echo "其他命令:"
	@echo "  make clean          - 清理构建文件"
	@echo "  make test           - 运行测试"
	@echo "  make docker-build   - 构建Docker镜像"
	@echo "  make install-tools  - 安装开发工具" 