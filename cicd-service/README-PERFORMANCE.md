# CICD服务性能优化指南

## 启动速度优化

本文档提供了多种方式来优化`go run main.go`启动慢的问题。

### 🚀 快速启动方案

#### 1. 使用Makefile（推荐）

```bash
# 查看所有可用命令
make help

# 快速启动（如果二进制文件存在则直接运行）
make start

# 构建并启动
make run

# 开发模式（实时重载）
make dev

# 并行构建
make build-parallel

# 预热构建缓存
make warm
```

#### 2. 使用启动脚本

```bash
# 快速启动
./start.sh -f

# 开发模式
./start.sh -d

# 跳过数据库迁移启动（加快启动）
./start.sh -s

# 延迟初始化模式
./start.sh -l

# 组合使用
./start.sh -f -s -l
```

#### 3. 直接使用优化后的二进制文件

```bash
# 首次构建
make build

# 之后直接运行（最快）
./build/cicd-service

# 或使用启动参数
./build/cicd-service -skip-migration -lazy-init
```

### ⚡ 性能优化选项

#### 启动参数优化

| 参数 | 说明 | 效果 |
|------|------|------|
| `-skip-migration` | 跳过数据库迁移检查 | 节省2-5秒 |
| `-lazy-init` | 延迟初始化非关键组件 | 节省3-8秒 |
| `-port 8080` | 直接指定端口 | 跳过配置验证 |

#### 构建优化

```bash
# 优化构建标志
CGO_ENABLED=0 go build -ldflags "-s -w" -tags netgo -o cicd-service .

# 并行构建
go build -p 4 -o cicd-service .

# 预构建缓存
go build -i .
```

### 📊 性能分析

#### 启用性能分析

```bash
# CPU分析
./build/cicd-service -cpuprofile=cpu.prof

# 内存分析
./build/cicd-service -memprofile=mem.prof

# 使用脚本
./start.sh --profile
```

#### 查看分析结果

```bash
# CPU分析
go tool pprof cpu.prof

# 内存分析
go tool pprof mem.prof
```

### 🔧 开发模式优化

#### 使用Air实时重载

```bash
# 安装air
go install github.com/cosmtrek/air@latest

# 启动开发模式
make dev
# 或
./start.sh -d
```

#### Air配置优化

`.air.toml`文件已经配置了：
- 排除不必要的目录监控
- 优化重建延迟
- 减少内存使用

### 📈 启动时间对比

| 启动方式 | 首次启动 | 后续启动 | 说明 |
|----------|----------|----------|------|
| `go run main.go` | 15-30秒 | 15-30秒 | 每次重新编译 |
| `make start` | 8-15秒 | 2-5秒 | 使用预构建文件 |
| `./start.sh -f -s -l` | 5-10秒 | 1-3秒 | 最优化配置 |
| `./build/cicd-service -skip-migration -lazy-init` | 3-8秒 | 1-3秒 | 直接运行二进制 |

### 🛠️ 最佳实践

#### 日常开发

```bash
# 1. 首次启动
make warm && make dev

# 2. 代码修改后（自动重载）
# air会自动检测文件变化并重新编译

# 3. 快速测试
make start
```

#### 生产部署

```bash
# 1. 优化构建
make build-optimized

# 2. 启动服务
./build/cicd-service -skip-migration
```

#### CI/CD流水线

```bash
# 1. 并行构建
make build-parallel

# 2. 容器化部署
make docker-build
```

### 🔍 故障排除

#### 常见启动慢的原因

1. **依赖下载慢**
   ```bash
   # 解决方案：预热依赖
   make deps
   go mod download
   ```

2. **数据库连接慢**
   ```bash
   # 解决方案：跳过迁移
   ./start.sh -s
   ```

3. **Kubernetes客户端初始化慢**
   ```bash
   # 解决方案：延迟初始化
   ./start.sh -l
   ```

4. **编译时间长**
   ```bash
   # 解决方案：使用预构建文件
   make build && ./build/cicd-service
   ```

#### 性能监控

```bash
# 查看启动时间
./build/cicd-service | grep "服务启动耗时"

# 查看内存使用
./build/cicd-service | grep "内存使用"

# 启用详细日志
export LOG_LEVEL=debug
./build/cicd-service
```

### 📋 快速参考

#### 最快启动命令

```bash
# 第一次
make build && ./build/cicd-service -skip-migration -lazy-init

# 后续启动
./build/cicd-service -skip-migration -lazy-init
```

#### 开发环境推荐

```bash
# 一次性设置
make install-tools
make warm

# 日常开发
make dev
```

#### 生产环境推荐

```bash
# 构建
make build-optimized

# 启动
./build/cicd-service -skip-migration
```

### 💡 额外优化建议

1. **使用SSD硬盘** - 显著提升编译和IO性能
2. **增加内存** - 减少swap使用，提升编译速度
3. **配置Go模块代理** - 加速依赖下载
4. **使用构建缓存** - Docker层缓存或本地构建缓存
5. **优化Docker镜像** - 使用多阶段构建和Alpine镜像

### 🔗 相关资源

- [Go编译优化](https://golang.org/doc/go1.18#compiler)
- [Air实时重载工具](https://github.com/cosmtrek/air)
- [Go性能分析](https://golang.org/doc/diagnostics.html) 