#!/bin/bash

# DevOps CICD Service 快速启动脚本
# 提供多种启动方式以优化不同场景下的启动速度

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 服务信息
SERVICE_NAME="cicd-service"
BUILD_DIR="build"
BINARY_PATH="$BUILD_DIR/$SERVICE_NAME"

# 帮助信息
show_help() {
    echo "DevOps CICD Service 启动脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help          显示帮助信息"
    echo "  -f, --fast          快速启动（使用预构建文件）"
    echo "  -d, --dev           开发模式（实时重载）"
    echo "  -b, --build         重新构建并启动"
    echo "  -s, --skip-migrate  跳过数据库迁移"
    echo "  -l, --lazy          延迟初始化非关键组件"
    echo "  -p, --port PORT     指定端口"
    echo "  -c, --config PATH   指定配置文件路径"
    echo "  --profile           启用性能分析"
    echo ""
    echo "示例:"
    echo "  $0 -f               # 快速启动"
    echo "  $0 -d               # 开发模式"
    echo "  $0 -b -s            # 重新构建并跳过迁移"
    echo "  $0 -p 8080          # 指定端口启动"
}

# 检查依赖
check_dependencies() {
    if ! command -v go &> /dev/null; then
        echo -e "${RED}❌ Go 未安装，请先安装 Go${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ Go 版本: $(go version)${NC}"
}

# 快速启动（使用预构建的二进制文件）
fast_start() {
    echo -e "${BLUE}🚀 快速启动模式${NC}"
    
    if [ -f "$BINARY_PATH" ]; then
        echo -e "${GREEN}✅ 使用已存在的二进制文件启动${NC}"
        exec ./"$BINARY_PATH" "$@"
    else
        echo -e "${YELLOW}⚠️ 二进制文件不存在，先构建...${NC}"
        build_and_start "$@"
    fi
}

# 构建并启动
build_and_start() {
    echo -e "${BLUE}🔨 构建并启动${NC}"
    
    # 创建构建目录
    mkdir -p "$BUILD_DIR"
    
    # 构建
    echo -e "${YELLOW}📦 构建应用...${NC}"
    CGO_ENABLED=0 go build -ldflags "-s -w" -tags netgo -o "$BINARY_PATH" .
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 构建完成${NC}"
        echo -e "${BLUE}🚀 启动服务...${NC}"
        exec ./"$BINARY_PATH" "$@"
    else
        echo -e "${RED}❌ 构建失败${NC}"
        exit 1
    fi
}

# 开发模式（实时重载）
dev_mode() {
    echo -e "${BLUE}🔧 开发模式启动${NC}"
    
    # 检查 air 工具
    if ! command -v air &> /dev/null; then
        echo -e "${YELLOW}📦 安装 air 工具...${NC}"
        go install github.com/cosmtrek/air@latest
    fi
    
    echo -e "${GREEN}✅ 使用 air 启动实时重载${NC}"
    exec air
}

# 清理旧文件
clean_build() {
    echo -e "${YELLOW}🧹 清理构建文件...${NC}"
    rm -rf "$BUILD_DIR"
    go clean
    echo -e "${GREEN}✅ 清理完成${NC}"
}

# 解析命令行参数
FAST_MODE=false
DEV_MODE=false
BUILD_MODE=false
SKIP_MIGRATE=false
LAZY_INIT=false
PROFILE_MODE=false
PORT=""
CONFIG_PATH=""
GO_ARGS=""

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -f|--fast)
            FAST_MODE=true
            shift
            ;;
        -d|--dev)
            DEV_MODE=true
            shift
            ;;
        -b|--build)
            BUILD_MODE=true
            shift
            ;;
        -s|--skip-migrate)
            SKIP_MIGRATE=true
            GO_ARGS="$GO_ARGS -skip-migration"
            shift
            ;;
        -l|--lazy)
            LAZY_INIT=true
            GO_ARGS="$GO_ARGS -lazy-init"
            shift
            ;;
        -p|--port)
            PORT="$2"
            GO_ARGS="$GO_ARGS -port $PORT"
            shift 2
            ;;
        -c|--config)
            CONFIG_PATH="$2"
            GO_ARGS="$GO_ARGS -config $CONFIG_PATH"
            shift 2
            ;;
        --profile)
            PROFILE_MODE=true
            GO_ARGS="$GO_ARGS -cpuprofile=cpu.prof -memprofile=mem.prof"
            shift
            ;;
        --clean)
            clean_build
            exit 0
            ;;
        *)
            echo -e "${RED}❌ 未知参数: $1${NC}"
            show_help
            exit 1
            ;;
    esac
done

# 主逻辑
main() {
    echo -e "${BLUE}🎯 DevOps CICD Service 启动脚本${NC}"
    echo -e "${YELLOW}📅 $(date)${NC}"
    
    # 检查依赖
    check_dependencies
    
    # 根据模式启动
    if [ "$DEV_MODE" = true ]; then
        dev_mode
    elif [ "$FAST_MODE" = true ]; then
        fast_start $GO_ARGS
    elif [ "$BUILD_MODE" = true ]; then
        build_and_start $GO_ARGS
    else
        # 默认行为：尝试快速启动
        fast_start $GO_ARGS
    fi
}

# 运行主函数
main "$@" 