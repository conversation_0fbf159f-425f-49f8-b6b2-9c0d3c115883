# CICD Service

CICD Service 是一个基于 Go 开发的微服务，用于处理持续集成和持续部署流程。该服务利用 Tekton 作为底层 CI/CD 引擎，提供 RESTful API 接口用于创建、管理和监控构建和部署任务。

## 功能特性

- 构建管理：创建、查询、取消构建任务
- 部署管理：支持 Kubernetes 和 Docker 部署
- 发布工单：支持工单流程管理，多应用同时发布
- 事件通知：通过 NATS 消息队列发布构建和部署事件
- 多环境支持：支持测试、预发布、生产等多环境

## 技术栈

- 语言：Go 1.21+
- Web 框架：Gin
- ORM：GORM
- 配置管理：Viper
- CI/CD 引擎：Tekton Pipelines
- 容器编排：Kubernetes
- 消息队列：NATS
- 数据库：MySQL/PostgreSQL/SQLite

## 快速开始

### 前置要求

- Go 1.21+
- Docker
- Kubernetes 集群
- Tekton Pipelines 已安装
- 数据库（MySQL/PostgreSQL/SQLite）

### 安装

1. 克隆仓库

```shell
git clone https://github.com/your-org/cicd-service.git
cd cicd-service
```

2. 安装依赖

```shell
go mod download
```

3. 配置

修改 `config/config.yaml` 文件，设置数据库、Harbor 和 Kubernetes 等配置。

4. 构建

```shell
go build -o cicd-service
```

5. 运行

```shell
./cicd-service
```

### Docker 部署

```shell
docker build -t cicd-service:latest .
docker run -p 8080:8080 cicd-service:latest
```

### Kubernetes 部署

```shell
kubectl apply -f k8s/deployment.yaml
```

## API 文档

服务启动后，可以访问 `/swagger/index.html` 查看 API 文档。

主要 API 端点：

- `GET /health` - 健康检查
- `POST /api/v1/builds` - 创建构建
- `GET /api/v1/builds` - 列出构建
- `POST /api/v1/deploys` - 创建部署
- `POST /api/v1/orders` - 创建发布工单

## 配置项

| 配置项 | 描述 | 默认值 |
|-------|------|-------|
| server.port | HTTP 服务器端口 | 8080 |
| database.type | 数据库类型 (mysql/postgres/sqlite) | mysql |
| database.host | 数据库主机 | localhost |
| harbor.url | Harbor 仓库 URL | harbor.fundpark.com |
| tekton.namespace | Tekton 命名空间 | default |

## 本地开发

1. 设置环境变量

```shell
export CICD_DATABASE_USERNAME=root
export CICD_DATABASE_PASSWORD=password
```

2. 运行测试

```shell
go test -v ./...
```

## 贡献指南

欢迎提交 Issue 和 Pull Request。

## 许可证

[MIT License](LICENSE) 