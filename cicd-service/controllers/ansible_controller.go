package controllers

import (
	"net/http"

	"github.com/devops-microservices/cicd-service/models"
	"github.com/devops-microservices/cicd-service/services"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// AnsibleController 处理Ansible相关的API请求
type AnsibleController struct {
	ansibleService *services.AnsibleService
	logger         logrus.FieldLogger
}

// NewAnsibleController 创建新的Ansible控制器
func NewAnsibleController(ansibleService *services.AnsibleService, logger logrus.FieldLogger) *AnsibleController {
	return &AnsibleController{
		ansibleService: ansibleService,
		logger:         logger,
	}
}

// RegisterAnsibleRoutes 注册Ansible相关的路由
func RegisterAnsibleRoutes(router *gin.Engine, ansibleService *services.AnsibleService, logger logrus.FieldLogger) {
	controller := NewAnsibleController(ansibleService, logger)

	ansibleGroup := router.Group("/api/v1/ansible")
	{
		ansibleGroup.POST("/deploy", controller.DeployDocker)
		ansibleGroup.GET("/status/:id", controller.GetDeploymentStatus)
		ansibleGroup.POST("/cancel/:id", controller.CancelDeployment)
		ansibleGroup.POST("/playbook", controller.RunPlaybook)
		ansibleGroup.GET("/playbook/:id", controller.GetPlaybookResult)
	}
}

// DeployDocker 使用Ansible部署Docker应用
func (c *AnsibleController) DeployDocker(ctx *gin.Context) {
	var params models.DockerDeploymentParams
	if err := ctx.ShouldBindJSON(&params); err != nil {
		c.logger.Warnf("无效的请求数据: %v", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的请求数据: " + err.Error(),
		})
		return
	}

	deployID, err := c.ansibleService.DeployDocker(ctx.Request.Context(), &params)
	if err != nil {
		c.logger.Errorf("Ansible部署失败: %v", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "Ansible部署失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"deploy_id": deployID,
		"message":   "应用部署已开始",
	})
}

// GetDeploymentStatus 获取Ansible部署状态
func (c *AnsibleController) GetDeploymentStatus(ctx *gin.Context) {
	deployID := ctx.Param("id")
	if deployID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "部署ID不能为空",
		})
		return
	}

	status, err := c.ansibleService.GetDeploymentStatus(ctx.Request.Context(), deployID)
	if err != nil {
		c.logger.Errorf("获取部署状态失败: %v", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取部署状态失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, status)
}

// CancelDeployment 取消Ansible部署
func (c *AnsibleController) CancelDeployment(ctx *gin.Context) {
	deployID := ctx.Param("id")
	if deployID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "部署ID不能为空",
		})
		return
	}

	err := c.ansibleService.CancelDeployment(ctx.Request.Context(), deployID)
	if err != nil {
		c.logger.Errorf("取消部署失败: %v", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "取消部署失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"message": "部署任务已取消",
	})
}

// RunPlaybook 运行自定义Ansible Playbook
func (c *AnsibleController) RunPlaybook(ctx *gin.Context) {
	var request services.RunPlaybookParams
	if err := ctx.ShouldBindJSON(&request); err != nil {
		c.logger.Warnf("无效的请求数据: %v", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的请求数据: " + err.Error(),
		})
		return
	}

	// 验证必填字段
	if request.Playbook == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Playbook文件名不能为空",
		})
		return
	}

	if len(request.Inventory) == 0 {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "Inventory主机列表不能为空",
		})
		return
	}

	// 运行Playbook
	result, err := c.ansibleService.RunPlaybook(ctx.Request.Context(), &request)
	if err != nil {
		c.logger.Errorf("运行Playbook失败: %v", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "运行Playbook失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"id":      result.ID,
		"status":  result.Status,
		"message": "Playbook执行已开始",
	})
}

// GetPlaybookResult 获取Playbook执行结果
func (c *AnsibleController) GetPlaybookResult(ctx *gin.Context) {
	runID := ctx.Param("id")
	if runID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "运行ID不能为空",
		})
		return
	}

	result, err := c.ansibleService.GetPlaybookResult(ctx.Request.Context(), runID)
	if err != nil {
		c.logger.Errorf("获取Playbook结果失败: %v", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取Playbook结果失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, result)
}
