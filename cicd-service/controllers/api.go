package controllers

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/devops-microservices/cicd-service/models"
	"github.com/devops-microservices/cicd-service/services"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// APIController 处理API请求
type APIController struct {
	cicdService    *services.CICDService
	publishService *services.PublishService
	logger         logrus.FieldLogger
	db             *gorm.DB
}

// NewAPIController 创建新的API控制器
func NewAPIController(cicdService *services.CICDService, publishService *services.PublishService, logger logrus.FieldLogger, db *gorm.DB) *APIController {
	return &APIController{
		cicdService:    cicdService,
		publishService: publishService,
		logger:         logger,
		db:             db,
	}
}

// HealthCheck 健康检查
func (c *APIController) HealthCheck(ctx *gin.Context) {
	ctx.JSON(http.StatusOK, gin.H{
		"status": "ok",
	})
}

// CreatePipeline 创建流水线
func (c *APIController) CreatePipeline(ctx *gin.Context) {
	appID := ctx.Param("id")
	if appID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "应用ID不能为空",
		})
		return
	}

	// 解析请求体中的pipeline YAML
	var requestBody struct {
		PipelineYAML string `json:"pipeline_yaml" binding:"required"`
	}

	if err := ctx.ShouldBindJSON(&requestBody); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的请求参数: " + err.Error(),
		})
		return
	}

	// 构建服务请求
	req := services.CreatePipelineRequest{
		AppID:        appID,
		PipelineYAML: requestBody.PipelineYAML,
	}

	pipeline, err := c.cicdService.CreatePipeline(ctx.Request.Context(), req)
	if err != nil {
		c.logger.Errorf("创建流水线失败: AppID=%s, Error=%v", appID, err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "创建流水线失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"message":  "流水线创建成功",
		"pipeline": pipeline,
		"app_id":   appID,
	})
}

// CreateBuild 创建构建
func (c *APIController) CreateBuild(ctx *gin.Context) {
	var req models.BuildRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的请求参数: " + err.Error(),
		})
		return
	}

	buildID, err := c.cicdService.CreateBuild(ctx.Request.Context(), req)
	if err != nil {
		c.logger.Errorf("创建构建失败: %v", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "创建构建失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"build_id": buildID,
		"message":  "构建任务已创建",
	})
}

// ListBuilds 列出构建
func (c *APIController) ListBuilds(ctx *gin.Context) {
	// 解析查询参数
	pageStr := ctx.DefaultQuery("page", "1")
	pageSizeStr := ctx.DefaultQuery("page_size", "10")
	appInfoID := ctx.Query("app_info_id")
	status := ctx.Query("status")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	pageSize, err := strconv.Atoi(pageSizeStr)
	if err != nil || pageSize < 1 {
		pageSize = 10
	}

	// 创建查询条件
	query := c.db.Model(&models.BuildJob{}).Preload("Deployer")

	// 添加过滤条件
	if appInfoID != "" {
		if appInfoIDInt, err := strconv.Atoi(appInfoID); err == nil {
			query = query.Where("app_info_id = ?", appInfoIDInt)
		}
	}

	if status != "" {
		if statusInt, err := strconv.Atoi(status); err == nil {
			query = query.Where("status = ?", statusInt)
		}
	}

	// 计算总记录数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		c.logger.Errorf("统计构建任务总数失败: %v", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "查询构建任务失败",
			"data":    nil,
		})
		return
	}

	// 查询分页数据
	var buildJobs []models.BuildJob
	if err := query.Order("id DESC").
		Limit(pageSize).
		Offset((page - 1) * pageSize).
		Find(&buildJobs).Error; err != nil {
		c.logger.Errorf("查询构建任务列表失败: %v", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "查询构建任务失败",
			"data":    nil,
		})
		return
	}

	// 准备返回的数据
	var items []map[string]interface{}

	// 处理每条记录
	for _, job := range buildJobs {
		item := make(map[string]interface{})

		// 复制基本字段
		item["id"] = job.ID
		item["created_time"] = job.CreatedAt
		item["update_time"] = job.UpdatedAt
		item["order_id"] = job.OrderID
		item["appid"] = job.AppID
		item["app_info_id"] = job.AppInfoID
		item["deployer"] = job.DeployerID
		item["status"] = job.Status
		item["queue_number"] = job.QueueNumber
		item["build_number"] = job.BuildNumber
		item["is_deploy"] = job.IsDeploy
		item["image"] = job.Image
		item["sync_status"] = job.SyncStatus
		item["modules"] = job.Modules
		item["batch_uuid"] = job.BatchUUID

		// 处理deployer_info
		if job.Deployer != nil {
			item["deployer_info"] = job.Deployer
		}

		// 解析CommitTag字段
		if job.CommitTag != "" {
			var commitTag models.CommitTag
			if err := json.Unmarshal([]byte(job.CommitTag), &commitTag); err == nil {
				item["commit_tag"] = commitTag
			} else {
				item["commit_tag"] = nil
			}
		}

		// 解析Commits字段
		if job.Commits != "" {
			var commits models.CommitInfo
			if err := json.Unmarshal([]byte(job.Commits), &commits); err == nil {
				item["commits"] = commits
			} else {
				item["commits"] = nil
			}
		}

		items = append(items, item)
	}

	// 构建分页响应
	ctx.JSON(http.StatusOK, gin.H{
		"code": 20000,
		"data": gin.H{
			"items":    items,
			"total":    total,
			"page":     page,
			"next":     nil,
			"previous": nil,
		},
	})
}

// GetBuild 获取构建详情
func (c *APIController) GetBuild(ctx *gin.Context) {
	buildID := ctx.Param("id")
	if buildID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "构建ID不能为空",
		})
		return
	}

	pipelineStatus, err := c.cicdService.GetPipelineStatus(ctx.Request.Context(), buildID)
	if err != nil {
		c.logger.Errorf("获取构建状态失败: %v", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取构建状态失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, pipelineStatus)
}

// CancelBuild 取消构建
func (c *APIController) CancelBuild(ctx *gin.Context) {
	buildID := ctx.Param("id")
	if buildID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "构建ID不能为空",
		})
		return
	}

	err := c.cicdService.CancelBuild(ctx.Request.Context(), buildID)
	if err != nil {
		c.logger.Errorf("取消构建失败: %v", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "取消构建失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"message": "构建任务已取消",
	})
}

// BuildCI 应用持续集成
func (c *APIController) BuildCI(ctx *gin.Context) {
	appID := ctx.Param("app_info_id")
	if appID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "应用模块ID不能为空",
		})
		return
	}

	// 解析appID为uint
	id, err := strconv.ParseUint(appID, 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的应用模块ID",
		})
		return
	}

	// 解析请求体
	var req models.CIRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的请求参数: " + err.Error(),
		})
		return
	}

	// 生成唯一的批次UUID（如果没有提供）
	if req.BatchUUID == "" {
		req.BatchUUID = fmt.Sprintf("batch-%s", time.Now().Format("20060102150405"))
	}

	// 确保Modules参数正确设置
	// 前端应用通常需要modules参数指定构建的模块
	if req.Modules == "" && ctx.Query("modules") != "" {
		req.Modules = ctx.Query("modules")
	}

	// 调用服务层方法执行持续集成
	buildID, err := c.cicdService.RunCI(ctx.Request.Context(), uint(id), req)
	if err != nil {
		c.logger.Errorf("执行持续集成失败: %v", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":  50000,
			"error": "执行持续集成失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 20000,
		"data": gin.H{
			"build_id": buildID,
			"message":  "构建任务已创建",
			"status":   "success",
		},
	})
}

// CreateDeploy 创建部署
func (c *APIController) CreateDeploy(ctx *gin.Context) {
	var req models.DeployRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的请求参数: " + err.Error(),
		})
		return
	}

	deployID, err := c.cicdService.CreateDeploy(ctx.Request.Context(), req)
	if err != nil {
		c.logger.Errorf("创建部署失败: %v", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "创建部署失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"deploy_id": deployID,
		"message":   "部署任务已创建",
	})
}

// ListDeploys 列出部署
func (c *APIController) ListDeploys(ctx *gin.Context) {
	// 解析查询参数
	pageStr := ctx.DefaultQuery("page", "1")
	pageSizeStr := ctx.DefaultQuery("page_size", "10")
	appInfoID := ctx.Query("app_info_id")
	status := ctx.Query("status")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	pageSize, err := strconv.Atoi(pageSizeStr)
	if err != nil || pageSize < 1 {
		pageSize = 10
	}

	// 创建查询条件
	query := c.db.Model(&models.DeployJob{}).Preload("Deployer")

	// 添加过滤条件
	if appInfoID != "" {
		if appInfoIDInt, err := strconv.Atoi(appInfoID); err == nil {
			query = query.Where("app_info_id = ?", appInfoIDInt)
		}
	}

	if status != "" {
		if statusInt, err := strconv.Atoi(status); err == nil {
			query = query.Where("status = ?", statusInt)
		}
	}

	// 计算总记录数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		c.logger.Errorf("统计部署任务总数失败: %v", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "查询部署任务失败",
			"data":    nil,
		})
		return
	}

	// 查询分页数据
	var deployJobs []models.DeployJob
	if err := query.Order("id DESC").
		Limit(pageSize).
		Offset((page - 1) * pageSize).
		Find(&deployJobs).Error; err != nil {
		c.logger.Errorf("查询部署任务列表失败: %v", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "查询部署任务失败",
			"data":    nil,
		})
		return
	}

	// 准备返回的数据
	var items []map[string]interface{}

	// 处理每条记录
	for _, job := range deployJobs {
		item := make(map[string]interface{})

		// 复制基本字段
		item["id"] = job.ID
		item["created_time"] = job.CreatedAt
		item["update_time"] = job.UpdatedAt
		item["uniq_id"] = job.UniqID
		item["order_id"] = job.OrderID
		item["appid"] = job.AppID
		item["app_info_id"] = job.AppInfoID
		item["deployer"] = job.DeployerID
		item["status"] = job.Status
		item["image"] = job.Image
		item["deploy_type"] = job.DeployType
		item["rollback_reason"] = job.RollbackReason
		item["rollback_comment"] = job.RollbackComment
		item["modules"] = job.Modules
		item["batch_uuid"] = job.BatchUUID

		// 处理deployer_info
		if job.Deployer != nil {
			item["deployer_info"] = job.Deployer
		}

		// 解析Kubernetes字段
		if job.Kubernetes != "" {
			var kubernetesConfig []map[string]interface{}
			if err := json.Unmarshal([]byte(job.Kubernetes), &kubernetesConfig); err == nil {
				item["kubernetes"] = kubernetesConfig
			} else {
				// 尝试解析为单个对象
				var singleConfig map[string]interface{}
				if err := json.Unmarshal([]byte(job.Kubernetes), &singleConfig); err == nil {
					item["kubernetes"] = []map[string]interface{}{singleConfig}
				} else {
					item["kubernetes"] = nil
				}
			}
		}

		items = append(items, item)
	}

	// 构建分页响应
	ctx.JSON(http.StatusOK, gin.H{
		"code": 20000,
		"data": gin.H{
			"items":    items,
			"total":    total,
			"page":     page,
			"next":     nil,
			"previous": nil,
		},
	})
}

// GetDeploy 获取部署详情
func (c *APIController) GetDeploy(ctx *gin.Context) {
	deployID := ctx.Param("id")
	if deployID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "部署ID不能为空",
		})
		return
	}

	status, err := c.cicdService.GetDeployStatus(ctx.Request.Context(), deployID)
	if err != nil {
		c.logger.Errorf("获取部署状态失败: %v", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取部署状态失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, status)
}

// CancelDeploy 取消部署
func (c *APIController) CancelDeploy(ctx *gin.Context) {
	deployID := ctx.Param("id")
	if deployID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "部署ID不能为空",
		})
		return
	}

	err := c.cicdService.CancelDeploy(ctx.Request.Context(), deployID)
	if err != nil {
		c.logger.Errorf("取消部署失败: %v", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "取消部署失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"message": "部署任务已取消",
	})
}

// CreateOrder 创建工单
func (c *APIController) CreateOrder(ctx *gin.Context) {
	var req struct {
		Title     string `json:"title" binding:"required"`
		Content   string `json:"content" binding:"required"`
		CreatorID uint   `json:"creator_id" binding:"required"`
		Apps      []uint `json:"apps" binding:"required"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		c.logger.Warnf("无效的请求数据: %v", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的请求数据: " + err.Error(),
		})
		return
	}

	order := &models.PublishOrder{
		OrderID:   generateOrderID(),
		Title:     req.Title,
		Content:   req.Content,
		Status:    0, // 未发布
		CreatorID: req.CreatorID,
	}

	createdOrder, err := c.publishService.CreatePublishOrder(ctx.Request.Context(), order, req.Apps)
	if err != nil {
		c.logger.Errorf("创建工单失败: %v", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "创建工单失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"order":   createdOrder,
		"message": "工单已创建",
	})
}

// ListOrders 列出工单
func (c *APIController) ListOrders(ctx *gin.Context) {
	params := make(map[string]interface{})

	// 解析查询参数
	if status := ctx.Query("status"); status != "" {
		statusInt, err := strconv.Atoi(status)
		if err == nil {
			params["status"] = statusInt
		}
	}

	if env := ctx.Query("env"); env != "" {
		envInt, err := strconv.Atoi(env)
		if err == nil {
			params["environment"] = envInt
		}
	}

	if creator := ctx.Query("creator"); creator != "" {
		creatorInt, err := strconv.Atoi(creator)
		if err == nil {
			params["creator_id"] = creatorInt
		}
	}

	if search := ctx.Query("search"); search != "" {
		params["search"] = search
	}

	// 解析分页参数
	if page := ctx.Query("page"); page != "" {
		pageInt, err := strconv.Atoi(page)
		if err == nil && pageInt > 0 {
			params["page"] = pageInt
		}
	} else {
		params["page"] = 1
	}

	if pageSize := ctx.Query("page_size"); pageSize != "" {
		pageSizeInt, err := strconv.Atoi(pageSize)
		if err == nil && pageSizeInt > 0 {
			params["page_size"] = pageSizeInt
		}
	} else {
		params["page_size"] = 10
	}

	// 设置排序
	params["order_by"] = "created_at DESC"

	orders, total, err := c.publishService.ListPublishOrders(ctx.Request.Context(), params)
	if err != nil {
		c.logger.Errorf("列出工单失败: %v", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "列出工单失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"orders": orders,
		"total":  total,
		"page":   params["page"],
		"size":   params["page_size"],
	})
}

// GetOrder 获取工单详情
func (c *APIController) GetOrder(ctx *gin.Context) {
	orderID := ctx.Param("id")
	if orderID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "工单ID不能为空",
		})
		return
	}

	order, err := c.publishService.GetPublishOrder(ctx.Request.Context(), orderID)
	if err != nil {
		c.logger.Errorf("获取工单失败: %v", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取工单失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, order)
}

// GetOrderApps 获取工单应用
func (c *APIController) GetOrderApps(ctx *gin.Context) {
	orderID := ctx.Param("id")
	if orderID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "工单ID不能为空",
		})
		return
	}

	apps, err := c.publishService.GetPublishApps(ctx.Request.Context(), orderID)
	if err != nil {
		c.logger.Errorf("获取工单应用失败: %v", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取工单应用失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"apps": apps,
	})
}

// CancelOrder 取消工单
func (c *APIController) CancelOrder(ctx *gin.Context) {
	orderID := ctx.Param("id")
	if orderID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "工单ID不能为空",
		})
		return
	}

	err := c.publishService.CancelPublishOrder(ctx.Request.Context(), orderID)
	if err != nil {
		c.logger.Errorf("取消工单失败: %v", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "取消工单失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"message": "工单已取消",
	})
}

// DeployApp 部署应用
func (c *APIController) DeployApp(ctx *gin.Context) {
	orderID := ctx.Param("id")
	appIDStr := ctx.Param("app_id")
	if orderID == "" || appIDStr == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "工单ID和应用ID不能为空",
		})
		return
	}

	appID, err := strconv.ParseUint(appIDStr, 10, 64)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的应用ID",
		})
		return
	}

	err = c.publishService.DeployPublishApp(ctx.Request.Context(), uint(appID))
	if err != nil {
		c.logger.Errorf("部署应用失败: %v", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "部署应用失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"message": "应用部署已启动",
	})
}

// CancelAppDeploy 取消应用部署
func (c *APIController) CancelAppDeploy(ctx *gin.Context) {
	orderID := ctx.Param("id")
	appIDStr := ctx.Param("app_id")
	if orderID == "" || appIDStr == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "工单ID和应用ID不能为空",
		})
		return
	}

	appID, err := strconv.ParseUint(appIDStr, 10, 64)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的应用ID",
		})
		return
	}

	err = c.publishService.CancelPublishApp(ctx.Request.Context(), uint(appID))
	if err != nil {
		c.logger.Errorf("取消应用部署失败: %v", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "取消应用部署失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"message": "应用部署已取消",
	})
}

// ListApps 列出应用
func (c *APIController) ListApps(ctx *gin.Context) {
	// 此API需要从数据库查询应用记录
	// 示例实现，实际应从数据库查询
	ctx.JSON(http.StatusOK, gin.H{
		"apps":    []gin.H{},
		"message": "功能待实现",
	})
}

// GetApp 获取应用详情
func (c *APIController) GetApp(ctx *gin.Context) {
	appIDStr := ctx.Param("id")
	if appIDStr == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "应用ID不能为空",
		})
		return
	}

	appID, err := strconv.Atoi(appIDStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的应用ID",
		})
		return
	}

	app, err := c.cicdService.GetApp(ctx.Request.Context(), appID)
	if err != nil {
		c.logger.Errorf("获取应用失败: %v", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取应用失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, app)
}

// GetAppVersions 获取应用版本
func (c *APIController) GetAppVersions(ctx *gin.Context) {
	// 此API需要从数据库查询应用版本记录
	// 示例实现，实际应从数据库查询
	ctx.JSON(http.StatusOK, gin.H{
		"versions": []gin.H{},
		"message":  "功能待实现",
	})
}

// 生成订单ID的辅助函数
func generateOrderID() string {
	// 生成一个基于时间的唯一ID
	now := time.Now()
	return fmt.Sprintf("PUB%s%03d", now.Format("20060102150405"), now.Nanosecond()/1000000)
}
