package controllers

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/devops-microservices/cicd-service/config"
	"github.com/devops-microservices/cicd-service/models"
	"github.com/devops-microservices/cicd-service/services"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// DeployController 部署控制器
type DeployController struct {
	db          *gorm.DB
	logger      logrus.FieldLogger
	cicdService *services.CICDService
	config      *config.Config
}

// NewDeployController 创建新的部署控制器
func NewDeployController(db *gorm.DB, logger logrus.FieldLogger, cicdService *services.CICDService, config *config.Config) *DeployController {
	return &DeployController{
		db:          db,
		logger:      logger,
		cicdService: cicdService,
		config:      config,
	}
}

// CommitTag 提交标签结构体
type CommitTag struct {
	Label string `json:"label"`
	Name  string `json:"name"`
}

// DeployRequest 部署请求结构体
type DeployRequest struct {
	AppInfoID  uint      `json:"app_info_id" binding:"required"`
	Image      string    `json:"image" binding:"required"`
	CommitTag  CommitTag `json:"commit_tag"`
	Kubernetes []uint    `json:"kubernetes"`
	Hosts      []uint    `json:"hosts"`
	BatchUUID  string    `json:"batch_uuid" binding:"required"`
	Force      bool      `json:"force"`
}

// GetDeployLogs 获取部署日志
func (c *DeployController) GetDeployLogs(ctx *gin.Context) {
	buildIDStr := ctx.Param("build_id")
	buildID, err := strconv.ParseUint(buildIDStr, 10, 32)
	if err != nil {
		c.logger.Errorf("无效的部署ID: %s", buildIDStr)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    40000,
			"message": "无效的部署ID",
			"data":    nil,
		})
		return
	}

	// 查询部署任务
	var deployJob models.DeployJob
	if err := c.db.First(&deployJob, uint(buildID)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.logger.Warnf("部署任务未找到: ID=%d", buildID)
			ctx.JSON(http.StatusNotFound, gin.H{
				"code":    40400,
				"message": "部署任务未找到",
				"data":    nil,
			})
			return
		}
		c.logger.Errorf("查询部署任务失败: %v", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "查询部署任务失败",
			"data":    nil,
		})
		return
	}

	// 解析Kubernetes集群信息
	var kubernetesConfig []map[string]interface{}
	var clusterNames []string

	if deployJob.Kubernetes != "" {
		json.Unmarshal([]byte(deployJob.Kubernetes), &kubernetesConfig)
		for _, config := range kubernetesConfig {
			clusterNames = append(clusterNames, config["name"].(string))
		}
	}

	// 查询部署结果
	var deployResult models.DeployJobResult
	if err := c.db.Where("job_id = ?", buildID).First(&deployResult).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.logger.Infof("部署结果未找到，可能部署仍在进行中: JobID=%d", buildID)

			// 如果部署仍在进行中，返回状态为3（部署中）的结果
			namespace := ""
			if len(kubernetesConfig) > 0 {
				if ns, exists := kubernetesConfig[0]["namespace"]; exists {
					namespace = ns.(string)
				}
			}

			// 构建进行中的部署结果
			resultData := map[string]interface{}{
				"status":     3, // 部署中
				"namespace":  namespace,
				"job_obj.id": deployJob.ID,
				"clusters":   clusterNames,
			}

			ctx.JSON(http.StatusOK, gin.H{
				"code":    20000,
				"message": "部署仍在进行中，返回当前进度",
				"data":    resultData,
			})
			return
		}
		c.logger.Errorf("查询部署结果失败: %v", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "查询部署结果失败",
			"data":    nil,
		})
		return
	}

	// 解析部署结果JSON
	var fullResultData map[string]interface{}
	if err := json.Unmarshal([]byte(deployResult.Result), &fullResultData); err != nil {
		c.logger.Errorf("解析部署结果JSON失败: %v", err)
		fullResultData = map[string]interface{}{}
	}

	// 确定整体部署状态
	overallStatus := deployJob.Status

	// 如果有原始数据，遍历每个集群的结果判断整体状态
	if len(fullResultData) > 0 {
		// 排除特殊键
		specialKeys := map[string]bool{
			"col_active": true,
			"worker":     true,
		}

		hasFailure := false
		allSuccess := true

		for k, v := range fullResultData {
			if specialKeys[k] {
				continue
			}

			// 检查每个集群的状态
			if clusterResult, ok := v.(map[string]interface{}); ok {
				if status, exists := clusterResult["status"].(float64); exists {
					if int(status) == 2 { // 失败状态
						hasFailure = true
					} else if int(status) != 1 { // 非成功状态
						allSuccess = false
					}
				}
			}
		}

		if hasFailure {
			overallStatus = 2 // 失败
		} else if !allSuccess {
			overallStatus = 3 // 部署中
		} else {
			overallStatus = 1 // 成功
		}
	}

	// 准备返回数据格式
	namespace := ""
	if len(kubernetesConfig) > 0 {
		if ns, exists := kubernetesConfig[0]["namespace"]; exists {
			namespace = ns.(string)
		}
	}

	// 构建所需的结果格式
	// resultData := map[string]interface{}{
	// 	"status":      overallStatus,
	// 	"namespace":   namespace,
	// 	"job_obj.id":  deployJob.ID,
	// 	"clusters":    clusterIds,
	// 	"full_result": fullResultData, // 保留原始完整结果以便前端可以查看详情
	// }
	fullResultData["clusters"] = clusterNames
	fullResultData["namespace"] = namespace
	fullResultData["job_obj.id"] = deployJob.ID
	fullResultData["status"] = overallStatus

	// 准备返回数据
	response := gin.H{
		"code":    20000,
		"message": "成功获取部署日志",
		"data":    fullResultData,
	}

	ctx.JSON(http.StatusOK, response)
}

// CreateDeploy 创建部署任务
// @Summary 创建部署任务
// @Description 创建新的部署任务，支持多Kubernetes环境部署
// @Tags 部署管理
// @Accept json
// @Produce json
// @Param deploy_request body DeployRequest true "部署请求"
// @Success 200 {object} map[string]interface{} "成功创建部署任务"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 404 {object} map[string]interface{} "应用信息未找到"
// @Failure 500 {object} map[string]interface{} "服务器内部错误"
// @Router /api/cicd/deploy [post]
func (c *DeployController) CreateDeploy(ctx *gin.Context) {
	var req DeployRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		c.logger.Errorf("参数绑定失败: %v", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    40000,
			"message": "参数错误: " + err.Error(),
			"data":    nil,
		})
		return
	}

	c.logger.Infof("收到部署请求: AppInfoID=%d, Image=%s, BatchUUID=%s",
		req.AppInfoID, req.Image, req.BatchUUID)

	// 获取应用信息
	appInfo, err := c.cicdService.GetAppInfo(ctx, strconv.Itoa(int(req.AppInfoID)))
	if err != nil {
		c.logger.Errorf("获取应用信息失败: %v", err)
		ctx.JSON(http.StatusNotFound, gin.H{
			"code":    40400,
			"message": "应用信息未找到",
			"data":    nil,
		})
		return
	}

	// 获取应用详情
	app, err := c.cicdService.GetApp(ctx, int(*appInfo.AppID))
	if err != nil {
		c.logger.Errorf("获取应用详情失败: %v", err)
		ctx.JSON(http.StatusNotFound, gin.H{
			"code":    40400,
			"message": "应用详情未找到",
			"data":    nil,
		})
		return
	}

	// 获取环境信息
	var environment models.Environment
	if err := c.db.First(&environment, appInfo.EnvironmentID).Error; err != nil {
		c.logger.Errorf("获取环境信息失败: %v", err)
		ctx.JSON(http.StatusNotFound, gin.H{
			"code":    40400,
			"message": "环境信息未找到",
			"data":    nil,
		})
		return
	}

	// 创建单条部署任务记录
	deployerID := uint(1) // TODO: 从认证信息获取部署者ID

	// 创建包含所有Kubernetes ID的配置
	kubernetesConfigList := make([]map[string]interface{}, 0, len(req.Kubernetes))

	// 验证每个Kubernetes集群是否存在
	var k8sClusters []models.KubernetesCluster
	for _, kubernetesID := range req.Kubernetes {
		var k8sCluster models.KubernetesCluster
		if err := c.db.First(&k8sCluster, kubernetesID).Error; err != nil {
			c.logger.Errorf("获取Kubernetes集群失败: ID=%d, Error=%v", kubernetesID, err)
			ctx.JSON(http.StatusNotFound, gin.H{
				"code":    40400,
				"message": fmt.Sprintf("Kubernetes集群[ID:%d]未找到", kubernetesID),
				"data":    nil,
			})
			return
		}

		k8sClusters = append(k8sClusters, k8sCluster)

		kubernetesConfigList = append(kubernetesConfigList, map[string]interface{}{
			"kubernetes_id": kubernetesID,
			"namespace":     environment.Name + "-" + app.Project.Product.Name,
			"force":         req.Force,
			"name":          k8sCluster.Name, // 添加集群名称，用于结果显示
		})
	}

	// 创建唯一ID，不再包含具体的kubernetes_id
	uniqID := fmt.Sprintf("%s-%d", req.BatchUUID, req.AppInfoID)

	// 将所有Kubernetes ID保存到一个记录中
	kubernetesJSON, _ := json.Marshal(kubernetesConfigList)

	deployJob := models.DeployJob{
		UniqID:     uniqID,
		AppID:      fmt.Sprintf("%d", app.ID),
		AppInfoID:  req.AppInfoID,
		Status:     3, // 部署中
		Image:      req.Image,
		DeployType: 0, // 常规部署
		BatchUUID:  req.BatchUUID,
		DeployerID: deployerID,
		Kubernetes: string(kubernetesJSON),
	}

	// 保存到数据库
	if err := c.db.Create(&deployJob).Error; err != nil {
		c.logger.Errorf("创建部署任务失败: %v", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "创建部署任务失败",
			"data":    nil,
		})
		return
	}

	// 创建用于存储所有结果的结构
	deployResults := make(map[string]interface{})
	deployResults["col_active"] = "" // 初始化活跃集群字段

	// 创建用于协调所有协程的等待组和结果通道
	var wg sync.WaitGroup
	resultChan := make(chan map[string]interface{}, len(req.Kubernetes))

	// 启动多个协程执行部署任务
	for i, k8sCluster := range k8sClusters {
		wg.Add(1)

		go func(cluster models.KubernetesCluster, index int) {
			defer wg.Done()

			deployService := services.NewKubernetesDeployService(c.db, c.logger, c.config)
			result := map[string]interface{}{
				"clusterName": cluster.Name,
				"status": map[string]interface{}{
					"status": 2, // 默认为失败状态
					"stages": map[string]interface{}{},
				},
			}

			// 执行部署
			err := deployService.ExecuteDeployment(deployJob, cluster, appInfo, app, environment)
			if err != nil {
				c.logger.Errorf("执行部署失败: JobID=%d, ClusterName=%s, Error=%v",
					deployJob.ID, cluster.Name, err)

				// 失败状态的结果
				result["status"] = map[string]interface{}{
					"status": 2, // 失败
					"stages": map[string]interface{}{
						"error": map[string]interface{}{
							"name":   "Error",
							"status": 2,
							"logs":   fmt.Sprintf("{\"error\": \"%s\"}", err.Error()),
							"msg":    fmt.Sprintf("部署到%s失败: %s", cluster.Name, err.Error()),
						},
					},
				}
			} else {
				// 成功状态的结果
				result["status"] = map[string]interface{}{
					"status": 1, // 成功
					"stages": []map[string]interface{}{
						{
							"name":   "Connect Kubernetes",
							"status": 1,
							"logs":   fmt.Sprintf("{\"apiVersion\": \"apps/v1\", \"kubeconfig\": \"%s\"}", k8sCluster.Name),
							"msg":    fmt.Sprintf("连接到Kubernetes集群 %s 成功", k8sCluster.Name),
						},
						{
							"name":   "Image Sync",
							"status": 1,
							"logs":   fmt.Sprintf("{\"image\": \"%s\", \"registry\": \"%s\"}", deployJob.Image, getImageRegistry(deployJob.Image)),
							"msg":    fmt.Sprintf("镜像 %s 同步成功", deployJob.Image),
						},
						{
							"name":   "Deployment",
							"status": 1,
							"logs":   fmt.Sprintf("{\"deployment\": \"%s\"}", app.Name),
							"msg":    fmt.Sprintf("准备更新Deployment[%s-%s]部署", app.Name, environment.Name),
						},
						{
							"name":   "Status Check",
							"status": 1,
							"logs":   fmt.Sprintf("{\"conditions\": [{\"lastTransitionTime\": \"%s\", \"status\": \"True\", \"type\": \"Available\"}]}", time.Now().Format(time.RFC3339)),
							"msg":    fmt.Sprintf("%s-%s-%s 部署成功", app.Name, environment.Name, getImageTag(deployJob.Image)),
						},
					},
				}
			}

			resultChan <- result
		}(k8sCluster, i)
	}

	// 等待所有部署任务完成
	wg.Wait()
	close(resultChan)

	// 收集所有结果
	allSuccess := true
	resultCount := len(req.Kubernetes)
	for i := 0; i < resultCount; i++ {
		result, ok := <-resultChan
		if !ok {
			break
		}

		clusterName := result["clusterName"].(string)
		status := result["status"].(map[string]interface{})

		// 保存每个集群的结果
		deployResults[clusterName] = status

		// 检查部署状态，如果有一个失败则整体状态为失败
		if status["status"].(int) != 1 {
			allSuccess = false
		} else if deployResults["col_active"] == "" {
			// 如果是第一个成功的集群，设置为活跃集群
			deployResults["col_active"] = clusterName
		}
	}

	// 更新整体部署状态
	finalStatus := models.StatusSuccess
	if !allSuccess {
		finalStatus = models.StatusFailed
	}

	c.db.Model(&deployJob).Updates(map[string]interface{}{
		"status": finalStatus,
	})

	// 创建单条部署结果记录
	deployResultJSON, _ := json.Marshal(deployResults)
	deployJobResult := models.DeployJobResult{
		JobID:  deployJob.ID,
		Result: string(deployResultJSON),
	}

	if err := c.db.Create(&deployJobResult).Error; err != nil {
		c.logger.Errorf("保存部署结果失败: %v", err)
	}

	c.logger.Infof("成功创建部署任务: ID=%d, 目标集群数=%d", deployJob.ID, len(req.Kubernetes))
	ctx.JSON(http.StatusOK, gin.H{
		"code":    20000,
		"message": "成功创建部署任务",
		"data": gin.H{
			"deploy_job": deployJob,
			"batch_uuid": req.BatchUUID,
			"image":      req.Image,
		},
	})
}

// getImageTag 从镜像字符串中提取标签部分
func getImageTag(image string) string {
	parts := strings.Split(image, ":")
	if len(parts) > 1 {
		return parts[len(parts)-1]
	}
	return "latest"
}

// getImageRegistry 从镜像字符串中提取镜像仓库部分
func getImageRegistry(image string) string {
	parts := strings.Split(image, "/")
	if len(parts) > 1 {
		return parts[0]
	}
	return ""
}

// RollbackRequest 回滚请求结构体（本地定义，避免循环导入）
type RollbackRequest struct {
	AppInfoID       uint   `json:"app_info_id" binding:"required"`   // 应用信息ID
	KubernetesID    uint   `json:"kubernetes_id" binding:"required"` // Kubernetes集群ID
	ToRevision      int64  `json:"to_revision,omitempty"`            // 回滚到的版本，为空时回滚到上一版本
	RollbackReason  int    `json:"rollback_reason"`                  // 回滚原因码
	RollbackComment string `json:"rollback_comment"`                 // 回滚备注
	BatchUUID       string `json:"batch_uuid" binding:"required"`    // 批次UUID
}

// RollbackToImageRequest 回滚到指定镜像请求结构体
type RollbackToImageRequest struct {
	AppInfoID       uint   `json:"app_info_id" binding:"required"` // 应用信息ID
	Kubernetes      []uint `json:"kubernetes"`                     // Kubernetes集群ID列表
	Image           string `json:"image" binding:"required"`       // 目标镜像
	Hosts           []uint `json:"hosts"`                          // 主机列表
	BatchUUID       string `json:"batch_uuid" binding:"required"`  // 批次UUID
	Force           bool   `json:"force"`                          // 是否强制
	Rollback        bool   `json:"rollback"`                       // 是否回滚标识
	RollbackReason  string `json:"rollback_reason"`                // 回滚原因
	RollbackComment string `json:"rollback_comment"`               // 回滚备注
}

// CreateRollback 创建回滚任务
// @Summary 创建回滚任务
// @Description 执行应用版本回滚，可指定版本或回滚到上一版本
// @Tags 部署管理
// @Accept json
// @Produce json
// @Param rollback_request body RollbackRequest true "回滚请求"
// @Success 200 {object} map[string]interface{} "成功创建回滚任务"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 404 {object} map[string]interface{} "应用信息未找到"
// @Failure 500 {object} map[string]interface{} "服务器内部错误"
// @Router /api/cicd/rollback [post]
func (c *DeployController) CreateRollback(ctx *gin.Context) {
	var req RollbackRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		c.logger.Errorf("回滚参数绑定失败: %v", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    40000,
			"message": "参数错误: " + err.Error(),
			"data":    nil,
		})
		return
	}

	c.logger.Infof("收到回滚请求: AppInfoID=%d, KubernetesID=%d, ToRevision=%d, BatchUUID=%s",
		req.AppInfoID, req.KubernetesID, req.ToRevision, req.BatchUUID)

	// 获取应用信息
	appInfo, err := c.cicdService.GetAppInfo(ctx, strconv.Itoa(int(req.AppInfoID)))
	if err != nil {
		c.logger.Errorf("获取应用信息失败: %v", err)
		ctx.JSON(http.StatusNotFound, gin.H{
			"code":    40400,
			"message": "应用信息未找到",
			"data":    nil,
		})
		return
	}

	// 获取应用详情
	app, err := c.cicdService.GetApp(ctx, int(*appInfo.AppID))
	if err != nil {
		c.logger.Errorf("获取应用详情失败: %v", err)
		ctx.JSON(http.StatusNotFound, gin.H{
			"code":    40400,
			"message": "应用详情未找到",
			"data":    nil,
		})
		return
	}

	// 获取环境信息
	var environment models.Environment
	if err := c.db.First(&environment, appInfo.EnvironmentID).Error; err != nil {
		c.logger.Errorf("获取环境信息失败: %v", err)
		ctx.JSON(http.StatusNotFound, gin.H{
			"code":    40400,
			"message": "环境信息未找到",
			"data":    nil,
		})
		return
	}

	// 获取Kubernetes集群信息
	var k8sCluster models.KubernetesCluster
	if err := c.db.First(&k8sCluster, req.KubernetesID).Error; err != nil {
		c.logger.Errorf("获取Kubernetes集群失败: ID=%d, Error=%v", req.KubernetesID, err)
		ctx.JSON(http.StatusNotFound, gin.H{
			"code":    40400,
			"message": fmt.Sprintf("Kubernetes集群[ID:%d]未找到", req.KubernetesID),
			"data":    nil,
		})
		return
	}

	deployerID := uint(1) // TODO: 从认证信息获取部署者ID

	// 转换为services包的RollbackRequest
	rollbackReq := services.RollbackRequest{
		AppInfoID:       req.AppInfoID,
		KubernetesID:    req.KubernetesID,
		ToRevision:      req.ToRevision,
		RollbackReason:  req.RollbackReason,
		RollbackComment: req.RollbackComment,
		BatchUUID:       req.BatchUUID,
	}

	// 异步执行回滚
	go func() {
		deployService := services.NewKubernetesDeployService(c.db, c.logger, c.config)
		rollbackResp, err := deployService.ExecuteRollback(rollbackReq, k8sCluster, appInfo, app, environment, deployerID)
		if err != nil {
			c.logger.Errorf("执行回滚失败: %v", err)
		} else {
			c.logger.Infof("回滚执行成功: JobID=%d, %s回滚到版本%d",
				rollbackResp.JobID, rollbackResp.AppName, rollbackResp.ToRevision)
		}
	}()

	c.logger.Infof("成功启动回滚任务")
	ctx.JSON(http.StatusOK, gin.H{
		"code":    20000,
		"message": "成功创建回滚任务",
		"data": gin.H{
			"app_name":        app.Name,
			"environment":     environment.Name,
			"cluster":         k8sCluster.Name,
			"batch_uuid":      req.BatchUUID,
			"to_revision":     req.ToRevision,
			"rollback_reason": req.RollbackReason,
		},
	})
}

// GetRollbackHistory 获取应用回滚历史
// @Summary 获取应用回滚历史
// @Description 获取指定应用在特定Kubernetes集群中的回滚历史版本
// @Tags 部署管理
// @Accept json
// @Produce json
// @Param app_info_id query uint true "应用信息ID"
// @Param kubernetes_id query uint true "Kubernetes集群ID"
// @Success 200 {object} map[string]interface{} "获取历史成功"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 404 {object} map[string]interface{} "应用信息未找到"
// @Failure 500 {object} map[string]interface{} "服务器内部错误"
// @Router /api/cicd/rollback/history [get]
func (c *DeployController) GetRollbackHistory(ctx *gin.Context) {
	appInfoIDStr := ctx.Query("app_info_id")
	kubernetesIDStr := ctx.Query("kubernetes_id")

	if appInfoIDStr == "" || kubernetesIDStr == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    40000,
			"message": "app_info_id和kubernetes_id参数不能为空",
			"data":    nil,
		})
		return
	}

	appInfoID, err := strconv.ParseUint(appInfoIDStr, 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    40000,
			"message": "无效的app_info_id参数",
			"data":    nil,
		})
		return
	}

	kubernetesID, err := strconv.ParseUint(kubernetesIDStr, 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    40000,
			"message": "无效的kubernetes_id参数",
			"data":    nil,
		})
		return
	}

	c.logger.Infof("获取回滚历史: AppInfoID=%d, KubernetesID=%d", appInfoID, kubernetesID)

	// 获取应用信息
	appInfo, err := c.cicdService.GetAppInfo(ctx, fmt.Sprintf("%d", appInfoID))
	if err != nil {
		c.logger.Errorf("获取应用信息失败: %v", err)
		ctx.JSON(http.StatusNotFound, gin.H{
			"code":    40400,
			"message": "应用信息未找到",
			"data":    nil,
		})
		return
	}

	// 获取应用详情
	app, err := c.cicdService.GetApp(ctx, int(*appInfo.AppID))
	if err != nil {
		c.logger.Errorf("获取应用详情失败: %v", err)
		ctx.JSON(http.StatusNotFound, gin.H{
			"code":    40400,
			"message": "应用详情未找到",
			"data":    nil,
		})
		return
	}

	// 获取环境信息
	var environment models.Environment
	if err := c.db.First(&environment, appInfo.EnvironmentID).Error; err != nil {
		c.logger.Errorf("获取环境信息失败: %v", err)
		ctx.JSON(http.StatusNotFound, gin.H{
			"code":    40400,
			"message": "环境信息未找到",
			"data":    nil,
		})
		return
	}

	// 获取Kubernetes集群信息
	var k8sCluster models.KubernetesCluster
	if err := c.db.First(&k8sCluster, uint(kubernetesID)).Error; err != nil {
		c.logger.Errorf("获取Kubernetes集群失败: ID=%d, Error=%v", kubernetesID, err)
		ctx.JSON(http.StatusNotFound, gin.H{
			"code":    40400,
			"message": fmt.Sprintf("Kubernetes集群[ID:%d]未找到", kubernetesID),
			"data":    nil,
		})
		return
	}

	// 获取回滚历史
	deployService := services.NewKubernetesDeployService(c.db, c.logger, c.config)
	history, err := deployService.GetRollbackHistory(k8sCluster, appInfo, app, environment)
	if err != nil {
		c.logger.Errorf("获取回滚历史失败: %v", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "获取回滚历史失败: " + err.Error(),
			"data":    nil,
		})
		return
	}

	c.logger.Infof("成功获取%d个历史版本", len(history))
	ctx.JSON(http.StatusOK, gin.H{
		"code":    20000,
		"message": "获取回滚历史成功",
		"data": gin.H{
			"app_name":    app.Name,
			"environment": environment.Name,
			"cluster":     k8sCluster.Name,
			"history":     history,
		},
	})
}

// CreateRollbackToImage 创建回滚到指定镜像任务
// @Summary 创建回滚到指定镜像任务
// @Description 回滚到指定的镜像版本，类似部署流程但标记为回滚操作
// @Tags 部署管理
// @Accept json
// @Produce json
// @Param rollback_request body RollbackToImageRequest true "回滚到指定镜像请求"
// @Success 200 {object} map[string]interface{} "成功创建回滚任务"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 404 {object} map[string]interface{} "应用信息未找到"
// @Failure 500 {object} map[string]interface{} "服务器内部错误"
// @Router /api/cicd/rollback/to-image [post]
func (c *DeployController) CreateRollbackToImage(ctx *gin.Context) {
	var req RollbackToImageRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		c.logger.Errorf("回滚到指定镜像参数绑定失败: %v", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    40000,
			"message": "参数错误: " + err.Error(),
			"data":    nil,
		})
		return
	}

	c.logger.Infof("收到回滚到指定镜像请求: AppInfoID=%d, Image=%s, Kubernetes=%v, BatchUUID=%s",
		req.AppInfoID, req.Image, req.Kubernetes, req.BatchUUID)

	// 获取应用信息
	appInfo, err := c.cicdService.GetAppInfo(ctx, strconv.Itoa(int(req.AppInfoID)))
	if err != nil {
		c.logger.Errorf("获取应用信息失败: %v", err)
		ctx.JSON(http.StatusNotFound, gin.H{
			"code":    40400,
			"message": "应用信息未找到",
			"data":    nil,
		})
		return
	}

	// 获取应用详情
	app, err := c.cicdService.GetApp(ctx, int(*appInfo.AppID))
	if err != nil {
		c.logger.Errorf("获取应用详情失败: %v", err)
		ctx.JSON(http.StatusNotFound, gin.H{
			"code":    40400,
			"message": "应用详情未找到",
			"data":    nil,
		})
		return
	}

	// 获取环境信息
	var environment models.Environment
	if err := c.db.First(&environment, appInfo.EnvironmentID).Error; err != nil {
		c.logger.Errorf("获取环境信息失败: %v", err)
		ctx.JSON(http.StatusNotFound, gin.H{
			"code":    40400,
			"message": "环境信息未找到",
			"data":    nil,
		})
		return
	}

	// 创建回滚任务记录
	var deployJobs []models.DeployJob
	deployerID := uint(1) // TODO: 从认证信息获取部署者ID

	// 解析回滚原因为整数
	rollbackReasonInt := 0
	if req.RollbackReason != "" {
		if reasonInt, err := strconv.Atoi(req.RollbackReason); err == nil {
			rollbackReasonInt = reasonInt
		}
	}

	// 为每个Kubernetes环境创建回滚任务
	for _, kubernetesID := range req.Kubernetes {
		// 获取Kubernetes集群信息
		var k8sCluster models.KubernetesCluster
		if err := c.db.First(&k8sCluster, kubernetesID).Error; err != nil {
			c.logger.Errorf("获取Kubernetes集群失败: ID=%d, Error=%v", kubernetesID, err)
			ctx.JSON(http.StatusNotFound, gin.H{
				"code":    40400,
				"message": fmt.Sprintf("Kubernetes集群[ID:%d]未找到", kubernetesID),
				"data":    nil,
			})
			return
		}

		deployJob := models.DeployJob{
			UniqID:          fmt.Sprintf("%s-%d-%d-rollback-image", req.BatchUUID, req.AppInfoID, kubernetesID),
			AppID:           fmt.Sprintf("%d", app.ID),
			AppInfoID:       req.AppInfoID,
			Status:          3, // 部署中
			Image:           req.Image,
			DeployType:      models.DeployTypeRollback, // 版本回退
			RollbackReason:  rollbackReasonInt,
			RollbackComment: req.RollbackComment,
			BatchUUID:       req.BatchUUID,
			DeployerID:      deployerID,
		}

		// 设置Kubernetes配置
		kubernetesConfig := map[string]interface{}{
			"kubernetes_id":    kubernetesID,
			"namespace":        fmt.Sprintf("%s-%s", strings.ToLower(environment.Name), strings.ToLower(app.Project.Product.Name)),
			"force":            req.Force,
			"rollback":         req.Rollback,
			"rollback_reason":  req.RollbackReason,
			"rollback_comment": req.RollbackComment,
			"image":            req.Image,
		}

		kubernetesJSON, _ := json.Marshal(kubernetesConfig)
		deployJob.Kubernetes = string(kubernetesJSON)

		// 保存到数据库
		if err := c.db.Create(&deployJob).Error; err != nil {
			c.logger.Errorf("创建回滚任务失败: %v", err)
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"code":    50000,
				"message": "创建回滚任务失败",
				"data":    nil,
			})
			return
		}

		deployJobs = append(deployJobs, deployJob)

		// 异步执行回滚部署
		go func(job models.DeployJob, cluster models.KubernetesCluster) {
			deployService := services.NewKubernetesDeployService(c.db, c.logger, c.config)
			if err := deployService.ExecuteDeployment(job, cluster, appInfo, app, environment); err != nil {
				c.logger.Errorf("执行回滚部署失败: JobID=%d, Error=%v", job.ID, err)
				// 更新部署状态为失败
				c.db.Model(&job).Updates(map[string]interface{}{
					"status": models.StatusFailed, // 部署失败
				})
			}
		}(deployJob, k8sCluster)
	}

	// TODO: 处理Hosts回滚部署（如果需要）
	for _, hostID := range req.Hosts {
		c.logger.Infof("TODO: 处理主机回滚部署 HostID=%d", hostID)
	}

	c.logger.Infof("成功创建%d个回滚到指定镜像任务", len(deployJobs))
	ctx.JSON(http.StatusOK, gin.H{
		"code":    20000,
		"message": "成功创建回滚到指定镜像任务",
		"data": gin.H{
			"deploy_jobs":      deployJobs,
			"batch_uuid":       req.BatchUUID,
			"target_image":     req.Image,
			"rollback_reason":  req.RollbackReason,
			"rollback_comment": req.RollbackComment,
			"app_name":         app.Name,
			"environment":      environment.Name,
		},
	})
}
