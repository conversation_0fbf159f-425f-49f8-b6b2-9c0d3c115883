package controllers

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/devops-microservices/cicd-service/models"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// BuildController 构建控制器
type BuildController struct {
	db     *gorm.DB
	logger logrus.FieldLogger
}

// NewBuildController 创建新的构建控制器
func NewBuildController(db *gorm.DB, logger logrus.FieldLogger) *BuildController {
	return &BuildController{
		db:     db,
		logger: logger,
	}
}

// GetBuildLogs 获取构建日志
// @Summary 获取构建日志
// @Description 根据构建ID获取构建的详细日志
// @Tags 构建管理
// @Accept json
// @Produce json
// @Param build_id path int true "构建ID"
// @Success 200 {object} map[string]interface{} "成功返回构建日志"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 404 {object} map[string]interface{} "构建任务未找到"
// @Failure 500 {object} map[string]interface{} "服务器内部错误"
// @Router /api/cicd/builds/{build_id}/logs [get]
func (c *BuildController) GetBuildLogs(ctx *gin.Context) {
	buildIDStr := ctx.Param("build_id")
	buildID, err := strconv.ParseUint(buildIDStr, 10, 32)
	if err != nil {
		c.logger.Errorf("无效的构建ID: %s", buildIDStr)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    40000,
			"message": "无效的构建ID",
			"data":    nil,
		})
		return
	}

	// 查询构建任务
	var buildJob models.BuildJob
	if err := c.db.First(&buildJob, uint(buildID)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.logger.Warnf("构建任务未找到: ID=%d", buildID)
			ctx.JSON(http.StatusNotFound, gin.H{
				"code":    40400,
				"message": "构建任务未找到",
				"data":    nil,
			})
			return
		}
		c.logger.Errorf("查询构建任务失败: %v", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "查询构建任务失败",
			"data":    nil,
		})
		return
	}

	// 查询构建结果
	var buildResult models.BuildJobResult
	if err := c.db.Where("job_id = ?", buildID).First(&buildResult).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.logger.Infof("构建结果未找到，可能构建仍在进行中: JobID=%d", buildID)
			ctx.JSON(http.StatusOK, gin.H{
				"code":    20000,
				"message": "构建仍在进行中，暂无日志",
				"data": gin.H{
					"build_job":      buildJob,
					"console_output": "构建仍在进行中，请稍后再试...",
					"result":         nil,
					"status":         buildJob.Status,
				},
			})
			return
		}
		c.logger.Errorf("查询构建结果失败: %v", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "查询构建结果失败",
			"data":    nil,
		})
		return
	}

	// 返回构建日志
	c.logger.Infof("成功获取构建日志: JobID=%d", buildID)
	ctx.JSON(http.StatusOK, gin.H{
		"code":    20000,
		"message": "成功获取构建日志",
		"data": gin.H{
			"build_job":      buildJob,
			"console_output": buildResult.ConsoleOutput,
			"result":         buildResult.Result,
			"status":         buildJob.Status,
		},
	})
}

// GetAppInfo 获取应用CI信息
// @Summary 获取应用CI信息
// @Description 根据环境和其他条件获取应用CI信息列表
// @Tags 构建管理
// @Accept json
// @Produce json
// @Param environment query int false "环境ID"
// @Param search query string false "搜索关键词"
// @Param page_size query int false "每页数量" default(20)
// @Param page query int false "页码" default(1)
// @Param ordering query string false "排序字段"
// @Success 200 {object} map[string]interface{} "成功返回应用CI信息列表"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 500 {object} map[string]interface{} "服务器内部错误"
// @Router /api/cicd/ci [get]
func (c *BuildController) GetAppInfo(ctx *gin.Context) {
	// 获取查询参数
	environmentIDStr := ctx.Query("environment")
	search := ctx.Query("search")
	pageStr := ctx.DefaultQuery("page", "1")
	pageSizeStr := ctx.DefaultQuery("page_size", "20")
	ordering := ctx.Query("ordering")
	appCategory := ctx.Query("app__category")

	// 转换参数类型
	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	pageSize, err := strconv.Atoi(pageSizeStr)
	if err != nil || pageSize < 1 {
		pageSize = 20
	}

	// 构建基础SQL查询
	sql := `
		SELECT 
			ai.id, ai.uniq_tag, ai.app_id, ai.environment_id, ai.branch, 
			COALESCE(ai.version, '') as version_str, 
			COALESCE(TO_CHAR(ai.created_at, 'YYYY-MM-DD"T"HH24:MI:SS'), '') as created_at_str, 
			ai.is_enable,
			ma.id as app_id, ma.name as app_name, ma.alias as app_alias, ma.appid, ma.category, ma.language, ma.repo, ma."desc" as app_desc, ma.is_k8s, ma.modules,
			p.id as project_id, p.alias as project_alias,
			pd.id as product_id, pd.alias as product_alias,
			e.id as env_id, e.name as env_name, e.alias as env_alias
		FROM cmdb_appinfo ai
		JOIN cmdb_microapp ma ON ai.app_id = ma.id
		JOIN cmdb_project p ON ma.project_id = p.id
		JOIN cmdb_product pd ON p.product_id = pd.id
		JOIN cmdb_environment e ON ai.environment_id = e.id
		WHERE ai.is_enable = 1
	`

	countSQL := `
		SELECT COUNT(*) 
		FROM cmdb_appinfo ai
		JOIN cmdb_microapp ma ON ai.app_id = ma.id
		JOIN cmdb_project p ON ma.project_id = p.id
		JOIN cmdb_product pd ON p.product_id = pd.id
		JOIN cmdb_environment e ON ai.environment_id = e.id
		WHERE ai.is_enable = 1
	`

	var args []interface{}

	// 应用环境过滤
	if environmentIDStr != "" {
		environmentID, err := strconv.ParseUint(environmentIDStr, 10, 32)
		if err == nil {
			sql += " AND ai.environment_id = ?"
			countSQL += " AND ai.environment_id = ?"
			args = append(args, environmentID)
		}
	}

	// 应用搜索
	if search != "" {
		sql += " AND (ma.name LIKE ? OR ma.alias LIKE ? OR ma.appid LIKE ?)"
		countSQL += " AND (ma.name LIKE ? OR ma.alias LIKE ? OR ma.appid LIKE ?)"
		likePattern := "%" + search + "%"
		args = append(args, likePattern, likePattern, likePattern)
	}

	// 应用分类过滤
	if appCategory != "" {
		sql += " AND ma.category = ?"
		countSQL += " AND ma.category = ?"
		args = append(args, appCategory)
	}

	// 排序
	if ordering != "" {
		// 处理排序字段，前端传入的字段名可能与数据库不一致
		orderField := "ai.updated_at" // 默认按更新时间排序
		orderDir := "DESC"            // 默认降序

		if ordering[0] == '-' {
			orderDir = "DESC"
			ordering = ordering[1:]
		} else {
			orderDir = "ASC"
		}

		// 映射前端字段名到数据库字段名
		switch ordering {
		case "created_time":
			orderField = "ai.created_at"
		case "update_time":
			orderField = "ai.updated_at"
		case "app__name":
			orderField = "ma.name"
		case "app__alias":
			orderField = "ma.alias"
		case "environment__name":
			orderField = "e.name"
		}

		sql += " ORDER BY " + orderField + " " + orderDir
	} else {
		// 默认按更新时间降序排列
		sql += " ORDER BY ai.updated_at DESC"
	}

	// 计算总数
	var total int64
	err = c.db.Raw(countSQL, args...).Scan(&total).Error
	if err != nil {
		c.logger.Errorf("查询应用总数失败: %v", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "查询应用总数失败",
			"data":    nil,
		})
		return
	}

	// 没有结果时提前返回
	if total == 0 {
		ctx.JSON(http.StatusOK, gin.H{
			"code": 20000,
			"data": gin.H{
				"items": []interface{}{},
			},
		})
		return
	}

	// 分页
	offset := (page - 1) * pageSize
	sql += fmt.Sprintf(" LIMIT %d OFFSET %d", pageSize, offset)

	// 执行查询
	rows, err := c.db.Raw(sql, args...).Rows()
	if err != nil {
		c.logger.Errorf("查询应用信息失败: %v", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "查询应用信息失败",
			"data":    nil,
		})
		return
	}
	defer rows.Close()

	// 构建响应数据
	var items []gin.H
	for rows.Next() {
		// 定义变量存储查询结果
		var (
			id, appID, environmentID                                                        uint
			uniqTag, branch, versionStr                                                     string
			createdAtStr                                                                    string
			isEnable                                                                        int
			appName, appAlias, appID_str, category, language, repo, appDesc, isK8s, modules string
			projectID                                                                       uint
			projectAlias                                                                    string
			productID                                                                       uint
			productAlias                                                                    string
			envID                                                                           uint
			envName, envAlias                                                               string
		)

		// 扫描结果到变量
		err := rows.Scan(
			&id, &uniqTag, &appID, &environmentID, &branch, &versionStr, &createdAtStr, &isEnable,
			&appID, &appName, &appAlias, &appID_str, &category, &language, &repo, &appDesc, &isK8s, &modules,
			&projectID, &projectAlias,
			&productID, &productAlias,
			&envID, &envName, &envAlias,
		)
		if err != nil {
			c.logger.Warnf("扫描应用信息行失败: %v", err)
			continue
		}

		// 处理App基本信息
		appData := gin.H{
			"id":       appID,
			"appid":    appID_str,
			"name":     appName,
			"alias":    appAlias,
			"project":  projectAlias,
			"product":  productAlias,
			"category": category,
			"language": language,
			"desc":     appDesc,
			"is_k8s":   isK8s,
		}

		// 处理JSON字段
		var repoJSON, modulesJSON interface{}

		// 解析repo字段
		if repo != "" {
			if err := json.Unmarshal([]byte(repo), &repoJSON); err != nil {
				c.logger.Warnf("解析repo JSON失败: %v, 使用原始字符串", err)
				repoJSON = repo
			}
		} else {
			repoJSON = gin.H{}
		}
		appData["repo"] = repoJSON

		// 解析modules字段
		if modules != "" {
			if err := json.Unmarshal([]byte(modules), &modulesJSON); err != nil {
				c.logger.Warnf("解析modules JSON失败: %v, 使用原始字符串", err)
				modulesJSON = modules
			}
		} else {
			modulesJSON = []interface{}{}
		}
		appData["modules"] = modulesJSON

		// 获取应用的最新构建信息
		latestBuild, _ := getLatestBuild(c.db, c.logger, id)

		// 获取应用的最新部署信息
		latestDeploy, _ := getLatestDeploy(c.db, c.logger, id)

		// 获取应用关联的Kubernetes部署信息
		k8sInfo := getKubernetesDeployInfo(c.db, c.logger, id)

		// 生成namespace
		namespace := strings.ToLower(
			fmt.Sprintf("%s-%s",
				strings.Replace(envName, "_", "-", -1),
				strings.Replace(productAlias, "_", "-", -1),
			),
		)

		// 处理创建时间
		if createdAtStr == "" {
			createdAtStr = time.Now().Format("2006-01-02T15:04:05")
		}

		// 构建完整的应用信息
		appInfoData := gin.H{
			"id":              id,
			"uniq_tag":        uniqTag,
			"app":             appData,
			"branch":          branch,
			"created_time":    createdAtStr,
			"desc":            nil,
			"environment":     environmentID,
			"hosts":           []string{},
			"is_enable":       isEnable,
			"kubernetes":      []gin.H{},
			"kubernetes_info": k8sInfo,
			"last_build":      latestBuild,
			"last_deploy":     latestDeploy,
			"namespace":       namespace,
			"online":          3,
			"pipeline_dl":     gin.H{},
			"pipeline_id":     0,
			"ports":           []gin.H{},
			"update_time":     time.Now().Format("2006-01-02T15:04:05"),
			"version":         versionStr,
			"allow_cd_branch": []string{"*"},
			"allow_ci_branch": []string{"*"},
		}

		items = append(items, appInfoData)
	}

	// 返回结果
	ctx.JSON(http.StatusOK, gin.H{
		"code": 20000,
		"data": gin.H{
			"total": total,
			"items": items,
		},
	})
}

// getLatestBuild 获取最新构建信息
func getLatestBuild(db *gorm.DB, logger logrus.FieldLogger, appInfoID uint) (gin.H, bool) {
	type BuildResult struct {
		ID          uint
		CreatedAt   time.Time
		OrderID     string
		Status      int
		BuildNumber int
		Commits     string
		CommitTag   string
		Image       string
		DeployerID  uint
		Username    string
		FirstName   string
		Position    string
	}

	// 查询最新构建
	sql := `
		SELECT 
			b.id, b.created_at, b.order_id, b.status, b.build_number, 
			b.commits, b.commit_tag, b.image, b.deployer_id,
			u.username, u.first_name, u.position
		FROM deploy_buildjob b
		LEFT JOIN ucenter_userprofile u ON b.deployer_id = u.id
		WHERE b.app_info_id = ?
		ORDER BY b.created_at DESC
		LIMIT 1
	`

	var result BuildResult
	err := db.Raw(sql, appInfoID).Scan(&result).Error

	buildExists := true
	if err != nil {
		if err != gorm.ErrRecordNotFound {
			logger.Warnf("查询应用 %d 的最新构建失败: %v", appInfoID, err)
		}
		buildExists = false
		return gin.H{}, false
	}

	// 解析commits
	var commits gin.H
	if result.Commits != "" {
		if err := json.Unmarshal([]byte(result.Commits), &commits); err != nil {
			logger.Warnf("解析构建commits失败: %v", err)
		}
	}

	// 解析commit_tag
	var commitTag gin.H
	if result.CommitTag != "" {
		if err := json.Unmarshal([]byte(result.CommitTag), &commitTag); err != nil {
			logger.Warnf("解析构建commit_tag失败: %v", err)
		}
	}

	// 构建deployer信息
	deployerInfo := gin.H{}
	if result.DeployerID > 0 {
		deployerInfo = gin.H{
			"id":         result.DeployerID,
			"username":   result.Username,
			"first_name": result.FirstName,
			"position":   result.Position,
		}
	}

	return gin.H{
		"id":           result.ID,
		"created_time": result.CreatedAt,
		"order_id":     result.OrderID,
		"status":       result.Status,
		"build_number": result.BuildNumber,
		"commit_tag":   commitTag,
		"commits":      commits,
		"image":        result.Image,
		"type":         "ci",
		"deployer":     deployerInfo,
	}, buildExists
}

// getLatestDeploy 获取最新部署信息
func getLatestDeploy(db *gorm.DB, logger logrus.FieldLogger, appInfoID uint) (gin.H, bool) {
	type DeployResult struct {
		ID         uint
		CreatedAt  time.Time
		OrderID    string
		Status     int
		Image      string
		BatchUUID  string
		DeployerID uint
		Username   string
		FirstName  string
		Position   string
	}

	// 查询最新部署
	sql := `
		SELECT 
			d.id, d.created_at, d.order_id, d.status, d.image, d.batch_uuid, d.deployer_id,
			u.username, u.first_name, u.position
		FROM deploy_deployjob d
		LEFT JOIN ucenter_userprofile u ON d.deployer_id = u.id
		WHERE d.app_info_id = ?
		ORDER BY d.created_at DESC
		LIMIT 1
	`

	var result DeployResult
	err := db.Raw(sql, appInfoID).Scan(&result).Error

	deployExists := true
	if err != nil {
		if err != gorm.ErrRecordNotFound {
			logger.Warnf("查询应用 %d 的最新部署失败: %v", appInfoID, err)
		}
		deployExists = false
		return gin.H{}, false
	}

	// 构建deployer信息
	deployerInfo := gin.H{}
	if result.DeployerID > 0 {
		deployerInfo = gin.H{
			"id":         result.DeployerID,
			"username":   result.Username,
			"first_name": result.FirstName,
			"position":   result.Position,
		}
	}

	return gin.H{
		"id":           result.ID,
		"created_time": result.CreatedAt,
		"order_id":     result.OrderID,
		"status":       result.Status,
		"image":        result.Image,
		"batch_uuid":   result.BatchUUID,
		"type":         "cd",
		"deployer":     deployerInfo,
	}, deployExists
}

// getKubernetesDeployInfo 获取Kubernetes部署信息
func getKubernetesDeployInfo(db *gorm.DB, logger logrus.FieldLogger, appInfoID uint) []gin.H {
	type K8sDeployResult struct {
		ID           uint
		KubernetesID uint
		Name         string
		Description  string
		Online       int
		Version      string
	}

	// 查询Kubernetes部署信息
	sql := `
		SELECT 
			kd.id, kd.kubernetes_id, 
			kc.name, kc.desc as description, kd.online, kd.version
		FROM cmdb_kubernetesdeploy kd
		JOIN cmdb_kubernetesclusters kc ON kd.kubernetes_id = kc.id
		WHERE kd.app_info_id = ?
	`

	var results []K8sDeployResult
	err := db.Raw(sql, appInfoID).Scan(&results).Error
	if err != nil {
		logger.Warnf("查询应用 %d 的Kubernetes部署信息失败: %v", appInfoID, err)
		return []gin.H{}
	}

	var k8sInfo []gin.H
	for _, result := range results {
		k8sInfo = append(k8sInfo, gin.H{
			"id": result.ID,
			"kubernetes": gin.H{
				"id":   result.KubernetesID,
				"name": result.Name,
				"desc": result.Description,
			},
			"online":  result.Online,
			"version": result.Version,
		})
	}

	return k8sInfo
}

// GetBuildImages 获取可部署的镜像列表
// @Summary 获取可部署的镜像列表
// @Description 根据应用信息ID和状态获取可用于部署的镜像列表
// @Tags 构建管理
// @Accept json
// @Produce json
// @Param app_info_id query int true "应用信息ID"
// @Param status query int false "构建状态 (1=成功, 2=失败, 3=构建中, 4=已取消)" default(1)
// @Param rollback query bool false "是否用于回滚" default(false)
// @Success 200 {object} map[string]interface{} "成功返回镜像列表"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 500 {object} map[string]interface{} "服务器内部错误"
// @Router /api/cicd/image/ [get]
func (c *BuildController) GetBuildImages(ctx *gin.Context) {
	// 获取查询参数
	appInfoIDStr := ctx.Query("app_info_id")
	statusStr := ctx.DefaultQuery("status", "1") // 默认只获取成功的构建
	rollbackStr := ctx.DefaultQuery("rollback", "false")

	if appInfoIDStr == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    40000,
			"message": "app_info_id 参数是必需的",
			"data":    nil,
		})
		return
	}

	appInfoID, err := strconv.ParseUint(appInfoIDStr, 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    40000,
			"message": "无效的 app_info_id",
			"data":    nil,
		})
		return
	}

	status, err := strconv.Atoi(statusStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    40000,
			"message": "无效的 status 参数",
			"data":    nil,
		})
		return
	}

	rollback, err := strconv.ParseBool(rollbackStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    40000,
			"message": "无效的 rollback 参数",
			"data":    nil,
		})
		return
	}

	// 构建查询条件，预加载Deployer信息
	query := c.db.Preload("Deployer").Where("app_info_id = ? AND status = ?", uint(appInfoID), status)

	// 如果不是回滚，只获取最近的构建
	if !rollback {
		query = query.Order("created_at DESC").Limit(10)
	} else {
		// 如果是回滚，获取更多历史记录
		query = query.Order("created_at DESC").Limit(50)
	}

	// 定义包含Deployer关联的BuildJob结构
	type BuildJobWithDeployer struct {
		models.BuildJob
		Deployer *models.UserProfile `gorm:"foreignKey:DeployerID;references:ID"`
	}

	var buildJobs []BuildJobWithDeployer
	if err := query.Find(&buildJobs).Error; err != nil {
		c.logger.Errorf("查询构建任务失败: %v", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "查询构建任务失败",
			"data":    nil,
		})
		return
	}

	// 转换为前端需要的格式
	var images []gin.H
	for _, buildJob := range buildJobs {
		if buildJob.Image != "" {
			// 解析 commits JSON
			var commits gin.H
			if buildJob.Commits != "" {
				if err := json.Unmarshal([]byte(buildJob.Commits), &commits); err != nil {
					c.logger.Warnf("解析commits失败: %v", err)
					commits = gin.H{}
				}
			}

			// 解析 commit_tag JSON
			var commitTag gin.H
			if buildJob.CommitTag != "" {
				if err := json.Unmarshal([]byte(buildJob.CommitTag), &commitTag); err != nil {
					c.logger.Warnf("解析commit_tag失败: %v", err)
					commitTag = gin.H{}
				}
			}

			// 构建 deployer_info
			deployerInfo := gin.H{}
			if buildJob.Deployer != nil {
				deployerInfo = gin.H{
					"id":         buildJob.Deployer.ID,
					"first_name": buildJob.Deployer.FirstName,
					"username":   buildJob.Deployer.Username,
					"name":       buildJob.Deployer.Name(),
					"position":   buildJob.Deployer.Position,
				}
			}

			// 格式化时间为前端需要的格式
			updateTime := buildJob.UpdatedAt.Format("2006-01-02T15:04:05")
			createdTime := buildJob.CreatedAt.Format("2006-01-02T15:04:05")

			imageData := gin.H{
				"id":            buildJob.ID,
				"deployer_info": deployerInfo,
				"update_time":   updateTime,
				"created_time":  createdTime,
				"order_id":      buildJob.OrderID,
				"appid":         buildJob.AppID,
				"app_info_id":    buildJob.AppInfoID,
				"status":        buildJob.Status,
				"queue_number":  buildJob.QueueNumber,
				"build_number":  buildJob.BuildNumber,
				"commits":       commits,
				"commit_tag":    commitTag,
				"is_deploy":     buildJob.IsDeploy,
				"jenkins_flow":  "", // 留空，因为使用Tekton
				"image":         buildJob.Image,
				"sync_status":   buildJob.SyncStatus,
				"modules":       buildJob.Modules,
				"batch_uuid":    buildJob.BatchUUID,
				"deployer":      buildJob.DeployerID,
			}

			images = append(images, imageData)
		}
	}

	c.logger.Infof("成功获取镜像列表: AppInfoID=%d, Status=%d, Count=%d", appInfoID, status, len(images))
	ctx.JSON(http.StatusOK, gin.H{
		"code": 20000,
		"data": images,
	})
}
