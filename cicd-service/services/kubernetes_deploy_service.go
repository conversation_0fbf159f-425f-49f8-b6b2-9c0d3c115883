package services

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/devops-microservices/cicd-service/config"
	"github.com/devops-microservices/cicd-service/models"
	"github.com/devops-microservices/cicd-service/utils"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/intstr"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
)

// KubernetesDeployService Kubernetes部署服务
type KubernetesDeployService struct {
	db     *gorm.DB
	logger logrus.FieldLogger
	config *config.Config
}

// NewKubernetesDeployService 创建新的Kubernetes部署服务
func NewKubernetesDeployService(db *gorm.DB, logger logrus.FieldLogger, cfg *config.Config) *KubernetesDeployService {
	return &KubernetesDeployService{
		db:     db,
		logger: logger,
		config: cfg,
	}
}

// K8sConfigType Kubernetes配置类型
type K8sConfigType struct {
	Type   string      `json:"type"`
	Config interface{} `json:"config"`
}

// BasicAuthConfig 基础认证配置
type BasicAuthConfig struct {
	Host     string `json:"host"`
	Username string `json:"username"`
	Password string `json:"password"`
	Token    string `json:"token"`
	Insecure bool   `json:"insecure"`
}

// DeploymentParams 部署参数
type DeploymentParams struct {
	AppName       string
	Namespace     string
	Image         string
	Replicas      int32
	Force         bool
	AppLabels     map[string]string
	ServiceConfig map[string]interface{}
	Ports         []int32
}

// getImageTag 从镜像字符串中提取标签部分
func getImageTag(image string) string {
	parts := strings.Split(image, ":")
	if len(parts) > 1 {
		return parts[len(parts)-1]
	}
	return "latest"
}

// ExecuteDeployment 执行实际的部署操作
func (s *KubernetesDeployService) ExecuteDeployment(
	deployJob models.DeployJob,
	k8sCluster models.KubernetesCluster,
	appInfo *models.AppInfo,
	app *models.MicroApp,
	environment models.Environment,
) error {
	s.logger.Infof("开始执行Kubernetes部署: JobID=%d, Cluster=%s, Image=%s",
		deployJob.ID, k8sCluster.Name, deployJob.Image)

	// 解密Kubernetes配置
	k8sConfig, err := s.decryptKubernetesConfig(k8sCluster.ConfigData.Data.(string))
	if err != nil {
		s.logger.Errorf("解密Kubernetes配置失败: %v", err)
		return fmt.Errorf("解密Kubernetes配置失败: %v", err)
	}

	// 创建Kubernetes客户端
	k8sClient, err := s.createKubernetesClient(k8sConfig)
	if err != nil {
		s.logger.Errorf("创建Kubernetes客户端失败: %v", err)
		return fmt.Errorf("创建Kubernetes客户端失败: %v", err)
	}

	// 构建部署参数
	deployParams := s.buildDeploymentParams(deployJob, appInfo, app, environment)

	ctx := context.TODO()

	// 1. 创建或确保Namespace存在
	if err := s.ensureNamespace(ctx, k8sClient, deployParams.Namespace); err != nil {
		return fmt.Errorf("创建命名空间失败: %v", err)
	}

	// 2. 确保Harbor Secret存在
	if err := s.ensureHarborSecret(ctx, k8sClient, deployParams.Namespace); err != nil {
		return fmt.Errorf("创建Harbor Secret失败: %v", err)
	}

	// 3. 使用新的YAML模板部署应用
	if err := s.deployApplicationWithTemplate(ctx, k8sClient, deployParams, appInfo, app, environment); err != nil {
		return fmt.Errorf("部署应用失败: %v", err)
	}

	// 4. 创建或更新Service
	if err := s.deployServiceWithTemplate(ctx, k8sClient, deployParams, appInfo); err != nil {
		return fmt.Errorf("部署服务失败: %v", err)
	}

	// 5. 检查部署状态
	if err := s.checkDeploymentStatus(ctx, k8sClient, deployParams); err != nil {
		return fmt.Errorf("检查部署状态失败: %v", err)
	}

	// 更新部署状态为成功
	if err := s.db.Model(&deployJob).Updates(map[string]interface{}{
		"status": 1, // 部署成功
	}).Error; err != nil {
		s.logger.Errorf("更新部署状态失败: %v", err)
		return err
	}

	// 保存部署结果，参考Python K8sDeploys类
	// deployResult := map[string]interface{}{
	// 	"status":     1,
	// 	"col_active": k8sCluster.Name,
	// 	"result": map[string]interface{}{
	// 		"col_active": k8sCluster.Name,
	// 		k8sCluster.Name: map[string]interface{}{
	// 			"stages": []map[string]interface{}{
	// 				{
	// 					"name":   "Connect Kubernetes",
	// 					"status": 1,
	// 					"logs":   fmt.Sprintf("{\"apiVersion\": \"apps/v1\", \"kubeconfig\": \"%s\"}", k8sCluster.Name),
	// 					"msg":    fmt.Sprintf("连接到Kubernetes集群 %s 成功", k8sCluster.Name),
	// 				},
	// 				{
	// 					"name":   "Image Sync",
	// 					"status": 1,
	// 					"logs":   fmt.Sprintf("{\"image\": \"%s\", \"registry\": \"%s\"}", deployJob.Image, getImageRegistry(deployJob.Image)),
	// 					"msg":    fmt.Sprintf("镜像 %s 同步成功", deployJob.Image),
	// 				},
	// 				{
	// 					"name":   "Deployment",
	// 					"status": 1,
	// 					"logs":   fmt.Sprintf("{\"deployment\": \"%s\", \"namespace\": \"%s\", \"replicas\": %d}", app.Name, deployParams.Namespace, deployParams.Replicas),
	// 					"msg":    fmt.Sprintf("准备更新Deployment[%s-%s]部署", app.Name, environment.Name),
	// 				},
	// 				{
	// 					"name":   "Status Check",
	// 					"status": 1,
	// 					"logs":   fmt.Sprintf("{\"conditions\": [{\"lastTransitionTime\": \"%s\", \"status\": \"True\", \"type\": \"Available\"}], \"replicas\": %d, \"availableReplicas\": %d}", time.Now().Format(time.RFC3339), deployParams.Replicas, deployParams.Replicas),
	// 					"msg":    fmt.Sprintf("%s-%s-%s 部署成功", app.Name, environment.Name, getImageTag(deployJob.Image)),
	// 				},
	// 			},
	// 			"status": 1,
	// 		},
	// 		"worker": fmt.Sprintf("%x", md5.Sum([]byte(time.Now().String()))),
	// 	},
	// 	"created_time": time.Now().Format(time.RFC3339),
	// 	"id":           deployJob.ID,
	// 	"job_id":       deployJob.ID,
	// }
	// deployResult := map[string]interface{}{
	// 	"col_active": k8sCluster.Name,
	// 	k8sCluster.Name: map[string]interface{}{
	// 		"stages": []map[string]interface{}{
	// 			{
	// 				"name":   "Connect Kubernetes",
	// 				"status": 1,
	// 				"logs":   fmt.Sprintf("{\"apiVersion\": \"apps/v1\", \"kubeconfig\": \"%s\"}", k8sCluster.Name),
	// 				"msg":    fmt.Sprintf("连接到Kubernetes集群 %s 成功", k8sCluster.Name),
	// 			},
	// 			{
	// 				"name":   "Image Sync",
	// 				"status": 1,
	// 				"logs":   fmt.Sprintf("{\"image\": \"%s\", \"registry\": \"%s\"}", deployJob.Image, getImageRegistry(deployJob.Image)),
	// 				"msg":    fmt.Sprintf("镜像 %s 同步成功", deployJob.Image),
	// 			},
	// 			{
	// 				"name":   "Deployment",
	// 				"status": 1,
	// 				"logs":   fmt.Sprintf("{\"deployment\": \"%s\", \"namespace\": \"%s\", \"replicas\": %d}", app.Name, deployParams.Namespace, deployParams.Replicas),
	// 				"msg":    fmt.Sprintf("准备更新Deployment[%s-%s]部署", app.Name, environment.Name),
	// 			},
	// 			{
	// 				"name":   "Status Check",
	// 				"status": 1,
	// 				"logs":   fmt.Sprintf("{\"conditions\": [{\"lastTransitionTime\": \"%s\", \"status\": \"True\", \"type\": \"Available\"}], \"replicas\": %d, \"availableReplicas\": %d}", time.Now().Format(time.RFC3339), deployParams.Replicas, deployParams.Replicas),
	// 				"msg":    fmt.Sprintf("%s-%s-%s 部署成功", app.Name, environment.Name, getImageTag(deployJob.Image)),
	// 			},
	// 		},
	// 		"status": 1,
	// 	},
	// }

	// deployResultJSON, _ := json.Marshal(deployResult)
	// deployJobResult := models.DeployJobResult{
	// 	JobID:  deployJob.ID,
	// 	Result: string(deployResultJSON),
	// }

	// if err := s.db.Create(&deployJobResult).Error; err != nil {
	// 	s.logger.Errorf("保存部署结果失败: %v", err)
	// 	return err
	// }

	s.logger.Infof("部署执行成功: JobID=%d", deployJob.ID)
	return nil
}

// decryptKubernetesConfig 解密Kubernetes配置
func (s *KubernetesDeployService) decryptKubernetesConfig(encryptedConfig string) (map[string]interface{}, error) {
	s.logger.Infof("🔧 开始解密Kubernetes配置，配置长度: %d", len(encryptedConfig))

	// 输出配置内容的前100个字符用于调试
	if len(encryptedConfig) > 100 {
		s.logger.Infof("📄 配置内容前100字符: %s", encryptedConfig[:100])
	} else {
		s.logger.Infof("📄 完整配置内容: %s", encryptedConfig)
	}

	// 检查配置是否为空
	if encryptedConfig == "" {
		return nil, fmt.Errorf("配置为空")
	}

	// 首先尝试检查是否为直接的Fernet加密字符串
	// Fernet token以 "gAAAAA" 开头（Base64编码后的0x80000000开头）
	if len(encryptedConfig) > 50 && (encryptedConfig[:6] == "gAAAAA" || encryptedConfig[:4] == "gAAA") {
		s.logger.Info("🔐 检测到直接的Fernet加密字符串，尝试解密...")

		// 获取加密配置
		secretKey := s.config.Encryption.Key
		salt := s.config.Encryption.Salt
		info := s.config.Encryption.Info
		if secretKey == "" {
			secretKey = "7f=5@e+a=b(ghm-l*mtc_ile60xuvxqi(l5y$3&gfpk1!)3_4v" // 默认使用Django SECRET_KEY
			s.logger.Warn("⚠️ 使用默认加密密钥")
		}
		if salt == "" {
			salt = "django-fernet-fields-hkdf-salt" // 默认盐值
			s.logger.Warn("⚠️ 使用默认盐值")
		}
		if info == "" {
			info = "django-fernet-fields" // 默认info
			s.logger.Warn("⚠️ 使用默认info")
		}

		s.logger.Infof("🔑 使用密钥: %s", secretKey)
		s.logger.Infof("🧂 使用盐值: %s", salt)
		s.logger.Infof("ℹ️ 使用info: %s", info)

		// 使用你的EncryptedField解密
		ef, err := utils.NewEncryptedField([]string{secretKey}, salt, info)
		if err != nil {
			s.logger.Errorf("❌ 创建EncryptedField失败: %v", err)
			return nil, fmt.Errorf("创建EncryptedField失败: %v", err)
		}

		decryptedData, err := ef.Decrypt([]byte(encryptedConfig))
		if err != nil {
			s.logger.Errorf("❌ Fernet解密失败: %v", err)
			return nil, fmt.Errorf("Fernet解密失败: %v", err)
		}

		s.logger.Infof("✅ Fernet解密成功，解密数据长度: %d", len(decryptedData))

		// 尝试解析为JSON
		var decryptedConfig interface{}
		if err := json.Unmarshal(decryptedData, &decryptedConfig); err != nil {
			// 如果不是JSON，作为字符串处理
			s.logger.Info("📝 解密数据不是JSON格式，作为kubeconfig字符串处理")
			return map[string]interface{}{
				"type":       "config",
				"kubeconfig": string(decryptedData),
			}, nil
		}

		// 如果是JSON，检查是否是K8s配置格式
		if configMap, ok := decryptedConfig.(map[string]interface{}); ok {
			if configType, exists := configMap["type"]; exists {
				s.logger.Infof("📋 解密后配置类型: %v", configType)

				// 检查配置类型并转换为标准格式
				switch configType {
				case "config":
					// 如果有config字段，将其重命名为kubeconfig
					if configContent, hasConfig := configMap["config"]; hasConfig {
						return map[string]interface{}{
							"type":       "config",
							"kubeconfig": configContent,
						}, nil
					}
					// 如果已经有kubeconfig字段，直接返回
					if _, hasKubeconfig := configMap["kubeconfig"]; hasKubeconfig {
						return configMap, nil
					}
					// 否则作为整个内容处理
					configBytes, _ := json.Marshal(configMap)
					return map[string]interface{}{
						"type":       "config",
						"kubeconfig": string(configBytes),
					}, nil
				case "basic":
					// 基础认证配置，直接返回
					return configMap, nil
				default:
					// 其他类型，作为kubeconfig处理
					configBytes, _ := json.Marshal(configMap)
					return map[string]interface{}{
						"type":       "config",
						"kubeconfig": string(configBytes),
					}, nil
				}
			}
		}

		// 默认作为kubeconfig处理
		return map[string]interface{}{
			"type":       "config",
			"kubeconfig": string(decryptedData),
		}, nil
	}

	// 尝试解析为JSON格式配置
	s.logger.Info("📋 尝试解析为JSON格式配置...")
	var configType K8sConfigType
	if err := json.Unmarshal([]byte(encryptedConfig), &configType); err != nil {
		s.logger.Errorf("❌ JSON解析失败: %v", err)
		s.logger.Errorf("📄 配置内容前200字符: %s", encryptedConfig[:min(200, len(encryptedConfig))])

		// 如果JSON解析失败，尝试直接作为配置使用
		s.logger.Warn("⚠️ JSON解析失败，尝试将配置作为明文kubeconfig使用")
		return map[string]interface{}{
			"type":       "config",
			"kubeconfig": encryptedConfig,
		}, nil
	}

	s.logger.Infof("✅ JSON解析成功，Kubernetes配置类型: %s", configType.Type)

	// 根据配置类型处理
	switch configType.Type {
	case "basic":
		// basic auth或token auth，配置已经明文
		s.logger.Info("🔧 处理基础认证配置...")
		if config, ok := configType.Config.(map[string]interface{}); ok {
			s.logger.Info("✅ 基础认证配置解析成功")
			// 为安全起见，不在日志中输出完整配置
			configCopy := make(map[string]interface{})
			for k, v := range config {
				if k == "password" || k == "token" {
					configCopy[k] = "******" // 隐藏敏感信息
				} else {
					configCopy[k] = v
				}
			}
			s.logger.Infof("📋 基础认证配置 (隐藏敏感信息): %+v", configCopy)
			return config, nil
		}
		return nil, fmt.Errorf("无效的basic配置格式")

	case "config":
		// kubeconfig格式，可能需要解密
		s.logger.Info("📝 处理kubeconfig格式配置...")
		if configStr, ok := configType.Config.(string); ok {
			s.logger.Infof("📄 kubeconfig字符串长度: %d", len(configStr))

			// 检查是否为Fernet加密的字符串
			if len(configStr) > 50 && (configStr[:6] == "gAAAAA" || configStr[:4] == "gAAA") {
				s.logger.Info("🔐 检测到Fernet加密的kubeconfig，尝试解密...")

				// 获取加密配置
				secretKey := s.config.Encryption.Key
				salt := s.config.Encryption.Salt
				info := s.config.Encryption.Info
				if secretKey == "" {
					secretKey = "7f=5@e+a=b(ghm-l*mtc_ile60xuvxqi(l5y$3&gfpk1!)3_4v" // 默认使用Django SECRET_KEY
					s.logger.Warn("⚠️ 使用默认加密密钥")
				}
				if salt == "" {
					salt = "django-fernet-fields-hkdf-salt" // 默认盐值
					s.logger.Warn("⚠️ 使用默认盐值")
				}
				if info == "" {
					info = "django-fernet-fields" // 默认info
					s.logger.Warn("⚠️ 使用默认info")
				}

				// 使用Fernet解密
				ef, err := utils.NewEncryptedField([]string{secretKey}, salt, info)
				if err != nil {
					s.logger.Errorf("❌ 创建EncryptedField失败: %v", err)
					return nil, fmt.Errorf("创建EncryptedField失败: %v", err)
				}

				decryptedData, err := ef.Decrypt([]byte(configStr))
				if err != nil {
					s.logger.Warnf("⚠️ Fernet解密失败，尝试直接使用配置: %v", err)
					// 如果解密失败，尝试直接使用
					return map[string]interface{}{
						"type":       "config",
						"kubeconfig": configStr,
					}, nil
				} else {
					s.logger.Infof("✅ Fernet解密成功，配置长度: %d", len(decryptedData))
					return map[string]interface{}{
						"type":       "config",
						"kubeconfig": string(decryptedData),
					}, nil
				}
			} else {
				s.logger.Info("📝 检测到明文kubeconfig，直接使用...")
				// 直接使用明文配置
				return map[string]interface{}{
					"type":       "config",
					"kubeconfig": configStr,
				}, nil
			}
		}
		return nil, fmt.Errorf("无效的config配置格式")

	default:
		return nil, fmt.Errorf("不支持的Kubernetes配置类型: %s", configType.Type)
	}
}

// min 辅助函数，返回两个整数中的较小值
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// createKubernetesClient 创建Kubernetes客户端
func (s *KubernetesDeployService) createKubernetesClient(config map[string]interface{}) (*kubernetes.Clientset, error) {
	s.logger.Infof("🔧 开始创建Kubernetes客户端，配置内容: %+v", config)

	configTypeRaw, exists := config["type"]
	if !exists {
		return nil, fmt.Errorf("配置中缺少type字段")
	}

	configType, ok := configTypeRaw.(string)
	if !ok {
		return nil, fmt.Errorf("配置type字段不是字符串类型: %T", configTypeRaw)
	}

	s.logger.Infof("📋 Kubernetes客户端配置类型: %s", configType)

	switch configType {
	case "basic":
		// 基础认证
		s.logger.Info("🔑 使用基础认证创建Kubernetes客户端")
		var basicConfig BasicAuthConfig
		configBytes, _ := json.Marshal(config)
		if err := json.Unmarshal(configBytes, &basicConfig); err != nil {
			return nil, fmt.Errorf("解析基础认证配置失败: %v", err)
		}

		// 创建REST配置
		restConfig := &rest.Config{
			Host:            basicConfig.Host,
			Username:        basicConfig.Username,
			Password:        basicConfig.Password,
			BearerToken:     basicConfig.Token,
			TLSClientConfig: rest.TLSClientConfig{Insecure: basicConfig.Insecure},
		}

		s.logger.Infof("✅ 基础认证配置创建成功，Host: %s", basicConfig.Host)
		return kubernetes.NewForConfig(restConfig)

	case "config":
		// kubeconfig格式
		s.logger.Info("📄 使用kubeconfig格式创建Kubernetes客户端")

		kubeconfigRaw, exists := config["kubeconfig"]
		if !exists {
			return nil, fmt.Errorf("配置中缺少kubeconfig字段")
		}

		kubeconfigStr, ok := kubeconfigRaw.(string)
		if !ok {
			return nil, fmt.Errorf("kubeconfig字段不是字符串类型: %T", kubeconfigRaw)
		}

		if kubeconfigStr == "" {
			return nil, fmt.Errorf("kubeconfig内容为空")
		}

		s.logger.Infof("📝 kubeconfig内容长度: %d", len(kubeconfigStr))

		// 解析kubeconfig
		restConfig, err := clientcmd.RESTConfigFromKubeConfig([]byte(kubeconfigStr))
		if err != nil {
			return nil, fmt.Errorf("解析kubeconfig失败: %v", err)
		}

		s.logger.Infof("✅ kubeconfig解析成功，服务器地址: %s", restConfig.Host)
		return kubernetes.NewForConfig(restConfig)

	default:
		return nil, fmt.Errorf("不支持的客户端配置类型: %s", configType)
	}
}

// buildDeploymentParams 构建部署参数
func (s *KubernetesDeployService) buildDeploymentParams(
	deployJob models.DeployJob,
	appInfo *models.AppInfo,
	app *models.MicroApp,
	environment models.Environment,
) *DeploymentParams {
	// 解析端口配置
	var ports []int32
	if appInfo.PortSettings.Data != nil {
		var portConfig map[string]interface{}
		if err := json.Unmarshal([]byte(appInfo.PortSettings.Data.(string)), &portConfig); err == nil {
			if portsArray, ok := portConfig["ports"].([]interface{}); ok {
				for _, port := range portsArray {
					if portNum, ok := port.(float64); ok {
						ports = append(ports, int32(portNum))
					}
				}
			}
		}
	}

	// 如果没有配置端口，使用默认端口
	if len(ports) == 0 {
		ports = []int32{80}
	}

	// 修正命名空间格式为 {environmentName.lower}-{productName.lower}
	namespace := fmt.Sprintf("%s-%s",
		strings.ToLower(environment.Name),
		strings.ToLower(app.Project.Product.Name))

	return &DeploymentParams{
		AppName:   app.Name,
		Namespace: namespace,
		Image:     deployJob.Image,
		Replicas:  1,
		Force:     true, // 从deployJob.Kubernetes解析
		AppLabels: map[string]string{
			"app":         app.Name,
			"environment": environment.Name,
			"version":     "v1",
		},
		ServiceConfig: map[string]interface{}{
			"type": "ClusterIP",
		},
		Ports: ports,
	}
}

// generateDeploymentYAML 生成Kubernetes Deployment YAML，参考Python的template_generate方法
func (s *KubernetesDeployService) generateDeploymentYAML(params *DeploymentParams, appInfo *models.AppInfo, app *models.MicroApp, environment models.Environment) *appsv1.Deployment {
	// 构建容器端口
	var containerPorts []corev1.ContainerPort
	for _, port := range params.Ports {
		portName := fmt.Sprintf("%s-%d", app.Name, port)
		if len(portName) > 15 {
			// name must be no more than 15 characters
			portName = fmt.Sprintf("%s-%d", app.Name[:10], port)
		}
		containerPorts = append(containerPorts, corev1.ContainerPort{
			Name:          portName,
			ContainerPort: port,
			Protocol:      corev1.ProtocolTCP,
		})
	}

	// 构建环境变量
	envVars := []corev1.EnvVar{
		{Name: "TZ", Value: "Asia/Shanghai"},
		{Name: "_RESTART", Value: time.Now().Format("20060102150405")},
		{Name: "PRODUCT_NAME", Value: app.Project.Product.Name},
		{Name: "PROJECT_NAME", Value: app.Project.Name},
		{Name: "APPNAME", Value: app.Name},
		{Name: "APPID", Value: fmt.Sprintf("%d", app.ID)},
		{Name: "ENV", Value: environment.Name},
		{Name: "POD_NAMESPACE", Value: params.Namespace},
	}

	// 构建标签
	labels := map[string]string{
		"app":                              app.Name,
		"environment":                      environment.Name,
		"version":                          "v1",
		"status-app-name-for-ops-platform": app.Name,
	}

	// 构建卷挂载
	volumeMounts := []corev1.VolumeMount{
		{
			Name:      "logs",
			MountPath: "/data/logs",
			ReadOnly:  false,
		},
	}

	// 构建卷
	volumes := []corev1.Volume{
		{
			Name: "logs",
			VolumeSource: corev1.VolumeSource{
				HostPath: &corev1.HostPathVolumeSource{
					Path: fmt.Sprintf("/data/%s-applogs/%s/", strings.ToLower(environment.Name), app.Project.Name),
				},
			},
		},
	}

	// 构建容器
	container := corev1.Container{
		Name:  app.Name,
		Image: params.Image,
		Ports: containerPorts,
		Env:   envVars,
		Resources: corev1.ResourceRequirements{
			Requests: corev1.ResourceList{
				corev1.ResourceCPU:    resource.MustParse("100m"),
				corev1.ResourceMemory: resource.MustParse("128Mi"),
			},
			Limits: corev1.ResourceList{
				corev1.ResourceCPU:    resource.MustParse("500m"),
				corev1.ResourceMemory: resource.MustParse("512Mi"),
			},
		},
		VolumeMounts: volumeMounts,
	}

	// 构建Pod模板
	podTemplate := corev1.PodTemplateSpec{
		ObjectMeta: metav1.ObjectMeta{
			Labels: labels,
			Annotations: map[string]string{
				"prometheus.io/app_product": app.Project.Product.Name,
				"prometheus.io/app_env":     environment.Name,
				"prometheus.io/app_project": app.Project.Name,
			},
		},
		Spec: corev1.PodSpec{
			Containers: []corev1.Container{container},
			ImagePullSecrets: []corev1.LocalObjectReference{
				{Name: "loginharbor"},
			},
			HostAliases: []corev1.HostAlias{
				{
					IP:        "************",
					Hostnames: []string{"harbor.fundpark.com", "source.fundpark.com"},
				},
				{
					IP:        "************",
					Hostnames: []string{"devops.fundpark.com"},
				},
			},
			TerminationGracePeriodSeconds: &[]int64{120}[0],
			Volumes:                       volumes,
		},
	}

	// 构建Deployment
	deployment := &appsv1.Deployment{
		ObjectMeta: metav1.ObjectMeta{
			Name:      app.Name,
			Namespace: params.Namespace,
			Labels:    labels,
		},
		Spec: appsv1.DeploymentSpec{
			Replicas: &params.Replicas,
			Selector: &metav1.LabelSelector{
				MatchLabels: map[string]string{
					"app": app.Name,
				},
			},
			Template: podTemplate,
			Strategy: appsv1.DeploymentStrategy{
				Type: appsv1.RollingUpdateDeploymentStrategyType,
				RollingUpdate: &appsv1.RollingUpdateDeployment{
					MaxSurge:       &intstr.IntOrString{Type: intstr.String, StrVal: "25%"},
					MaxUnavailable: &intstr.IntOrString{Type: intstr.String, StrVal: "25%"},
				},
			},
		},
	}

	s.logger.Infof("📄 生成Deployment YAML: %s/%s", params.Namespace, app.Name)
	return deployment
}

// deployToKubernetes 部署到Kubernetes
func (s *KubernetesDeployService) deployToKubernetes(client *kubernetes.Clientset, params *DeploymentParams) error {
	s.logger.Infof("部署应用到Kubernetes: App=%s, Namespace=%s, Image=%s",
		params.AppName, params.Namespace, params.Image)

	ctx := context.TODO()

	// 1. 创建或确保Namespace存在
	if err := s.ensureNamespace(ctx, client, params.Namespace); err != nil {
		return fmt.Errorf("创建命名空间失败: %v", err)
	}

	// 2. 确保Harbor Secret存在
	if err := s.ensureHarborSecret(ctx, client, params.Namespace); err != nil {
		return fmt.Errorf("创建Harbor Secret失败: %v", err)
	}

	// 3. 创建或更新Deployment
	if err := s.deployApplication(ctx, client, params); err != nil {
		return fmt.Errorf("部署应用失败: %v", err)
	}

	// 4. 创建或更新Service
	if err := s.deployService(ctx, client, params); err != nil {
		return fmt.Errorf("部署服务失败: %v", err)
	}

	// 5. 检查部署状态
	if err := s.checkDeploymentStatus(ctx, client, params); err != nil {
		return fmt.Errorf("检查部署状态失败: %v", err)
	}

	s.logger.Infof("应用成功部署到Kubernetes: %s/%s", params.Namespace, params.AppName)
	return nil
}

// ensureNamespace 确保命名空间存在
func (s *KubernetesDeployService) ensureNamespace(ctx context.Context, client *kubernetes.Clientset, namespace string) error {
	_, err := client.CoreV1().Namespaces().Get(ctx, namespace, metav1.GetOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			// 创建命名空间
			ns := &corev1.Namespace{
				ObjectMeta: metav1.ObjectMeta{
					Name: namespace,
				},
			}
			_, err = client.CoreV1().Namespaces().Create(ctx, ns, metav1.CreateOptions{})
			if err != nil {
				return fmt.Errorf("创建命名空间失败: %v", err)
			}
			s.logger.Infof("成功创建命名空间: %s", namespace)
		} else {
			return fmt.Errorf("获取命名空间失败: %v", err)
		}
	}
	return nil
}

// deployApplication 部署应用
func (s *KubernetesDeployService) deployApplicationWithTemplate(ctx context.Context, client *kubernetes.Clientset, params *DeploymentParams, appInfo *models.AppInfo, app *models.MicroApp, environment models.Environment) error {
	// 使用新的YAML生成方法
	deployment := s.generateDeploymentYAML(params, appInfo, app, environment)

	// 检查Deployment是否已存在
	_, err := client.AppsV1().Deployments(params.Namespace).Get(ctx, params.AppName, metav1.GetOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			// 创建新的Deployment
			_, err = client.AppsV1().Deployments(params.Namespace).Create(ctx, deployment, metav1.CreateOptions{})
			if err != nil {
				return fmt.Errorf("创建Deployment失败: %v", err)
			}
			s.logger.Infof("成功创建Deployment: %s/%s", params.Namespace, params.AppName)
		} else {
			return fmt.Errorf("获取Deployment失败: %v", err)
		}
	} else {
		// 更新现有Deployment
		_, err = client.AppsV1().Deployments(params.Namespace).Update(ctx, deployment, metav1.UpdateOptions{})
		if err != nil {
			return fmt.Errorf("更新Deployment失败: %v", err)
		}
		s.logger.Infof("成功更新Deployment: %s/%s", params.Namespace, params.AppName)
	}

	return nil
}

// deployApplication 部署应用（保持兼容性）
func (s *KubernetesDeployService) deployApplication(ctx context.Context, client *kubernetes.Clientset, params *DeploymentParams) error {
	deployment := s.createDeployment(params)

	// 检查Deployment是否已存在
	_, err := client.AppsV1().Deployments(params.Namespace).Get(ctx, params.AppName, metav1.GetOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			// 创建新的Deployment
			_, err = client.AppsV1().Deployments(params.Namespace).Create(ctx, deployment, metav1.CreateOptions{})
			if err != nil {
				return fmt.Errorf("创建Deployment失败: %v", err)
			}
			s.logger.Infof("成功创建Deployment: %s/%s", params.Namespace, params.AppName)
		} else {
			return fmt.Errorf("获取Deployment失败: %v", err)
		}
	} else {
		// 更新现有Deployment
		_, err = client.AppsV1().Deployments(params.Namespace).Update(ctx, deployment, metav1.UpdateOptions{})
		if err != nil {
			return fmt.Errorf("更新Deployment失败: %v", err)
		}
		s.logger.Infof("成功更新Deployment: %s/%s", params.Namespace, params.AppName)
	}

	return nil
}

// createDeployment 创建Deployment对象
func (s *KubernetesDeployService) createDeployment(params *DeploymentParams) *appsv1.Deployment {
	// 构建容器端口
	var containerPorts []corev1.ContainerPort
	for _, port := range params.Ports {
		containerPorts = append(containerPorts, corev1.ContainerPort{
			Name:          fmt.Sprintf("port-%d", port),
			ContainerPort: port,
			Protocol:      corev1.ProtocolTCP,
		})
	}

	return &appsv1.Deployment{
		ObjectMeta: metav1.ObjectMeta{
			Name:      params.AppName,
			Namespace: params.Namespace,
			Labels:    params.AppLabels,
		},
		Spec: appsv1.DeploymentSpec{
			Replicas: &params.Replicas,
			Selector: &metav1.LabelSelector{
				MatchLabels: map[string]string{
					"app": params.AppName,
				},
			},
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: params.AppLabels,
				},
				Spec: corev1.PodSpec{
					Containers: []corev1.Container{
						{
							Name:  params.AppName,
							Image: params.Image,
							Ports: containerPorts,
							Resources: corev1.ResourceRequirements{
								Requests: corev1.ResourceList{
									corev1.ResourceCPU:    resource.MustParse("100m"),
									corev1.ResourceMemory: resource.MustParse("128Mi"),
								},
								Limits: corev1.ResourceList{
									corev1.ResourceCPU:    resource.MustParse("500m"),
									corev1.ResourceMemory: resource.MustParse("512Mi"),
								},
							},
						},
					},
				},
			},
		},
	}
}

// generateServiceYAML 生成Service YAML，参考Python的template_svc_generate方法
func (s *KubernetesDeployService) generateServiceYAML(params *DeploymentParams, appInfo *models.AppInfo) (*corev1.Service, bool) {
	// 解析端口配置，检查是否有NodePort
	var servicePorts []corev1.ServicePort
	serviceType := corev1.ServiceTypeClusterIP
	hasNodePort := false

	if appInfo.PortSettings.Data != nil {
		var portConfig map[string]interface{}
		if err := json.Unmarshal([]byte(appInfo.PortSettings.Data.(string)), &portConfig); err == nil {
			if portsArray, ok := portConfig["ports"].([]interface{}); ok {
				for _, portData := range portsArray {
					if portMap, ok := portData.(map[string]interface{}); ok {
						port := int32(portMap["port"].(float64))
						protocol := corev1.ProtocolTCP
						if proto, exists := portMap["protocol"]; exists {
							if strings.ToUpper(proto.(string)) == "UDP" {
								protocol = corev1.ProtocolUDP
							}
						}

						portName := fmt.Sprintf("port-%d", port)
						if name, exists := portMap["name"]; exists {
							portName = name.(string)
						}

						servicePort := corev1.ServicePort{
							Name:       portName,
							Port:       port,
							TargetPort: intstr.FromInt(int(port)),
							Protocol:   protocol,
						}

						// 检查是否有NodePort配置
						if nodePort, exists := portMap["node_port"]; exists {
							nodePortNum := int32(nodePort.(float64))
							if nodePortNum > 0 {
								servicePort.NodePort = nodePortNum
								serviceType = corev1.ServiceTypeNodePort
								hasNodePort = true
							}
						}

						servicePorts = append(servicePorts, servicePort)
					}
				}
			}
		}
	}

	// 如果没有端口配置或者没有NodePort，返回false表示不需要创建Service
	if len(servicePorts) == 0 || !hasNodePort {
		s.logger.Infof("📝 端口配置中没有NodePort，跳过Service创建")
		return nil, false
	}

	service := &corev1.Service{
		ObjectMeta: metav1.ObjectMeta{
			Name:      params.AppName,
			Namespace: params.Namespace,
			Labels:    params.AppLabels,
		},
		Spec: corev1.ServiceSpec{
			Selector: map[string]string{
				"app": params.AppName,
			},
			Ports: servicePorts,
			Type:  serviceType,
		},
	}

	return service, true
}

// deployService 部署服务
func (s *KubernetesDeployService) deployService(ctx context.Context, client *kubernetes.Clientset, params *DeploymentParams) error {
	service := s.createService(params)

	// 检查Service是否已存在
	_, err := client.CoreV1().Services(params.Namespace).Get(ctx, params.AppName, metav1.GetOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			// 创建新的Service
			_, err = client.CoreV1().Services(params.Namespace).Create(ctx, service, metav1.CreateOptions{})
			if err != nil {
				return fmt.Errorf("创建Service失败: %v", err)
			}
			s.logger.Infof("成功创建Service: %s/%s", params.Namespace, params.AppName)
		} else {
			return fmt.Errorf("获取Service失败: %v", err)
		}
	} else {
		// 更新现有Service
		_, err = client.CoreV1().Services(params.Namespace).Update(ctx, service, metav1.UpdateOptions{})
		if err != nil {
			return fmt.Errorf("更新Service失败: %v", err)
		}
		s.logger.Infof("成功更新Service: %s/%s", params.Namespace, params.AppName)
	}

	return nil
}

// deployServiceWithTemplate 使用模板部署服务
func (s *KubernetesDeployService) deployServiceWithTemplate(ctx context.Context, client *kubernetes.Clientset, params *DeploymentParams, appInfo *models.AppInfo) error {
	service, shouldCreate := s.generateServiceYAML(params, appInfo)

	if !shouldCreate {
		s.logger.Infof("📝 跳过Service创建: %s/%s", params.Namespace, params.AppName)
		return nil
	}

	// 检查Service是否已存在
	_, err := client.CoreV1().Services(params.Namespace).Get(ctx, params.AppName, metav1.GetOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			// 创建新的Service
			_, err = client.CoreV1().Services(params.Namespace).Create(ctx, service, metav1.CreateOptions{})
			if err != nil {
				return fmt.Errorf("创建Service失败: %v", err)
			}
			s.logger.Infof("成功创建Service: %s/%s", params.Namespace, params.AppName)
		} else {
			return fmt.Errorf("获取Service失败: %v", err)
		}
	} else {
		// 更新现有Service
		_, err = client.CoreV1().Services(params.Namespace).Update(ctx, service, metav1.UpdateOptions{})
		if err != nil {
			return fmt.Errorf("更新Service失败: %v", err)
		}
		s.logger.Infof("成功更新Service: %s/%s", params.Namespace, params.AppName)
	}

	return nil
}

// createService 创建Service对象（保持兼容性）
func (s *KubernetesDeployService) createService(params *DeploymentParams) *corev1.Service {
	// 构建服务端口
	var servicePorts []corev1.ServicePort
	for _, port := range params.Ports {
		servicePorts = append(servicePorts, corev1.ServicePort{
			Name:       fmt.Sprintf("port-%d", port),
			Port:       port,
			TargetPort: intstr.FromInt(int(port)),
			Protocol:   corev1.ProtocolTCP,
		})
	}

	return &corev1.Service{
		ObjectMeta: metav1.ObjectMeta{
			Name:      params.AppName,
			Namespace: params.Namespace,
			Labels:    params.AppLabels,
		},
		Spec: corev1.ServiceSpec{
			Selector: map[string]string{
				"app": params.AppName,
			},
			Ports: servicePorts,
			Type:  corev1.ServiceTypeClusterIP,
		},
	}
}

// checkDeploymentStatus 检查部署状态
func (s *KubernetesDeployService) checkDeploymentStatus(ctx context.Context, client *kubernetes.Clientset, params *DeploymentParams) error {
	// 等待Deployment就绪
	timeout := time.After(5 * time.Minute)
	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-timeout:
			return fmt.Errorf("部署超时")
		case <-ticker.C:
			deployment, err := client.AppsV1().Deployments(params.Namespace).Get(ctx, params.AppName, metav1.GetOptions{})
			if err != nil {
				s.logger.Errorf("获取Deployment状态失败: %v", err)
				continue
			}

			if deployment.Status.ReadyReplicas == *deployment.Spec.Replicas {
				s.logger.Infof("Deployment已就绪: %s/%s", params.Namespace, params.AppName)
				return nil
			}

			s.logger.Infof("等待Deployment就绪: %d/%d", deployment.Status.ReadyReplicas, *deployment.Spec.Replicas)
		}
	}
}

// RollbackRequest 回滚请求结构体
type RollbackRequest struct {
	AppInfoID       uint   `json:"app_info_id" binding:"required"`   // 应用信息ID
	KubernetesID    uint   `json:"kubernetes_id" binding:"required"` // Kubernetes集群ID
	ToRevision      int64  `json:"to_revision,omitempty"`            // 回滚到的版本，为空时回滚到上一版本
	RollbackReason  int    `json:"rollback_reason"`                  // 回滚原因码
	RollbackComment string `json:"rollback_comment"`                 // 回滚备注
	BatchUUID       string `json:"batch_uuid" binding:"required"`    // 批次UUID
}

// RollbackResponse 回滚响应结构体
type RollbackResponse struct {
	JobID          uint                   `json:"job_id"`          // 回滚任务ID
	AppName        string                 `json:"app_name"`        // 应用名称
	Namespace      string                 `json:"namespace"`       // 命名空间
	FromRevision   int64                  `json:"from_revision"`   // 回滚前版本
	ToRevision     int64                  `json:"to_revision"`     // 回滚到版本
	RollbackStatus string                 `json:"rollback_status"` // 回滚状态
	DeploymentInfo map[string]interface{} `json:"deployment_info"` // 部署信息
}

// ExecuteRollback 执行版本回滚
func (s *KubernetesDeployService) ExecuteRollback(
	rollbackReq RollbackRequest,
	k8sCluster models.KubernetesCluster,
	appInfo *models.AppInfo,
	app *models.MicroApp,
	environment models.Environment,
	deployerID uint,
) (*RollbackResponse, error) {
	s.logger.Infof("开始执行版本回滚: AppInfo=%d, Cluster=%s, ToRevision=%d",
		rollbackReq.AppInfoID, k8sCluster.Name, rollbackReq.ToRevision)

	// 解密Kubernetes配置
	k8sConfig, err := s.decryptKubernetesConfig(k8sCluster.ConfigData.Data.(string))
	if err != nil {
		s.logger.Errorf("解密Kubernetes配置失败: %v", err)
		return nil, fmt.Errorf("解密Kubernetes配置失败: %v", err)
	}

	// 创建Kubernetes客户端
	k8sClient, err := s.createKubernetesClient(k8sConfig)
	if err != nil {
		s.logger.Errorf("创建Kubernetes客户端失败: %v", err)
		return nil, fmt.Errorf("创建Kubernetes客户端失败: %v", err)
	}

	// 构建部署参数以获取命名空间
	deployParams := s.buildDeploymentParams(models.DeployJob{}, appInfo, app, environment)
	ctx := context.TODO()

	// 获取当前Deployment信息
	deployment, err := k8sClient.AppsV1().Deployments(deployParams.Namespace).Get(ctx, app.Name, metav1.GetOptions{})
	if err != nil {
		s.logger.Errorf("获取Deployment失败: %v", err)
		return nil, fmt.Errorf("获取Deployment失败: %v", err)
	}

	// 获取当前版本号
	currentRevision := deployment.ObjectMeta.Annotations["deployment.kubernetes.io/revision"]
	s.logger.Infof("当前Deployment版本: %s", currentRevision)

	// 获取ReplicaSet历史
	replicaSets, err := k8sClient.AppsV1().ReplicaSets(deployParams.Namespace).List(ctx, metav1.ListOptions{
		LabelSelector: fmt.Sprintf("app=%s", app.Name),
	})
	if err != nil {
		s.logger.Errorf("获取ReplicaSet历史失败: %v", err)
		return nil, fmt.Errorf("获取ReplicaSet历史失败: %v", err)
	}

	if len(replicaSets.Items) < 2 {
		s.logger.Errorf("没有足够的历史版本进行回滚")
		return nil, fmt.Errorf("没有足够的历史版本进行回滚")
	}

	// 确定回滚目标版本
	var targetRevision int64
	if rollbackReq.ToRevision > 0 {
		targetRevision = rollbackReq.ToRevision
	} else {
		// 找到上一个版本
		targetRevision, err = s.findPreviousRevision(replicaSets.Items, currentRevision)
		if err != nil {
			return nil, fmt.Errorf("无法确定回滚目标版本: %v", err)
		}
	}

	s.logger.Infof("执行回滚: %s -> %d", currentRevision, targetRevision)

	// 创建回滚任务记录
	deployJob := models.DeployJob{
		UniqID:          fmt.Sprintf("%s-%d-%d-rollback", rollbackReq.BatchUUID, rollbackReq.AppInfoID, rollbackReq.KubernetesID),
		AppID:           fmt.Sprintf("%d", app.ID),
		AppInfoID:       rollbackReq.AppInfoID,
		Status:          3,                         // 部署中
		Image:           "",                        // 回滚时镜像由Kubernetes自动确定
		DeployType:      models.DeployTypeRollback, // 版本回退
		RollbackReason:  rollbackReq.RollbackReason,
		RollbackComment: rollbackReq.RollbackComment,
		BatchUUID:       rollbackReq.BatchUUID,
		DeployerID:      deployerID,
	}

	// 设置Kubernetes配置
	kubernetesConfig := map[string]interface{}{
		"kubernetes_id":   rollbackReq.KubernetesID,
		"namespace":       deployParams.Namespace,
		"from_revision":   currentRevision,
		"to_revision":     targetRevision,
		"rollback_reason": rollbackReq.RollbackReason,
	}

	kubernetesJSON, _ := json.Marshal(kubernetesConfig)
	deployJob.Kubernetes = string(kubernetesJSON)

	// 保存到数据库
	if err := s.db.Create(&deployJob).Error; err != nil {
		s.logger.Errorf("创建回滚任务记录失败: %v", err)
		return nil, fmt.Errorf("创建回滚任务记录失败: %v", err)
	}

	// 执行实际的回滚操作
	err = s.performKubernetesRollback(ctx, k8sClient, app.Name, deployParams.Namespace, targetRevision)
	if err != nil {
		// 更新任务状态为失败
		s.db.Model(&deployJob).Updates(map[string]interface{}{
			"status": models.StatusFailed,
		})
		return nil, fmt.Errorf("执行Kubernetes回滚失败: %v", err)
	}

	// 等待回滚完成
	err = s.waitForRollbackComplete(ctx, k8sClient, app.Name, deployParams.Namespace, targetRevision)
	if err != nil {
		// 更新任务状态为失败
		s.db.Model(&deployJob).Updates(map[string]interface{}{
			"status": models.StatusFailed,
		})
		return nil, fmt.Errorf("等待回滚完成失败: %v", err)
	}

	// 获取回滚后的镜像信息
	updatedDeployment, err := k8sClient.AppsV1().Deployments(deployParams.Namespace).Get(ctx, app.Name, metav1.GetOptions{})
	if err == nil && len(updatedDeployment.Spec.Template.Spec.Containers) > 0 {
		deployJob.Image = updatedDeployment.Spec.Template.Spec.Containers[0].Image
	}

	// 更新任务状态为成功
	s.db.Model(&deployJob).Updates(map[string]interface{}{
		"status": models.StatusSuccess,
		"image":  deployJob.Image,
	})

	// 保存回滚结果
	rollbackResult := map[string]interface{}{
		"status":        "success",
		"cluster":       k8sCluster.Name,
		"namespace":     deployParams.Namespace,
		"from_revision": currentRevision,
		"to_revision":   targetRevision,
		"image":         deployJob.Image,
		"rollback_time": time.Now(),
	}

	rollbackResultJSON, _ := json.Marshal(rollbackResult)
	deployJobResult := models.DeployJobResult{
		JobID:  deployJob.ID,
		Result: string(rollbackResultJSON),
	}

	if err := s.db.Create(&deployJobResult).Error; err != nil {
		s.logger.Errorf("保存回滚结果失败: %v", err)
	}

	s.logger.Infof("版本回滚执行成功: JobID=%d, %s -> %d", deployJob.ID, currentRevision, targetRevision)

	return &RollbackResponse{
		JobID:          deployJob.ID,
		AppName:        app.Name,
		Namespace:      deployParams.Namespace,
		FromRevision:   s.parseRevision(currentRevision),
		ToRevision:     targetRevision,
		RollbackStatus: "success",
		DeploymentInfo: rollbackResult,
	}, nil
}

// performKubernetesRollback 执行实际的Kubernetes回滚操作
func (s *KubernetesDeployService) performKubernetesRollback(ctx context.Context, client *kubernetes.Clientset, appName, namespace string, toRevision int64) error {
	s.logger.Infof("执行Kubernetes回滚: App=%s, Namespace=%s, ToRevision=%d", appName, namespace, toRevision)

	// 使用kubectl rollout undo的等效API操作
	// 通过更新Deployment的annotation来触发回滚
	deployment, err := client.AppsV1().Deployments(namespace).Get(ctx, appName, metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取Deployment失败: %v", err)
	}

	// 设置回滚annotation
	if deployment.Annotations == nil {
		deployment.Annotations = make(map[string]string)
	}

	// 使用deployment.kubernetes.io/rollback-to来指定回滚目标
	deployment.Annotations["deployment.kubernetes.io/rollback-to"] = fmt.Sprintf("%d", toRevision)

	// 更新Deployment以触发回滚
	_, err = client.AppsV1().Deployments(namespace).Update(ctx, deployment, metav1.UpdateOptions{})
	if err != nil {
		return fmt.Errorf("更新Deployment触发回滚失败: %v", err)
	}

	s.logger.Infof("已触发Kubernetes回滚: %s/%s", namespace, appName)
	return nil
}

// waitForRollbackComplete 等待回滚完成
func (s *KubernetesDeployService) waitForRollbackComplete(ctx context.Context, client *kubernetes.Clientset, appName, namespace string, toRevision int64) error {
	s.logger.Infof("等待回滚完成: %s/%s", namespace, appName)

	timeout := time.After(10 * time.Minute) // 10分钟超时
	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-timeout:
			return fmt.Errorf("回滚超时")
		case <-ticker.C:
			deployment, err := client.AppsV1().Deployments(namespace).Get(ctx, appName, metav1.GetOptions{})
			if err != nil {
				s.logger.Errorf("检查回滚状态失败: %v", err)
				continue
			}

			// 检查Deployment是否就绪
			if deployment.Status.ReadyReplicas == *deployment.Spec.Replicas &&
				deployment.Status.UpdatedReplicas == *deployment.Spec.Replicas {

				// 检查当前版本是否为目标版本
				currentRevision := deployment.ObjectMeta.Annotations["deployment.kubernetes.io/revision"]
				if s.parseRevision(currentRevision) == toRevision {
					s.logger.Infof("回滚完成: %s/%s, 当前版本: %s", namespace, appName, currentRevision)
					return nil
				}
			}

			s.logger.Infof("等待回滚完成: %d/%d副本就绪", deployment.Status.ReadyReplicas, *deployment.Spec.Replicas)
		}
	}
}

// findPreviousRevision 查找上一个版本
func (s *KubernetesDeployService) findPreviousRevision(replicaSets []appsv1.ReplicaSet, currentRevision string) (int64, error) {
	currentRev := s.parseRevision(currentRevision)
	var previousRev int64 = 0

	for _, rs := range replicaSets {
		if rs.Annotations == nil {
			continue
		}

		revisionStr, exists := rs.Annotations["deployment.kubernetes.io/revision"]
		if !exists {
			continue
		}

		revision := s.parseRevision(revisionStr)

		// 找到小于当前版本的最大版本号
		if revision < currentRev && revision > previousRev {
			previousRev = revision
		}
	}

	if previousRev == 0 {
		return 0, fmt.Errorf("没有找到可回滚的历史版本")
	}

	return previousRev, nil
}

// parseRevision 解析版本号
func (s *KubernetesDeployService) parseRevision(revisionStr string) int64 {
	if revisionStr == "" {
		return 0
	}

	revision, err := strconv.ParseInt(revisionStr, 10, 64)
	if err != nil {
		s.logger.Warnf("解析版本号失败: %s, %v", revisionStr, err)
		return 0
	}

	return revision
}

// GetRollbackHistory 获取应用的回滚历史
func (s *KubernetesDeployService) GetRollbackHistory(
	k8sCluster models.KubernetesCluster,
	appInfo *models.AppInfo,
	app *models.MicroApp,
	environment models.Environment,
) ([]map[string]interface{}, error) {
	s.logger.Infof("获取回滚历史: App=%s, Cluster=%s", app.Name, k8sCluster.Name)

	// 解密Kubernetes配置
	k8sConfig, err := s.decryptKubernetesConfig(k8sCluster.ConfigData.Data.(string))
	if err != nil {
		return nil, fmt.Errorf("解密Kubernetes配置失败: %v", err)
	}

	// 创建Kubernetes客户端
	k8sClient, err := s.createKubernetesClient(k8sConfig)
	if err != nil {
		return nil, fmt.Errorf("创建Kubernetes客户端失败: %v", err)
	}

	// 构建部署参数以获取命名空间
	deployParams := s.buildDeploymentParams(models.DeployJob{}, appInfo, app, environment)
	ctx := context.TODO()

	// 获取ReplicaSet历史
	replicaSets, err := k8sClient.AppsV1().ReplicaSets(deployParams.Namespace).List(ctx, metav1.ListOptions{
		LabelSelector: fmt.Sprintf("app=%s", app.Name),
	})
	if err != nil {
		return nil, fmt.Errorf("获取ReplicaSet历史失败: %v", err)
	}

	// 获取当前Deployment
	deployment, err := k8sClient.AppsV1().Deployments(deployParams.Namespace).Get(ctx, app.Name, metav1.GetOptions{})
	if err != nil {
		return nil, fmt.Errorf("获取Deployment失败: %v", err)
	}

	currentRevision := s.parseRevision(deployment.ObjectMeta.Annotations["deployment.kubernetes.io/revision"])

	var history []map[string]interface{}

	// 按版本号排序并构建历史记录
	for _, rs := range replicaSets.Items {
		if rs.Annotations == nil {
			continue
		}

		revisionStr, exists := rs.Annotations["deployment.kubernetes.io/revision"]
		if !exists {
			continue
		}

		revision := s.parseRevision(revisionStr)
		if revision == 0 {
			continue
		}

		// 获取镜像信息
		var image string
		if len(rs.Spec.Template.Spec.Containers) > 0 {
			image = rs.Spec.Template.Spec.Containers[0].Image
		}

		historyItem := map[string]interface{}{
			"revision":     revision,
			"image":        image,
			"created_at":   rs.CreationTimestamp.Time,
			"replicas":     rs.Status.Replicas,
			"is_current":   revision == currentRevision,
			"can_rollback": revision != currentRevision,
		}

		history = append(history, historyItem)
	}

	s.logger.Infof("获取到%d个历史版本", len(history))
	return history, nil
}

// getImageRegistry 从镜像字符串中提取镜像仓库部分
func getImageRegistry(image string) string {
	parts := strings.Split(image, "/")
	if len(parts) > 1 {
		return parts[0]
	}
	return ""
}
