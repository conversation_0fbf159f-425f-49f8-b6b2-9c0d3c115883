package services

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"

	"github.com/devops-microservices/cicd-service/models"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// AnsibleConfig Ansible配置
type AnsibleConfig struct {
	AnsiblePath  string // Ansible可执行文件路径
	PlaybookDir  string // Playbook目录
	InventoryDir string // Inventory目录
	LogDir       string // 日志目录
}

// AnsibleService 用于Ansible部署的服务
type AnsibleService struct {
	logger logrus.FieldLogger
	config *AnsibleConfig
}

// PlaybookResult Ansible Playbook执行结果
type PlaybookResult struct {
	ID         string                 `json:"id"`
	Status     string                 `json:"status"`
	StartTime  time.Time              `json:"start_time"`
	EndTime    time.Time              `json:"end_time,omitempty"`
	ExitCode   int                    `json:"exit_code"`
	Output     string                 `json:"output,omitempty"`
	Error      string                 `json:"error,omitempty"`
	ResultData map[string]interface{} `json:"result_data,omitempty"`
}

// NewAnsibleService 创建新的Ansible服务
func NewAnsibleService(logger logrus.FieldLogger, config *AnsibleConfig) *AnsibleService {
	return &AnsibleService{
		logger: logger,
		config: config,
	}
}

// DeployDocker 使用Ansible部署Docker应用
func (s *AnsibleService) DeployDocker(ctx context.Context, params *models.DockerDeploymentParams) (string, error) {
	s.logger.Infof("使用Ansible部署Docker应用: %s", params.Name)

	// 生成唯一的部署ID
	deployID := fmt.Sprintf("ansible-docker-%s-%s", params.Name, time.Now().Format("20060102-150405"))

	// 创建临时Inventory文件
	inventoryFile, err := s.createInventory(params.TargetHosts)
	if err != nil {
		return "", fmt.Errorf("创建Inventory文件失败: %v", err)
	}
	defer os.Remove(inventoryFile)

	// 创建临时变量文件
	varsFile, err := s.createVarsFile(params)
	if err != nil {
		return "", fmt.Errorf("创建变量文件失败: %v", err)
	}
	defer os.Remove(varsFile)

	// 构建Ansible命令
	playbookPath := filepath.Join(s.config.PlaybookDir, "docker-deploy.yml")
	cmd := exec.CommandContext(
		ctx,
		s.config.AnsiblePath,
		"playbook",
		playbookPath,
		"-i", inventoryFile,
		"--extra-vars", fmt.Sprintf("@%s", varsFile),
	)

	// 执行Ansible命令
	output, err := cmd.CombinedOutput()
	if err != nil {
		s.logger.Errorf("Ansible执行失败: %v\n输出: %s", err, string(output))
		return "", fmt.Errorf("Ansible执行失败: %v", err)
	}

	s.logger.Infof("Ansible执行成功: %s", string(output))
	return deployID, nil
}

// createInventory 创建临时Inventory文件
func (s *AnsibleService) createInventory(hosts []string) (string, error) {
	// 创建临时文件
	file, err := os.CreateTemp("", "ansible-inventory-*.ini")
	if err != nil {
		return "", err
	}
	defer file.Close()

	// 写入主机组
	content := "[docker_hosts]\n"
	for _, host := range hosts {
		content += host + "\n"
	}

	// 写入文件
	if _, err := file.WriteString(content); err != nil {
		return "", err
	}

	return file.Name(), nil
}

// createVarsFile 创建临时变量文件
func (s *AnsibleService) createVarsFile(params *models.DockerDeploymentParams) (string, error) {
	// 创建临时文件
	file, err := os.CreateTemp("", "ansible-vars-*.json")
	if err != nil {
		return "", err
	}
	defer file.Close()

	// 构建变量
	vars := map[string]interface{}{
		"app_name":        params.Name,
		"docker_image":    params.DockerImage,
		"env_vars":        params.Env,
		"replicas":        params.Replicas,
		"exposed_port":    params.ExposedPort,
		"harbor_username": params.HarborUsername,
		"harbor_password": params.HarborPassword,
	}

	// 序列化为JSON
	data, err := json.Marshal(vars)
	if err != nil {
		return "", err
	}

	// 写入文件
	if _, err := file.Write(data); err != nil {
		return "", err
	}

	return file.Name(), nil
}

// CancelDeployment 取消Ansible部署
func (s *AnsibleService) CancelDeployment(ctx context.Context, deployID string) error {
	// 从deployID中提取应用名称
	parts := strings.Split(deployID, "-")
	if len(parts) < 3 {
		return fmt.Errorf("无效的部署ID格式: %s", deployID)
	}

	appName := parts[2]
	s.logger.Infof("取消应用部署: %s", appName)

	// 这里应该实现停止相关Docker容器的逻辑
	// 例如通过Ansible运行停止容器的playbook

	// 构建Ansible命令
	playbookPath := filepath.Join(s.config.PlaybookDir, "docker-stop.yml")
	cmd := exec.CommandContext(
		ctx,
		s.config.AnsiblePath,
		"playbook",
		playbookPath,
		"--extra-vars", fmt.Sprintf("app_name=%s", appName),
	)

	// 执行Ansible命令
	output, err := cmd.CombinedOutput()
	if err != nil {
		s.logger.Errorf("停止应用失败: %v\n输出: %s", err, string(output))
		return fmt.Errorf("停止应用失败: %v", err)
	}

	s.logger.Infof("成功停止应用: %s", appName)
	return nil
}

// GetDeploymentStatus 获取Ansible部署状态
func (s *AnsibleService) GetDeploymentStatus(ctx context.Context, deployID string) (map[string]interface{}, error) {
	// 从deployID中提取应用名称
	parts := strings.Split(deployID, "-")
	if len(parts) < 3 {
		return nil, fmt.Errorf("无效的部署ID格式: %s", deployID)
	}

	appName := parts[2]
	s.logger.Infof("获取应用部署状态: %s", appName)

	// 这里应该实现检查Docker容器状态的逻辑
	// 例如通过Ansible运行检查容器状态的playbook

	// 返回一个示例状态
	status := map[string]interface{}{
		"id":      deployID,
		"name":    appName,
		"status":  "running", // 应该根据实际情况确定
		"type":    "ansible-docker",
		"updated": time.Now(),
	}

	return status, nil
}

// RunPlaybookParams Ansible Playbook运行参数
type RunPlaybookParams struct {
	Playbook    string                 `json:"playbook"`    // Playbook文件名
	Inventory   []string               `json:"inventory"`   // 主机列表
	ExtraVars   map[string]interface{} `json:"extra_vars"`  // 额外变量
	Tags        []string               `json:"tags"`        // 标签
	SkipTags    []string               `json:"skip_tags"`   // 跳过标签
	Limit       string                 `json:"limit"`       // 限制
	Verbose     bool                   `json:"verbose"`     // 详细输出
	CheckMode   bool                   `json:"check_mode"`  // 检查模式
	Forks       int                    `json:"forks"`       // 并发进程数
	Environment map[string]string      `json:"environment"` // 环境变量
}

// RunPlaybook 运行Ansible Playbook
func (s *AnsibleService) RunPlaybook(ctx context.Context, params *RunPlaybookParams) (*PlaybookResult, error) {
	s.logger.Infof("运行Ansible Playbook: %s", params.Playbook)

	// 生成唯一的运行ID
	runID := uuid.New().String()
	result := &PlaybookResult{
		ID:        runID,
		Status:    "running",
		StartTime: time.Now(),
	}

	// 创建临时Inventory文件
	inventoryFile, err := s.createCustomInventory(params.Inventory)
	if err != nil {
		return nil, fmt.Errorf("创建Inventory文件失败: %v", err)
	}
	defer os.Remove(inventoryFile)

	// 创建临时变量文件
	var varsFile string
	if len(params.ExtraVars) > 0 {
		varsFile, err = s.createCustomVarsFile(params.ExtraVars)
		if err != nil {
			return nil, fmt.Errorf("创建变量文件失败: %v", err)
		}
		defer os.Remove(varsFile)
	}

	// 确定Playbook文件路径
	playbookPath := params.Playbook
	if !filepath.IsAbs(playbookPath) {
		playbookPath = filepath.Join(s.config.PlaybookDir, playbookPath)
	}

	// 检查Playbook文件是否存在
	if _, err := os.Stat(playbookPath); os.IsNotExist(err) {
		return nil, fmt.Errorf("Playbook文件不存在: %s", playbookPath)
	}

	// 构建Ansible命令
	args := []string{
		"playbook",
		playbookPath,
		"-i", inventoryFile,
	}

	// 添加额外变量
	if varsFile != "" {
		args = append(args, "--extra-vars", fmt.Sprintf("@%s", varsFile))
	}

	// 添加标签
	if len(params.Tags) > 0 {
		args = append(args, "--tags", strings.Join(params.Tags, ","))
	}

	// 添加跳过标签
	if len(params.SkipTags) > 0 {
		args = append(args, "--skip-tags", strings.Join(params.SkipTags, ","))
	}

	// 添加限制
	if params.Limit != "" {
		args = append(args, "--limit", params.Limit)
	}

	// 添加详细输出
	if params.Verbose {
		args = append(args, "-v")
	}

	// 添加检查模式
	if params.CheckMode {
		args = append(args, "--check")
	}

	// 添加并发进程数
	if params.Forks > 0 {
		args = append(args, "--forks", fmt.Sprintf("%d", params.Forks))
	}

	// 创建命令
	cmd := exec.CommandContext(ctx, s.config.AnsiblePath, args...)

	// 设置环境变量
	if len(params.Environment) > 0 {
		env := os.Environ()
		for k, v := range params.Environment {
			env = append(env, fmt.Sprintf("%s=%s", k, v))
		}
		cmd.Env = env
	}

	// 创建日志文件
	logFilePath := filepath.Join(s.config.LogDir, fmt.Sprintf("ansible-%s.log", runID))
	logFile, err := os.Create(logFilePath)
	if err != nil {
		return nil, fmt.Errorf("创建日志文件失败: %v", err)
	}
	defer logFile.Close()

	// 设置输出
	cmd.Stdout = logFile
	cmd.Stderr = logFile

	// 执行命令
	s.logger.Infof("开始执行Ansible命令: %s %s", s.config.AnsiblePath, strings.Join(args, " "))
	err = cmd.Start()
	if err != nil {
		return nil, fmt.Errorf("启动Ansible命令失败: %v", err)
	}

	// 异步等待命令完成
	go func() {
		err := cmd.Wait()
		endTime := time.Now()

		// 重新打开日志文件以读取输出
		logFile, openErr := os.Open(logFilePath)
		if openErr != nil {
			s.logger.Errorf("打开日志文件失败: %v", openErr)
		} else {
			defer logFile.Close()
			output, readErr := io.ReadAll(logFile)
			if readErr != nil {
				s.logger.Errorf("读取日志文件失败: %v", readErr)
			} else {
				// 更新结果
				result.Output = string(output)
			}
		}

		// 更新结果状态
		result.EndTime = endTime
		if err != nil {
			if exitErr, ok := err.(*exec.ExitError); ok {
				result.Status = "failed"
				result.ExitCode = exitErr.ExitCode()
				result.Error = exitErr.Error()
			} else {
				result.Status = "error"
				result.Error = err.Error()
			}
		} else {
			result.Status = "success"
			result.ExitCode = 0
		}

		// 保存结果到文件
		resultFilePath := filepath.Join(s.config.LogDir, fmt.Sprintf("ansible-result-%s.json", runID))
		resultData, _ := json.MarshalIndent(result, "", "  ")
		if writeErr := os.WriteFile(resultFilePath, resultData, 0644); writeErr != nil {
			s.logger.Errorf("写入结果文件失败: %v", writeErr)
		}

		s.logger.Infof("Ansible命令执行完成: %s, 状态: %s", runID, result.Status)
	}()

	return result, nil
}

// GetPlaybookResult 获取Playbook执行结果
func (s *AnsibleService) GetPlaybookResult(ctx context.Context, runID string) (*PlaybookResult, error) {
	s.logger.Infof("获取Playbook执行结果: %s", runID)

	// 读取结果文件
	resultFilePath := filepath.Join(s.config.LogDir, fmt.Sprintf("ansible-result-%s.json", runID))
	resultData, err := os.ReadFile(resultFilePath)
	if err != nil {
		if os.IsNotExist(err) {
			// 结果文件不存在，可能正在运行或已被清理
			return &PlaybookResult{
				ID:     runID,
				Status: "unknown",
			}, nil
		}
		return nil, fmt.Errorf("读取结果文件失败: %v", err)
	}

	// 解析结果
	var result PlaybookResult
	if err := json.Unmarshal(resultData, &result); err != nil {
		return nil, fmt.Errorf("解析结果文件失败: %v", err)
	}

	return &result, nil
}

// createCustomInventory 创建自定义Inventory文件
func (s *AnsibleService) createCustomInventory(hosts []string) (string, error) {
	// 创建临时文件
	file, err := os.CreateTemp("", "ansible-inventory-*.ini")
	if err != nil {
		return "", err
	}
	defer file.Close()

	// 写入主机信息
	content := ""
	for _, host := range hosts {
		if strings.HasPrefix(host, "[") && strings.HasSuffix(host, "]") {
			// 这是一个组定义
			content += host + "\n"
		} else {
			// 这是一个主机定义
			content += host + "\n"
		}
	}

	// 如果没有定义任何组，添加一个默认组
	if !strings.Contains(content, "[") {
		content = "[all]\n" + content
	}

	// 写入文件
	if _, err := file.WriteString(content); err != nil {
		return "", err
	}

	return file.Name(), nil
}

// createCustomVarsFile 创建自定义变量文件
func (s *AnsibleService) createCustomVarsFile(vars map[string]interface{}) (string, error) {
	// 创建临时文件
	file, err := os.CreateTemp("", "ansible-vars-*.json")
	if err != nil {
		return "", err
	}
	defer file.Close()

	// 序列化为JSON
	data, err := json.Marshal(vars)
	if err != nil {
		return "", err
	}

	// 写入文件
	if _, err := file.Write(data); err != nil {
		return "", err
	}

	return file.Name(), nil
}
