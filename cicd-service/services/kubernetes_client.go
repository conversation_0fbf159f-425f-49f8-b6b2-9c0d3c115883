package services

import (
	"context"
	"fmt"
	"path/filepath"

	"github.com/sirupsen/logrus"
	v1 "k8s.io/api/core/v1"
	k8serrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
	"k8s.io/client-go/util/homedir"
)

// K8sClient 封装了与Kubernetes交互的客户端
type K8sClient struct {
	Config    *rest.Config
	ClientSet *kubernetes.Clientset
	Logger    logrus.FieldLogger
}

// NewK8sClient 创建一个新的Kubernetes客户端
func NewK8sClient(logger logrus.FieldLogger) (*K8sClient, error) {
	var config *rest.Config
	var err error

	// 尝试从服务账号获取集群内配置
	config, err = rest.InClusterConfig()
	if err != nil {
		// 如果在集群外，则尝试使用kubeconfig
		logger.Info("无法获取集群内配置，尝试使用kubeconfig")

		// 确定kubeconfig路径
		kubeconfig := filepath.Join(homedir.HomeDir(), ".kube", "config")

		// 使用kubeconfig创建配置
		config, err = clientcmd.BuildConfigFromFlags("", kubeconfig)
		if err != nil {
			return nil, fmt.Errorf("无法创建Kubernetes配置: %v", err)
		}
	}

	// 创建clientset
	clientset, err := kubernetes.NewForConfig(config)
	if err != nil {
		return nil, fmt.Errorf("无法创建Kubernetes客户端: %v", err)
	}

	return &K8sClient{
		Config:    config,
		ClientSet: clientset,
		Logger:    logger,
	}, nil
}

// IsHealthy 检查Kubernetes连接是否健康
func (c *K8sClient) IsHealthy() bool {
	// 尝试获取API版本
	_, err := c.ClientSet.Discovery().ServerVersion()
	if err != nil {
		c.Logger.Errorf("Kubernetes健康检查失败: %v", err)
		return false
	}
	return true
}

// GetNamespaces 获取所有命名空间
func (c *K8sClient) GetNamespaces() ([]string, error) {
	ctx := context.Background()
	namespaceList, err := c.ClientSet.CoreV1().Namespaces().List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("获取命名空间列表失败: %v", err)
	}

	var namespaces []string
	for _, ns := range namespaceList.Items {
		namespaces = append(namespaces, ns.Name)
	}

	return namespaces, nil
}

// CreateNamespace 创建命名空间
func (c *K8sClient) CreateNamespace(name string) error {
	ctx := context.Background()

	// 检查命名空间是否已存在
	_, err := c.ClientSet.CoreV1().Namespaces().Get(ctx, name, metav1.GetOptions{})
	if err == nil {
		// 命名空间已存在
		return nil
	}

	// 创建命名空间
	namespace := &v1.Namespace{
		ObjectMeta: metav1.ObjectMeta{
			Name: name,
		},
	}

	_, err = c.ClientSet.CoreV1().Namespaces().Create(ctx, namespace, metav1.CreateOptions{})
	if err != nil {
		return fmt.Errorf("创建命名空间失败: %v", err)
	}

	return nil
}

// GetSecret 获取Secret
func (c *K8sClient) GetSecret(ctx context.Context, name string, namespace string) (*v1.Secret, error) {
	return c.ClientSet.CoreV1().Secrets(namespace).Get(ctx, name, metav1.GetOptions{})
}

// CreateSecret 创建Secret
func (c *K8sClient) CreateSecret(ctx context.Context, name string, namespace string, data map[string][]byte) error {
	secret := &v1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Name:      name,
			Namespace: namespace,
		},
		Type: v1.SecretTypeOpaque,
		Data: data,
	}

	_, err := c.ClientSet.CoreV1().Secrets(namespace).Create(ctx, secret, metav1.CreateOptions{})
	if err != nil {
		return fmt.Errorf("创建Secret失败: %v", err)
	}

	c.Logger.Infof("成功创建Secret: %s/%s", namespace, name)
	return nil
}

// UpdateSecret 更新Secret
func (c *K8sClient) UpdateSecret(ctx context.Context, name string, namespace string, data map[string][]byte) error {
	// 先获取现有Secret
	secret, err := c.GetSecret(ctx, name, namespace)
	if err != nil {
		if k8serrors.IsNotFound(err) {
			return c.CreateSecret(ctx, name, namespace, data)
		}
		return fmt.Errorf("获取Secret失败: %v", err)
	}

	// 更新数据
	secret.Data = data

	_, err = c.ClientSet.CoreV1().Secrets(namespace).Update(ctx, secret, metav1.UpdateOptions{})
	if err != nil {
		return fmt.Errorf("更新Secret失败: %v", err)
	}

	c.Logger.Infof("成功更新Secret: %s/%s", namespace, name)
	return nil
}
