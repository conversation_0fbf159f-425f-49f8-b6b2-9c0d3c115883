package services

import (
	"context"
	"fmt"

	"github.com/sirupsen/logrus"
	tektonv1 "github.com/tektoncd/pipeline/pkg/apis/pipeline/v1"
	tektonversioned "github.com/tektoncd/pipeline/pkg/client/clientset/versioned"
	tektonv1client "github.com/tektoncd/pipeline/pkg/client/clientset/versioned/typed/pipeline/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	k8stypes "k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/rest"
)

// TektonV1ClientWrapper 包装了Tekton v1的客户端
type TektonV1ClientWrapper struct {
	client tektonv1client.TektonV1Interface
	logger logrus.FieldLogger
}

// TektonClientWrapper 定义了Tekton客户端的接口
type TektonClientWrapper interface {
	TektonV1() TektonV1InterfaceWrapper
}

// TektonV1InterfaceWrapper 定义了Tekton v1 API的接口
type TektonV1InterfaceWrapper interface {
	Pipelines(namespace string) PipelineInterface
	PipelineRuns(namespace string) PipelineRunInterface
	Tasks(namespace string) TaskInterface
	TaskRuns(namespace string) TaskRunInterface
}

// TaskInterface 定义了Task资源的操作接口
type TaskInterface interface {
	Get(ctx interface{}, name string, options interface{}) (*tektonv1.Task, error)
	List(ctx interface{}, options interface{}) (*tektonv1.TaskList, error)
	Create(ctx interface{}, task *tektonv1.Task, options interface{}) (*tektonv1.Task, error)
	Update(ctx interface{}, task *tektonv1.Task, options interface{}) (*tektonv1.Task, error)
	Delete(ctx interface{}, name string, options interface{}) error
}

// TaskRunInterface 定义了TaskRun资源的操作接口
type TaskRunInterface interface {
	Get(ctx interface{}, name string, options interface{}) (*tektonv1.TaskRun, error)
	List(ctx interface{}, options interface{}) (*tektonv1.TaskRunList, error)
	Create(ctx interface{}, taskRun *tektonv1.TaskRun, options interface{}) (*tektonv1.TaskRun, error)
	Update(ctx interface{}, taskRun *tektonv1.TaskRun, options interface{}) (*tektonv1.TaskRun, error)
	Delete(ctx interface{}, name string, options interface{}) error
}

// NewTektonClientWrapper 创建一个新的Tekton客户端
func NewTektonClientWrapper(config *rest.Config, logger logrus.FieldLogger) (*TektonClientImpl, error) {
	// 创建Tekton客户端
	tektonClientset, err := tektonversioned.NewForConfig(config)
	if err != nil {
		return nil, fmt.Errorf("无法创建Tekton客户端: %v", err)
	}

	// 创建Tekton v1客户端
	tektonV1Client := tektonClientset.TektonV1()
	tektonV1Wrapper := &TektonV1ClientWrapper{
		client: tektonV1Client,
		logger: logger,
	}

	return &TektonClientImpl{
		PipelineClient: tektonV1Wrapper,
		Logger:         logger,
	}, nil
}

// TektonClientImpl 是与Tekton交互的客户端实现
type TektonClientImpl struct {
	PipelineClient TektonClientWrapper
	Logger         logrus.FieldLogger
}

// TektonV1 实现TektonClientWrapper接口
func (t *TektonV1ClientWrapper) TektonV1() TektonV1InterfaceWrapper {
	return t
}

// Pipelines 获取Pipeline资源接口
func (t *TektonV1ClientWrapper) Pipelines(namespace string) PipelineInterface {
	return &PipelineInterfaceWrapper{
		client:    t.client.Pipelines(namespace),
		logger:    t.logger,
		namespace: namespace,
	}
}

// PipelineRuns 获取PipelineRun资源接口
func (t *TektonV1ClientWrapper) PipelineRuns(namespace string) PipelineRunInterface {
	return &PipelineRunInterfaceWrapper{
		client:    t.client.PipelineRuns(namespace),
		logger:    t.logger,
		namespace: namespace,
	}
}

// Tasks 获取Task资源接口
func (t *TektonV1ClientWrapper) Tasks(namespace string) TaskInterface {
	return &TaskInterfaceWrapper{
		client:    t.client.Tasks(namespace),
		logger:    t.logger,
		namespace: namespace,
	}
}

// TaskRuns 获取TaskRun资源接口
func (t *TektonV1ClientWrapper) TaskRuns(namespace string) TaskRunInterface {
	return &TaskRunInterfaceWrapper{
		client:    t.client.TaskRuns(namespace),
		logger:    t.logger,
		namespace: namespace,
	}
}

// PipelineInterfaceWrapper 包装了Pipeline资源的接口
type PipelineInterfaceWrapper struct {
	client    tektonv1client.PipelineInterface
	logger    logrus.FieldLogger
	namespace string
}

// Get 获取Pipeline资源
func (p *PipelineInterfaceWrapper) Get(ctx context.Context, name string, options metav1.GetOptions) (*tektonv1.Pipeline, error) {
	return p.client.Get(ctx, name, options)
}

// List 列出Pipeline资源
func (p *PipelineInterfaceWrapper) List(ctx context.Context, options metav1.ListOptions) (*tektonv1.PipelineList, error) {
	return p.client.List(ctx, options)
}

// Create 创建Pipeline资源
func (p *PipelineInterfaceWrapper) Create(ctx context.Context, pipeline *tektonv1.Pipeline, options metav1.CreateOptions) (*tektonv1.Pipeline, error) {
	return p.client.Create(ctx, pipeline, options)
}

// Update 更新Pipeline资源
func (p *PipelineInterfaceWrapper) Update(ctx context.Context, pipeline *tektonv1.Pipeline, options metav1.UpdateOptions) (*tektonv1.Pipeline, error) {
	return p.client.Update(ctx, pipeline, options)
}

// Delete 删除Pipeline资源
func (p *PipelineInterfaceWrapper) Delete(ctx context.Context, name string, options metav1.DeleteOptions) error {
	return p.client.Delete(ctx, name, options)
}

// PipelineRunInterfaceWrapper 包装了PipelineRun资源的接口
type PipelineRunInterfaceWrapper struct {
	client    tektonv1client.PipelineRunInterface
	logger    logrus.FieldLogger
	namespace string
}

// Get 获取PipelineRun资源
func (p *PipelineRunInterfaceWrapper) Get(ctx context.Context, name string, options metav1.GetOptions) (*tektonv1.PipelineRun, error) {
	return p.client.Get(ctx, name, options)
}

// List 列出PipelineRun资源
func (p *PipelineRunInterfaceWrapper) List(ctx context.Context, options metav1.ListOptions) (*tektonv1.PipelineRunList, error) {
	return p.client.List(ctx, options)
}

// Create 创建PipelineRun资源
func (p *PipelineRunInterfaceWrapper) Create(ctx context.Context, pipelineRun *tektonv1.PipelineRun, options metav1.CreateOptions) (*tektonv1.PipelineRun, error) {
	return p.client.Create(ctx, pipelineRun, options)
}

// Update 更新PipelineRun资源
func (p *PipelineRunInterfaceWrapper) Update(ctx context.Context, pipelineRun *tektonv1.PipelineRun, options metav1.UpdateOptions) (*tektonv1.PipelineRun, error) {
	return p.client.Update(ctx, pipelineRun, options)
}

// Delete 删除PipelineRun资源
func (p *PipelineRunInterfaceWrapper) Delete(ctx context.Context, name string, options metav1.DeleteOptions) error {
	return p.client.Delete(ctx, name, options)
}

// Patch 修补PipelineRun资源
func (p *PipelineRunInterfaceWrapper) Patch(ctx context.Context, name string, pt k8stypes.PatchType, data []byte, options metav1.PatchOptions) (*tektonv1.PipelineRun, error) {
	return p.client.Patch(ctx, name, pt, data, options)
}

// TaskInterfaceWrapper 包装了Task资源的接口
type TaskInterfaceWrapper struct {
	client    tektonv1client.TaskInterface
	logger    logrus.FieldLogger
	namespace string
}

// Get 获取Task资源
func (t *TaskInterfaceWrapper) Get(ctx interface{}, name string, options interface{}) (*tektonv1.Task, error) {
	return t.client.Get(ctx.(context.Context), name, options.(metav1.GetOptions))
}

// List 列出Task资源
func (t *TaskInterfaceWrapper) List(ctx interface{}, options interface{}) (*tektonv1.TaskList, error) {
	return t.client.List(ctx.(context.Context), options.(metav1.ListOptions))
}

// Create 创建Task资源
func (t *TaskInterfaceWrapper) Create(ctx interface{}, task *tektonv1.Task, options interface{}) (*tektonv1.Task, error) {
	return t.client.Create(ctx.(context.Context), task, options.(metav1.CreateOptions))
}

// Update 更新Task资源
func (t *TaskInterfaceWrapper) Update(ctx interface{}, task *tektonv1.Task, options interface{}) (*tektonv1.Task, error) {
	return t.client.Update(ctx.(context.Context), task, options.(metav1.UpdateOptions))
}

// Delete 删除Task资源
func (t *TaskInterfaceWrapper) Delete(ctx interface{}, name string, options interface{}) error {
	return t.client.Delete(ctx.(context.Context), name, options.(metav1.DeleteOptions))
}

// TaskRunInterfaceWrapper 包装了TaskRun资源的接口
type TaskRunInterfaceWrapper struct {
	client    tektonv1client.TaskRunInterface
	logger    logrus.FieldLogger
	namespace string
}

// Get 获取TaskRun资源
func (t *TaskRunInterfaceWrapper) Get(ctx interface{}, name string, options interface{}) (*tektonv1.TaskRun, error) {
	return t.client.Get(ctx.(context.Context), name, options.(metav1.GetOptions))
}

// List 列出TaskRun资源
func (t *TaskRunInterfaceWrapper) List(ctx interface{}, options interface{}) (*tektonv1.TaskRunList, error) {
	return t.client.List(ctx.(context.Context), options.(metav1.ListOptions))
}

// Create 创建TaskRun资源
func (t *TaskRunInterfaceWrapper) Create(ctx interface{}, taskRun *tektonv1.TaskRun, options interface{}) (*tektonv1.TaskRun, error) {
	return t.client.Create(ctx.(context.Context), taskRun, options.(metav1.CreateOptions))
}

// Update 更新TaskRun资源
func (t *TaskRunInterfaceWrapper) Update(ctx interface{}, taskRun *tektonv1.TaskRun, options interface{}) (*tektonv1.TaskRun, error) {
	return t.client.Update(ctx.(context.Context), taskRun, options.(metav1.UpdateOptions))
}

// Delete 删除TaskRun资源
func (t *TaskRunInterfaceWrapper) Delete(ctx interface{}, name string, options interface{}) error {
	return t.client.Delete(ctx.(context.Context), name, options.(metav1.DeleteOptions))
}
