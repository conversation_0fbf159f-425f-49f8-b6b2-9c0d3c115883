package services

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
)

// checkHarborProject 检查Harbor项目是否存在
func (s *CICDService) checkHarborProject(ctx context.Context, projectName string) (bool, error) {
	// 构建Harbor API URL
	apiURL := fmt.Sprintf("%s/api/v2.0/projects/%s", strings.TrimSuffix(s.harborURL, "/"), projectName)

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, "GET", apiURL, nil)
	if err != nil {
		return false, fmt.Errorf("创建HTTP请求失败: %v", err)
	}

	// 设置基本认证
	req.SetBasicAuth(s.harborUsername, s.harborPassword)
	req.Header.Set("Accept", "application/json")

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return false, fmt.Errorf("发送HTTP请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode == 200 {
		s.logger.Infof("Harbor项目 %s 已存在", projectName)
		return true, nil
	} else if resp.StatusCode == 404 {
		s.logger.Infof("Harbor项目 %s 不存在", projectName)
		return false, nil
	} else {
		body, _ := io.ReadAll(resp.Body)
		return false, fmt.Errorf("检查Harbor项目失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}
}

// createHarborProject 创建Harbor项目
func (s *CICDService) createHarborProject(ctx context.Context, projectName string) error {
	// 构建Harbor API URL
	apiURL := fmt.Sprintf("%s/api/v2.0/projects", strings.TrimSuffix(s.harborURL, "/"))

	// 构建请求体
	requestBody := map[string]interface{}{
		"project_name": projectName,
		"public":       false,
		"metadata": map[string]string{
			"public": "false",
		},
	}

	bodyJSON, err := json.Marshal(requestBody)
	if err != nil {
		return fmt.Errorf("序列化请求体失败: %v", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, "POST", apiURL, bytes.NewBuffer(bodyJSON))
	if err != nil {
		return fmt.Errorf("创建HTTP请求失败: %v", err)
	}

	// 设置请求头
	req.SetBasicAuth(s.harborUsername, s.harborPassword)
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("发送HTTP请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 检查响应状态码
	body, _ := io.ReadAll(resp.Body)
	if resp.StatusCode == 201 || resp.StatusCode == 200 {
		s.logger.Infof("成功创建Harbor项目: %s", projectName)
		return nil
	} else if resp.StatusCode == 409 {
		// 项目已存在，不算错误
		s.logger.Infof("Harbor项目 %s 已存在（409冲突）", projectName)
		return nil
	} else {
		return fmt.Errorf("创建Harbor项目失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}
}

// ensureHarborSecret 确保Harbor登录Secret存在
func (s *KubernetesDeployService) ensureHarborSecret(ctx context.Context, client *kubernetes.Clientset, namespace string) error {
	secretName := "loginharbor"

	s.logger.Infof("🔍 检查Harbor Secret: %s/%s", namespace, secretName)

	// 检查Secret是否已存在
	_, err := client.CoreV1().Secrets(namespace).Get(ctx, secretName, metav1.GetOptions{})
	if err == nil {
		s.logger.Infof("✅ Harbor Secret已存在: %s/%s", namespace, secretName)
		return nil
	}

	if !errors.IsNotFound(err) {
		return fmt.Errorf("检查Harbor Secret失败: %v", err)
	}

	s.logger.Infof("🔧 创建Harbor Secret: %s/%s", namespace, secretName)

	// 构建Docker配置
	harborURL := strings.TrimPrefix(s.config.Harbor.URL, "http://")
	harborURL = strings.TrimPrefix(harborURL, "https://")
	harborURL = strings.TrimSuffix(harborURL, "/")

	dockerConfig := map[string]interface{}{
		"auths": map[string]interface{}{
			harborURL: map[string]interface{}{
				"username": s.config.Harbor.PullUsername,
				"password": s.config.Harbor.PullPassword,
				"auth":     base64.StdEncoding.EncodeToString([]byte(fmt.Sprintf("%s:%s", s.config.Harbor.PullUsername, s.config.Harbor.PullPassword))),
			},
		},
	}

	dockerConfigJSON, err := json.Marshal(dockerConfig)
	if err != nil {
		return fmt.Errorf("构建Docker配置失败: %v", err)
	}

	// 创建Secret
	secret := &corev1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Name:      secretName,
			Namespace: namespace,
		},
		Type: corev1.SecretTypeDockerConfigJson,
		Data: map[string][]byte{
			".dockerconfigjson": dockerConfigJSON,
		},
	}

	_, err = client.CoreV1().Secrets(namespace).Create(ctx, secret, metav1.CreateOptions{})
	if err != nil {
		return fmt.Errorf("创建Harbor Secret失败: %v", err)
	}

	s.logger.Infof("✅ 成功创建Harbor Secret: %s/%s", namespace, secretName)
	return nil
}
