package services

import (
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"path/filepath"

	"github.com/devops-microservices/cicd-service/models"
	"github.com/sirupsen/logrus"
	tektonv1 "github.com/tektoncd/pipeline/pkg/apis/pipeline/v1"
	"gorm.io/gorm"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/serializer"
	"sigs.k8s.io/yaml"
)

// PipelineService 提供Pipeline相关服务
type PipelineService struct {
	db     *gorm.DB
	logger logrus.FieldLogger
}

// NewPipelineService 创建Pipeline服务
func NewPipelineService(db *gorm.DB, logger logrus.FieldLogger) *PipelineService {
	return &PipelineService{
		db:     db,
		logger: logger,
	}
}

// YAMLToPipelineDict 将YAML格式的Pipeline转换为字典
func (p *PipelineService) YAMLToPipelineDict(yamlContent []byte) (map[string]interface{}, error) {
	var pipelineDict map[string]interface{}

	// 将YAML转换为字典
	if err := yaml.Unmarshal(yamlContent, &pipelineDict); err != nil {
		return nil, fmt.Errorf("解析YAML失败: %v", err)
	}

	return pipelineDict, nil
}

// PipelineDictToYAML 将字典格式的Pipeline转换为YAML
func (p *PipelineService) PipelineDictToYAML(pipelineDict map[string]interface{}) ([]byte, error) {
	yamlContent, err := yaml.Marshal(pipelineDict)
	if err != nil {
		return nil, fmt.Errorf("转换为YAML失败: %v", err)
	}

	return yamlContent, nil
}

// LoadBuildPipelineTemplate 加载默认的构建流水线模板
func (p *PipelineService) LoadBuildPipelineTemplate() (map[string]interface{}, error) {
	// 读取build-pipeline.yaml文件
	yamlPath := filepath.Join("k8s", "tekton", "build-pipeline.yaml")
	yamlContent, err := ioutil.ReadFile(yamlPath)
	if err != nil {
		p.logger.Errorf("读取build-pipeline.yaml失败: %v", err)
		return nil, fmt.Errorf("读取Pipeline模板文件失败: %v", err)
	}

	return p.YAMLToPipelineDict(yamlContent)
}

// ParsePipelineToTekton 将Pipeline字典转换为Tekton Pipeline对象
func (p *PipelineService) ParsePipelineToTekton(pipelineDict map[string]interface{}) (*tektonv1.Pipeline, error) {
	// 将字典转换回YAML
	yamlContent, err := p.PipelineDictToYAML(pipelineDict)
	if err != nil {
		return nil, fmt.Errorf("转换为YAML失败: %v", err)
	}

	// 创建Kubernetes运行时scheme
	scheme := runtime.NewScheme()
	tektonv1.AddToScheme(scheme)

	// 创建解码器
	codecs := serializer.NewCodecFactory(scheme)
	deserializer := codecs.UniversalDeserializer()

	// 解析YAML为Tekton Pipeline对象
	obj, _, err := deserializer.Decode(yamlContent, nil, nil)
	if err != nil {
		return nil, fmt.Errorf("解析Tekton Pipeline失败: %v", err)
	}

	pipeline, ok := obj.(*tektonv1.Pipeline)
	if !ok {
		return nil, fmt.Errorf("对象类型不是Pipeline")
	}

	return pipeline, nil
}

// SavePipelineToApp 将Pipeline配置保存到MicroApp
func (p *PipelineService) SavePipelineToApp(ctx context.Context, appID uint, pipelineDict map[string]interface{}) error {
	// 将Pipeline字典转换为JSON字符串
	pipelineJSON, err := json.Marshal(pipelineDict)
	if err != nil {
		return fmt.Errorf("序列化Pipeline配置失败: %v", err)
	}

	// 更新MicroApp的pipeline_dl字段
	result := p.db.Model(&models.MicroApp{}).Where("id = ?", appID).Update("pipeline_dl", string(pipelineJSON))
	if result.Error != nil {
		return fmt.Errorf("保存Pipeline配置失败: %v", result.Error)
	}

	p.logger.Infof("成功保存Pipeline配置到应用ID: %d", appID)
	return nil
}

// GetPipelineConfigByID 根据ID获取Pipeline配置
func (p *PipelineService) GetPipelineConfigByID(pipelineID uint) (*models.Pipeline, error) {
	var pipeline models.Pipeline
	if err := p.db.First(&pipeline, pipelineID).Error; err != nil {
		return nil, fmt.Errorf("查询Pipeline失败: %v", err)
	}

	return &pipeline, nil
}

// GetPipelineConfig 获取Pipeline配置
func (p *PipelineService) GetPipelineConfig(pipelineName string) (*models.Pipeline, error) {
	var pipeline models.Pipeline
	if err := p.db.First(&pipeline, "name = ?", pipelineName).Error; err != nil {
		return nil, fmt.Errorf("查询Pipeline失败: %v", err)
	}

	return &pipeline, nil
}

// GetAppPipelineConfig 获取应用的Pipeline配置
func (p *PipelineService) GetAppPipelineConfig(ctx context.Context, appID uint) (map[string]interface{}, error) {
	var app models.MicroApp
	if err := p.db.First(&app, appID).Error; err != nil {
		return nil, fmt.Errorf("查询应用失败: %v", err)
	}

	// 如果pipeline_dl为空，返回默认模板
	if app.PipelineID == 0 {
		p.logger.Infof("应用ID: %d 没有自定义Pipeline配置，加载默认模板", appID)
		return p.LoadBuildPipelineTemplate()
	}

	pipeline, err := p.GetPipelineConfigByID(uint(app.PipelineID))
	if err != nil {
		return nil, fmt.Errorf("查询Pipeline失败: %v", err)
	}

	pipelineDict, err := p.YAMLToPipelineDict([]byte(pipeline.PipelineConfig.Data.(string)))

	if err != nil {
		p.logger.Errorf("解析应用Pipeline配置失败: %v", err)
		// 如果解析失败，回退到默认模板
		return p.LoadBuildPipelineTemplate()
	}

	return pipelineDict, nil
}

// CreatePipelineRunTemplate 根据Pipeline配置创建PipelineRun模板
func (p *PipelineService) CreatePipelineRunTemplate(pipelineName string, params []tektonv1.Param) map[string]interface{} {
	pipelineRunTemplate := map[string]interface{}{
		"apiVersion": "tekton.dev/v1",
		"kind":       "PipelineRun",
		"metadata": map[string]interface{}{
			"generateName": fmt.Sprintf("build-%s-", pipelineName),
			"namespace":    "default",
		},
		"spec": map[string]interface{}{
			"pipelineRef": map[string]interface{}{
				"name": pipelineName,
			},
			"params": params,
			"workspaces": []map[string]interface{}{
				{
					"name": "shared-workspace",
					"persistentVolumeClaim": map[string]interface{}{
						"claimName": "tekton-workspace-pvc",
					},
				},
				{
					"name": "docker-credentials",
					"secret": map[string]interface{}{
						"secretName": "docker-config",
					},
				},
				{
					"name": "gitlab-credentials",
					"secret": map[string]interface{}{
						"secretName": "gitlab-auth",
					},
				},
			},
			"podTemplate": map[string]interface{}{
				"hostAliases": []map[string]interface{}{
					{
						"ip": "************",
						"hostnames": []string{
							"harbor.fundpark.com",
						},
					},
				},
			},
		},
	}

	return pipelineRunTemplate
}

// InitializeDefaultPipeline 为应用初始化默认Pipeline配置
func (p *PipelineService) InitializeDefaultPipeline(ctx context.Context, appID uint) error {
	// 加载默认模板
	defaultPipeline, err := p.LoadBuildPipelineTemplate()
	if err != nil {
		return fmt.Errorf("加载默认Pipeline模板失败: %v", err)
	}

	// 保存到应用
	return p.SavePipelineToApp(ctx, appID, defaultPipeline)
}

// CreatePipelineRequest 创建流水线请求结构
type CreatePipelineRequest struct {
	AppID        string `json:"app_id" binding:"required"`
	PipelineYAML string `json:"pipeline_yaml" binding:"required"`
}

// CreatePipeline 创建流水线
// 接收pipeline yaml, 转成dict保存到microapp的pipeline_dl字段
func (s *CICDService) CreatePipeline(ctx context.Context, req CreatePipelineRequest) (string, error) {
	// if req.PipelineYAML == "" {
	// 	return "", fmt.Errorf("pipeline_yaml不能为空")
	// }

	// if req.AppID == "" {
	// 	return "", fmt.Errorf("app_id不能为空")
	// }

	// // 正确处理strconv.Atoi的两个返回值
	// appIDInt, err := strconv.Atoi(req.AppID)
	// if err != nil {
	// 	return "", fmt.Errorf("无效的appID: %v", err)
	// }

	// // 获取应用信息
	// app, err := s.GetApp(ctx, appIDInt)
	// if err != nil {
	// 	s.logger.Errorf("获取应用失败: AppID=%s, Error=%v", req.AppID, err)
	// 	return "", fmt.Errorf("获取应用失败: %v", err)
	// }

	// // 验证YAML格式并转换为字典
	// pipelineDict, err := s.pipelineService.YAMLToPipelineDict([]byte(req.PipelineYAML))
	// if err != nil {
	// 	s.logger.Errorf("解析Pipeline YAML失败: AppID=%s, Error=%v", req.AppID, err)
	// 	return "", fmt.Errorf("解析Pipeline YAML失败: %v", err)
	// }

	// // 将pipeline字典序列化为JSON字符串保存到PipelineDL字段
	// pipelineDictJSON, err := json.Marshal(pipelineDict)
	// if err != nil {
	// 	s.logger.Errorf("序列化pipeline字典失败: AppID=%s, Error=%v", req.AppID, err)
	// 	return "", fmt.Errorf("序列化pipeline字典失败: %v", err)
	// }

	// // 更新应用的Pipeline配置
	// app.PipelineDL = string(pipelineDictJSON)
	// if err := s.db.Save(app).Error; err != nil {
	// 	s.logger.Errorf("保存应用pipeline失败: AppID=%s, Error=%v", req.AppID, err)
	// 	return "", fmt.Errorf("保存应用pipeline失败: %v", err)
	// }

	// s.logger.Infof("成功保存应用 ID:%d 的Pipeline配置，YAML长度: %d字节", appIDInt, len(req.PipelineYAML))
	return "success", nil
}

// CreatePipelineFromContext 从上下文创建流水线 (兼容旧的调用方式)
func (s *CICDService) CreatePipelineFromContext(ctx context.Context, appID string) (string, error) {
	pipelineYaml := ctx.Value("pipeline_yaml")
	if pipelineYaml == nil {
		return "", fmt.Errorf("pipeline_yaml不能为空")
	}

	// 类型断言将interface{}转换为string
	pipelineYamlStr, ok := pipelineYaml.(string)
	if !ok {
		return "", fmt.Errorf("pipeline_yaml类型错误，期望string类型")
	}

	req := CreatePipelineRequest{
		AppID:        appID,
		PipelineYAML: pipelineYamlStr,
	}

	return s.CreatePipeline(ctx, req)
}

// CreateTektonPipelineFromConfig 从Pipeline配置创建Tekton Pipeline对象
func (s *CICDService) CreateTektonPipelineFromConfig(pipelineConfig map[string]interface{}, pipelineName string, namespace string) (*tektonv1.Pipeline, error) {
	if s.logger != nil {
		s.logger.Infof("从配置创建Tekton Pipeline: %s", pipelineName)
	}

	// 检查Pipeline配置的基本字段
	spec, ok := pipelineConfig["spec"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("Pipeline配置中缺少spec字段或类型错误")
	}

	// 创建Pipeline对象
	tektonPipeline := &tektonv1.Pipeline{
		ObjectMeta: metav1.ObjectMeta{
			Name:      pipelineName,
			Namespace: namespace,
		},
		Spec: tektonv1.PipelineSpec{},
	}

	// 处理description
	if description, ok := spec["description"].(string); ok {
		tektonPipeline.Spec.Description = description
	}

	// 处理params
	if paramsInterface, ok := spec["params"].([]interface{}); ok {
		params, err := s.convertParams(paramsInterface)
		if err != nil {
			return nil, fmt.Errorf("转换参数失败: %v", err)
		}
		tektonPipeline.Spec.Params = params
	}

	// 处理tasks
	if tasksInterface, ok := spec["tasks"].([]interface{}); ok {
		tasks, err := s.convertTasks(tasksInterface)
		if err != nil {
			return nil, fmt.Errorf("转换任务失败: %v", err)
		}
		tektonPipeline.Spec.Tasks = tasks
	}

	// 处理workspaces
	if workspacesInterface, ok := spec["workspaces"].([]interface{}); ok {
		workspaces, err := s.convertWorkspaces(workspacesInterface)
		if err != nil {
			return nil, fmt.Errorf("转换工作空间失败: %v", err)
		}
		tektonPipeline.Spec.Workspaces = workspaces
	}

	if s.logger != nil {
		s.logger.Infof("成功创建Tekton Pipeline对象: %s", pipelineName)
	}
	return tektonPipeline, nil
}

// convertParams 转换参数列表
func (s *CICDService) convertParams(paramsInterface []interface{}) ([]tektonv1.ParamSpec, error) {
	var params []tektonv1.ParamSpec

	for _, paramInterface := range paramsInterface {
		paramMap, ok := paramInterface.(map[string]interface{})
		if !ok {
			continue
		}

		param := tektonv1.ParamSpec{}

		if name, ok := paramMap["name"].(string); ok {
			param.Name = name
		}

		if paramType, ok := paramMap["type"].(string); ok {
			param.Type = tektonv1.ParamType(paramType)
		}

		if description, ok := paramMap["description"].(string); ok {
			param.Description = description
		}

		if defaultValue, ok := paramMap["default"]; ok {
			if defaultStr, ok := defaultValue.(string); ok {
				param.Default = &tektonv1.ParamValue{
					Type:      tektonv1.ParamTypeString,
					StringVal: defaultStr,
				}
			} else if defaultArray, ok := defaultValue.([]interface{}); ok {
				var arrayVals []string
				for _, val := range defaultArray {
					if strVal, ok := val.(string); ok {
						arrayVals = append(arrayVals, strVal)
					}
				}
				param.Default = &tektonv1.ParamValue{
					Type:     tektonv1.ParamTypeArray,
					ArrayVal: arrayVals,
				}
			}
		}

		params = append(params, param)
	}

	return params, nil
}

// convertTasks 转换任务列表
func (s *CICDService) convertTasks(tasksInterface []interface{}) ([]tektonv1.PipelineTask, error) {
	var tasks []tektonv1.PipelineTask

	for _, taskInterface := range tasksInterface {
		taskMap, ok := taskInterface.(map[string]interface{})
		if !ok {
			continue
		}

		task := tektonv1.PipelineTask{}

		if name, ok := taskMap["name"].(string); ok {
			task.Name = name
		}

		// 处理taskRef
		if taskRefInterface, ok := taskMap["taskRef"].(map[string]interface{}); ok {
			if taskRefName, ok := taskRefInterface["name"].(string); ok {
				task.TaskRef = &tektonv1.TaskRef{
					Name: taskRefName,
				}
			}
		}

		// 处理params
		if paramsInterface, ok := taskMap["params"].([]interface{}); ok {
			taskParams, err := s.convertTaskParams(paramsInterface)
			if err != nil {
				return nil, fmt.Errorf("转换任务参数失败: %v", err)
			}
			task.Params = taskParams
		}

		// 处理workspaces
		if workspacesInterface, ok := taskMap["workspaces"].([]interface{}); ok {
			workspaceBindings, err := s.convertWorkspaceBindings(workspacesInterface)
			if err != nil {
				return nil, fmt.Errorf("转换任务工作空间失败: %v", err)
			}
			task.Workspaces = workspaceBindings
		}

		// 处理runAfter
		if runAfterInterface, ok := taskMap["runAfter"].([]interface{}); ok {
			var runAfter []string
			for _, after := range runAfterInterface {
				if afterStr, ok := after.(string); ok {
					runAfter = append(runAfter, afterStr)
				}
			}
			task.RunAfter = runAfter
		}

		tasks = append(tasks, task)
	}

	return tasks, nil
}

// convertTaskParams 转换任务参数
func (s *CICDService) convertTaskParams(paramsInterface []interface{}) ([]tektonv1.Param, error) {
	var params []tektonv1.Param

	for _, paramInterface := range paramsInterface {
		paramMap, ok := paramInterface.(map[string]interface{})
		if !ok {
			continue
		}

		param := tektonv1.Param{}

		if name, ok := paramMap["name"].(string); ok {
			param.Name = name
		}

		if value, ok := paramMap["value"]; ok {
			if valueStr, ok := value.(string); ok {
				param.Value = tektonv1.ParamValue{
					Type:      tektonv1.ParamTypeString,
					StringVal: valueStr,
				}
			} else if valueArray, ok := value.([]interface{}); ok {
				var arrayVals []string
				for _, val := range valueArray {
					if strVal, ok := val.(string); ok {
						arrayVals = append(arrayVals, strVal)
					}
				}
				param.Value = tektonv1.ParamValue{
					Type:     tektonv1.ParamTypeArray,
					ArrayVal: arrayVals,
				}
			}
		}

		params = append(params, param)
	}

	return params, nil
}

// convertWorkspaceBindings 转换工作空间绑定
func (s *CICDService) convertWorkspaceBindings(workspacesInterface []interface{}) ([]tektonv1.WorkspacePipelineTaskBinding, error) {
	var workspaces []tektonv1.WorkspacePipelineTaskBinding

	for _, workspaceInterface := range workspacesInterface {
		workspaceMap, ok := workspaceInterface.(map[string]interface{})
		if !ok {
			continue
		}

		workspace := tektonv1.WorkspacePipelineTaskBinding{}

		if name, ok := workspaceMap["name"].(string); ok {
			workspace.Name = name
		}

		if workspaceName, ok := workspaceMap["workspace"].(string); ok {
			workspace.Workspace = workspaceName
		}

		workspaces = append(workspaces, workspace)
	}

	return workspaces, nil
}

// convertWorkspaces 转换工作空间声明
func (s *CICDService) convertWorkspaces(workspacesInterface []interface{}) ([]tektonv1.PipelineWorkspaceDeclaration, error) {
	var workspaces []tektonv1.PipelineWorkspaceDeclaration

	for _, workspaceInterface := range workspacesInterface {
		workspaceMap, ok := workspaceInterface.(map[string]interface{})
		if !ok {
			continue
		}

		workspace := tektonv1.PipelineWorkspaceDeclaration{}

		if name, ok := workspaceMap["name"].(string); ok {
			workspace.Name = name
		}

		if description, ok := workspaceMap["description"].(string); ok {
			workspace.Description = description
		}

		// 处理optional字段
		if optional, ok := workspaceMap["optional"].(bool); ok {
			workspace.Optional = optional
		}

		workspaces = append(workspaces, workspace)
	}

	return workspaces, nil
}
