package services

import (
	"context"

	tektonv1 "github.com/tektoncd/pipeline/pkg/apis/pipeline/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	k8stypes "k8s.io/apimachinery/pkg/types"
)

// TektonClient 用于与Tekton交互的客户端接口
type TektonClient struct {
	PipelineClient TektonPipelineClient
}

// TektonPipelineClient 定义了Tekton Pipeline客户端应该提供的方法
type TektonPipelineClient interface {
	TektonV1() TektonV1Interface
}

// TektonV1Interface 定义了Tekton v1 API接口
type TektonV1Interface interface {
	PipelineRuns(namespace string) PipelineRunInterface
	Pipelines(namespace string) PipelineInterface
}

// PipelineRunInterface 定义了PipelineRun资源的操作接口
type PipelineRunInterface interface {
	Create(ctx context.Context, pipelineRun *tektonv1.PipelineRun, opts metav1.CreateOptions) (*tektonv1.PipelineRun, error)
	Get(ctx context.Context, name string, opts metav1.GetOptions) (*tektonv1.PipelineRun, error)
	List(ctx context.Context, opts metav1.ListOptions) (*tektonv1.PipelineRunList, error)
	Patch(ctx context.Context, name string, pt k8stypes.PatchType, data []byte, opts metav1.PatchOptions) (*tektonv1.PipelineRun, error)
}

// PipelineInterface 定义了Pipeline资源的操作接口
type PipelineInterface interface {
	Create(ctx context.Context, pipeline *tektonv1.Pipeline, opts metav1.CreateOptions) (*tektonv1.Pipeline, error)
	Get(ctx context.Context, name string, opts metav1.GetOptions) (*tektonv1.Pipeline, error)
	Update(ctx context.Context, pipeline *tektonv1.Pipeline, opts metav1.UpdateOptions) (*tektonv1.Pipeline, error)
}

// KubernetesClient 用于与Kubernetes交互的客户端接口
type KubernetesClient struct {
	ClientSet KubernetesClientSet
}

// KubernetesClientSet 定义了Kubernetes客户端集合
type KubernetesClientSet interface {
	CoreV1() CoreV1Interface
	AppsV1() AppsV1Interface
}

// CoreV1Interface 定义了Kubernetes Core v1 API接口
type CoreV1Interface interface {
	Pods(namespace string) PodInterface
	Secrets(namespace string) SecretInterface
	ServiceAccounts(namespace string) ServiceAccountInterface
}

// AppsV1Interface 定义了Kubernetes Apps v1 API接口
type AppsV1Interface interface {
	Deployments(namespace string) DeploymentInterface
	ReplicaSets(namespace string) ReplicaSetInterface
}

// PodInterface 定义了Pod资源的操作接口
type PodInterface interface {
	List(ctx context.Context, opts metav1.ListOptions) (interface{}, error)
	GetLogs(name string, opts interface{}) interface{}
}

// SecretInterface 定义了Secret资源的操作接口
type SecretInterface interface {
	Create(ctx context.Context, secret interface{}, opts metav1.CreateOptions) (interface{}, error)
	Get(ctx context.Context, name string, opts metav1.GetOptions) (interface{}, error)
	Update(ctx context.Context, secret interface{}, opts metav1.UpdateOptions) (interface{}, error)
}

// ServiceAccountInterface 定义了ServiceAccount资源的操作接口
type ServiceAccountInterface interface {
	Get(ctx context.Context, name string, opts metav1.GetOptions) (interface{}, error)
}

// DeploymentInterface 定义了Deployment资源的操作接口
type DeploymentInterface interface {
	Create(ctx context.Context, deployment interface{}, opts metav1.CreateOptions) (interface{}, error)
	Get(ctx context.Context, name string, opts metav1.GetOptions) (interface{}, error)
	Update(ctx context.Context, deployment interface{}, opts metav1.UpdateOptions) (interface{}, error)
	Delete(ctx context.Context, name string, opts metav1.DeleteOptions) error
	Patch(ctx context.Context, name string, pt k8stypes.PatchType, data []byte, opts metav1.PatchOptions) (interface{}, error)
}

// ReplicaSetInterface 定义了ReplicaSet资源的操作接口
type ReplicaSetInterface interface {
	List(ctx context.Context, opts metav1.ListOptions) (interface{}, error)
}

// NewTektonClient 创建新的Tekton客户端
func NewTektonClient(pipelineClient TektonPipelineClient) *TektonClient {
	return &TektonClient{
		PipelineClient: pipelineClient,
	}
}

// NewKubernetesClient 创建新的Kubernetes客户端
func NewKubernetesClient(clientSet KubernetesClientSet) *KubernetesClient {
	return &KubernetesClient{
		ClientSet: clientSet,
	}
}
