package services

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/devops-microservices/cicd-service/models"
	pod "github.com/tektoncd/pipeline/pkg/apis/pipeline/pod"
	tektonv1 "github.com/tektoncd/pipeline/pkg/apis/pipeline/v1"
	corev1 "k8s.io/api/core/v1"
	k8serrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// RunCI 执行持续集成
func (s *CICDService) RunCI(ctx context.Context, appInfoID uint, req models.CIRequest) (string, error) {
	s.logger.Infof("为应用 ID:%d 执行持续集成", appInfoID)

	// 检查数据库连接
	if s.db == nil {
		s.logger.Error("数据库连接为空")
		return "", fmt.Errorf("数据库连接未初始化")
	}

	// 确保Harbor和GitLab认证Secret存在
	if err := s.ensureHarborRegistrySecret(ctx); err != nil {
		s.logger.Warnf("确保Harbor认证Secret存在失败: %v", err)
		// 继续执行，不要因为Secret创建失败而阻止CI执行
	}

	if s.gitlabToken != "" {
		if err := s.ensureGitlabAuthSecret(ctx); err != nil {
			s.logger.Warnf("确保GitLab认证Secret存在失败: %v", err)
			// 继续执行，不要因为Secret创建失败而阻止CI执行
		}
	}

	// 获取应用信息
	s.logger.Infof("开始获取AppInfo: ID=%d", appInfoID)
	appInfoObj, err := s.GetAppInfo(ctx, fmt.Sprintf("%d", appInfoID))
	if err != nil {
		s.logger.Errorf("获取应用信息失败: %v", err)
		return "", fmt.Errorf("获取应用信息失败: %v", err)
	}

	// 获取应用
	s.logger.Infof("开始获取MicroApp: ID=%d [%s]", appInfoObj.AppID)
	app, err := s.GetApp(ctx, int(*appInfoObj.AppID))
	if err != nil {
		s.logger.Errorf("获取应用失败: %v", err)
		return "", fmt.Errorf("获取应用失败: %v", err)
	}

	// 记录成功获取的应用信息
	s.logger.Infof("成功获取应用信息: AppInfo.ID=%d, MicroApp.AppID=%s, MicroApp.Name=%s", appInfoID, app.ID, app.Name)

	// 如果AppCode为空，为其赋值
	if app.AppCode == "" {
		s.logger.Warnf("应用AppCode为空，使用应用名称作为替代: %s", app.Name)
		app.AppCode = app.Name
	}

	// 解析Git仓库信息
	// var repoInfo map[string]interface{}
	// repoInfo = app.RepoSettings.Data.(map[string]interface{})
	// err = json.Unmarshal([]byte(app.RepoSettings.Data.(string)), &repoInfo)
	// if err != nil {
	// 	s.logger.Errorf("解析仓库信息失败: %v, 原始数据: %s", err, app.RepoSettings.Data)
	// 	return "", fmt.Errorf("解析仓库信息失败: %v", err)
	// }

	// 获取仓库URL
	repoURL, ok := app.RepoSettings.Data.(map[string]interface{})["http_url_to_repo"].(string)
	if !ok || repoURL == "" {
		s.logger.Errorf("无效的仓库URL, repoInfo=%v", app.RepoSettings.Data)
		return "", fmt.Errorf("无效的仓库URL")
	}

	s.logger.Infof("使用仓库URL: %s", repoURL)

	// 获取环境信息以构建正确的项目名称
	var environment models.Environment
	if err := s.db.First(&environment, appInfoObj.EnvironmentID).Error; err != nil {
		s.logger.Errorf("获取环境信息失败: %v", err)
		return "", fmt.Errorf("获取环境信息失败: %v", err)
	}

	// 获取产品信息（已经通过分步加载）
	productName := "default"
	if app.Project.Product.Name != "" {
		productName = app.Project.Product.Name
	} else {
		s.logger.Warnf("应用 %s 的产品信息为空，使用默认值: %s", app.Name, productName)
	}

	// 构建镜像名称
	// 格式: harbor地址/项目名/应用名:标签（不包含协议前缀）
	// 项目名称格式: environmentName.lower() + "-" + productName.lower()
	projectName := fmt.Sprintf("%s-%s", strings.ToLower(environment.Name), strings.ToLower(productName))

	imageTag := fmt.Sprintf("%s_%s", time.Now().Format("20060102150405"), req.Commits.ShortID)

	// 调用Harbor API检查项目是否存在
	projectExists, err := s.checkHarborProject(ctx, projectName)
	if err != nil {
		s.logger.Errorf("检查Harbor项目是否存在失败: %v", err)
		return "", fmt.Errorf("检查Harbor项目是否存在失败: %v", err)
	}

	// 如果项目不存在，则创建项目
	if !projectExists {
		s.logger.Infof("Harbor项目 %s 不存在，正在创建...", projectName)
		err := s.createHarborProject(ctx, projectName)
		if err != nil {
			s.logger.Errorf("创建Harbor项目失败: %v", err)
			return "", fmt.Errorf("创建Harbor项目失败: %v", err)
		}
		s.logger.Infof("✅ 成功创建Harbor项目: %s", projectName)
	} else {
		s.logger.Infof("✅ Harbor项目 %s 已存在", projectName)
	}

	// 清理Harbor URL，移除协议前缀
	harborHost := s.harborURL
	harborHost = strings.TrimPrefix(harborHost, "http://")
	harborHost = strings.TrimPrefix(harborHost, "https://")
	harborHost = strings.TrimSuffix(harborHost, "/")

	imageName := fmt.Sprintf("%s/%s/%s:%s", harborHost, projectName, app.Name, imageTag)

	s.logger.Infof("构建镜像名称: %s (项目: %s, 环境: %s, 产品: %s)",
		imageName, projectName, environment.Name, productName)

	// 将Commits和CommitTag转换为JSON字符串
	commitsJSON, err := json.Marshal(req.Commits)
	if err != nil {
		return "", fmt.Errorf("序列化提交信息失败: %v", err)
	}

	commitTagJSON, err := json.Marshal(req.CommitTag)
	if err != nil {
		return "", fmt.Errorf("序列化提交标签失败: %v", err)
	}

	deployerID := 1

	// 创建构建任务记录
	buildJob := models.BuildJob{
		AppID:      fmt.Sprintf("%d", app.ID),
		AppInfoID:  appInfoID,
		Status:     3, // 构建中
		Commits:    string(commitsJSON),
		CommitTag:  string(commitTagJSON),
		IsDeploy:   0, // 默认不部署
		BatchUUID:  req.BatchUUID,
		Modules:    req.Modules,
		DeployerID: uint(deployerID),
	}

	// 确保AppID不为空
	if buildJob.AppID == "" {
		s.logger.Warnf("构建任务AppID为空，使用应用名称作为替代: %s", app.Name)
		buildJob.AppID = app.Name
	}

	if req.IsDeploy {
		buildJob.IsDeploy = 1
	}

	// 获取应用的Pipeline配置
	s.logger.Infof("获取应用 ID:%d, 应用模块 %s 的Pipeline配置", app.ID, appInfoObj.UniqueTag)
	pName := appInfoObj.UniqueTag
	if appInfoObj.PipelineID == 0 {
		pName = app.Name
		if app.PipelineID == 0 {
			pName = app.LanguageCode
		}
	}
	fmt.Println("pName======", pName)
	pipeline, err := s.pipelineService.GetPipelineConfig(pName)
	// fmt.Println("pipeline======", pipeline)
	if err != nil {
		s.logger.Errorf("获取应用Pipeline失败: %v", err)
		return "", fmt.Errorf("获取应用Pipeline失败: %v", err)
	}
	pipelineConfig := map[string]interface{}{}
	err = json.Unmarshal([]byte(pipeline.PipelineConfig.Data.(string)), &pipelineConfig)
	if err != nil {
		s.logger.Errorf("解析Pipeline配置失败: %v", err)
		return "", fmt.Errorf("解析Pipeline配置失败: %v", err)
	}
	// 从Pipeline配置中提取Pipeline名称, 每个microapp的pipeline名称必须唯一
	pipelineName := fmt.Sprintf("build-%s", strings.Replace(appInfoObj.UniqueTag, ".", "-", -1))

	// 更新Pipeline配置中的名称，确保唯一性
	if metadata, ok := pipelineConfig["metadata"].(map[string]interface{}); ok {
		metadata["name"] = pipelineName
	} else {
		pipelineConfig["metadata"] = map[string]interface{}{
			"name":      pipelineName,
			"namespace": "default",
		}
	}

	// 在Kubernetes中创建Pipeline
	tektonConfig := s.tektonClient.Config()
	pipelineInterface := s.tektonClient.PipelineClient.TektonV1().Pipelines(tektonConfig.Namespace)

	// 首先检查Pipeline是否已存在
	existingPipeline, err := pipelineInterface.Get(ctx, pipelineName, metav1.GetOptions{})
	if err != nil {
		if k8serrors.IsNotFound(err) {
			// Pipeline不存在，创建它
			s.logger.Infof("创建新的Pipeline: %s", pipelineName)

			// 从应用的Pipeline配置创建Tekton Pipeline
			tektonPipeline, err := s.CreateTektonPipelineFromConfig(pipelineConfig, pipelineName, tektonConfig.Namespace)
			if err != nil {
				s.logger.Errorf("创建Tekton Pipeline对象失败: %v", err)
				return "", fmt.Errorf("创建Tekton Pipeline对象失败: %v", err)
			}

			// 在Kubernetes中创建Pipeline
			createdPipeline, err := pipelineInterface.Create(ctx, tektonPipeline, metav1.CreateOptions{})
			if err != nil {
				s.logger.Errorf("在Kubernetes中创建Pipeline失败: %v", err)
				return "", fmt.Errorf("在Kubernetes中创建Pipeline失败: %v", err)
			}

			s.logger.Infof("成功创建Pipeline: %s", createdPipeline.Name)
		} else {
			s.logger.Errorf("检查Pipeline失败: %v", err)
			return "", fmt.Errorf("检查Pipeline失败: %v", err)
		}
	} else {
		s.logger.Infof("Pipeline %s 已存在，跳过创建", pipelineName)
		// 更新tekton pipeline
		tektonPipeline, err := s.CreateTektonPipelineFromConfig(pipelineConfig, pipelineName, tektonConfig.Namespace)
		if err != nil {
			s.logger.Errorf("更新Tekton Pipeline对象失败: %v", err)
			return "", fmt.Errorf("更新Tekton Pipeline对象失败: %v", err)
		}
		// 设置resourceVersion，这是更新资源的必要字段
		tektonPipeline.ObjectMeta.ResourceVersion = existingPipeline.ObjectMeta.ResourceVersion

		_, err = pipelineInterface.Update(ctx, tektonPipeline, metav1.UpdateOptions{})
		if err != nil {
			s.logger.Errorf("更新Tekton Pipeline对象失败: %v", err)
			return "", fmt.Errorf("更新Tekton Pipeline对象失败: %v", err)
		}
		s.logger.Infof("成功更新Pipeline: %s", pipelineName)
	}

	s.logger.Infof("使用Pipeline: %s", pipelineName)

	// 创建PipelineRun参数
	params := []tektonv1.Param{
		{
			Name: "git-url",
			Value: tektonv1.ParamValue{
				Type:      tektonv1.ParamTypeString,
				StringVal: repoURL,
			},
		},
		{
			Name: "git-revision",
			Value: tektonv1.ParamValue{
				Type:      tektonv1.ParamTypeString,
				StringVal: req.CommitTag.Name,
			},
		},
		{
			Name: "image-name",
			Value: tektonv1.ParamValue{
				Type:      tektonv1.ParamTypeString,
				StringVal: imageName,
			},
		},
		{
			Name: "build-id",
			Value: tektonv1.ParamValue{
				Type:      tektonv1.ParamTypeString,
				StringVal: fmt.Sprintf("%d", buildJob.ID),
			},
		},
	}

	// 添加Harbor认证参数
	params = append(params, tektonv1.Param{
		Name: "HARBOR_USERNAME",
		Value: tektonv1.ParamValue{
			Type:      tektonv1.ParamTypeString,
			StringVal: s.harborUsername,
		},
	})

	params = append(params, tektonv1.Param{
		Name: "HARBOR_PASSWORD",
		Value: tektonv1.ParamValue{
			Type:      tektonv1.ParamTypeString,
			StringVal: s.harborPassword,
		},
	})

	// 添加模块参数
	if req.Modules != "" {
		params = append(params, tektonv1.Param{
			Name: "modules",
			Value: tektonv1.ParamValue{
				Type:      tektonv1.ParamTypeString,
				StringVal: req.Modules,
			},
		})
	}

	// 添加语言相关参数
	params = append(params, tektonv1.Param{
		Name: "language",
		Value: tektonv1.ParamValue{
			Type:      tektonv1.ParamTypeString,
			StringVal: app.LanguageCode,
		},
	})

	// 添加构建命令
	params = append(params, tektonv1.Param{
		Name: "build-command",
		Value: tektonv1.ParamValue{
			Type:      tektonv1.ParamTypeString,
			StringVal: appInfoObj.BuildCommand,
		},
	})

	// 生成唯一的PipelineRun名称
	pipelineRunName := fmt.Sprintf("build-%s-%d", strings.Replace(appInfoObj.UniqueTag, ".", "-", -1), time.Now().Unix())

	// 创建 PipelineRun
	pipelineRun := &tektonv1.PipelineRun{
		ObjectMeta: metav1.ObjectMeta{
			Name:      pipelineRunName,
			Namespace: tektonConfig.Namespace,
		},
		Spec: tektonv1.PipelineRunSpec{
			PipelineRef: &tektonv1.PipelineRef{
				Name: pipelineName,
			},
			Params:   params,
			Timeouts: &tektonv1.TimeoutFields{Pipeline: &metav1.Duration{Duration: 30 * time.Minute}},
			Workspaces: []tektonv1.WorkspaceBinding{
				{
					Name: "shared-workspace",
					PersistentVolumeClaim: &corev1.PersistentVolumeClaimVolumeSource{
						ClaimName: "tekton-workspace-pvc",
					},
					SubPath: pipelineRunName, // 使用PipelineRun名称作为子目录
				},
				{
					Name: "docker-credentials",
					Secret: &corev1.SecretVolumeSource{
						SecretName: "docker-config",
					},
				},
				{
					Name: "gitlab-credentials",
					Secret: &corev1.SecretVolumeSource{
						SecretName: "gitlab-auth",
					},
				},
			},
			TaskRunTemplate: tektonv1.PipelineTaskRunTemplate{
				ServiceAccountName: "tekton-sa",
				PodTemplate: &pod.Template{
					HostAliases: []corev1.HostAlias{
						{
							IP: "************",
							Hostnames: []string{
								"harbor.fundpark.com",
							},
						},
					},
				},
			},
		},
	}

	// 保存到数据库
	result := s.db.Create(&buildJob)
	if result.Error != nil {
		s.logger.Errorf("创建构建任务记录失败: %v, AppID=%s, AppInfoID=%d, App.Name=%s, UniqueTag=%s",
			result.Error, buildJob.AppID, appInfoID, app.Name, appInfoObj.UniqueTag)
		return "", fmt.Errorf("创建构建任务记录失败: %v", result.Error)
	}

	// 创建 PipelineRun
	createdPipelineRun, err := s.tektonClient.PipelineClient.TektonV1().PipelineRuns(tektonConfig.Namespace).Create(ctx, pipelineRun, metav1.CreateOptions{})
	if err != nil {
		// 更新构建任务状态
		s.db.Model(&buildJob).Updates(map[string]interface{}{
			"status": 2, // 构建失败
		})
		return "", fmt.Errorf("创建 PipelineRun 失败: %v", err)
	}

	// 更新构建任务记录
	s.db.Model(&buildJob).Updates(map[string]interface{}{
		"image":        imageName,
		"build_number": buildJob.ID,
	})

	// 创建监控服务并启动一个goroutine来监控构建状态
	monitorService := NewBuildMonitorService(s.tektonClient, s.db, s.logger)
	go func() {
		err := monitorService.MonitorBuildStatus(context.Background(), buildJob.ID, createdPipelineRun.Name, pipelineName)
		if err != nil {
			s.logger.Errorf("监控构建状态失败: %v", err)
		}
	}()

	s.logger.Infof("成功启动CI流水线，Pipeline: %s, PipelineRun: %s", pipelineName, createdPipelineRun.Name)
	return fmt.Sprintf("%d", buildJob.ID), nil
}
