package services

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/devops-microservices/cicd-service/models"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// PublishService 处理应用发布的服务
type PublishService struct {
	cicdService *CICDService
	db          *gorm.DB
	log         logrus.FieldLogger
}

// NewPublishService 创建一个新的发布服务
func NewPublishService(cicdService *CICDService, db *gorm.DB, log logrus.FieldLogger) *PublishService {
	return &PublishService{
		cicdService: cicdService,
		db:          db,
		log:         log,
	}
}

// CreatePublishOrder 创建发布工单
func (s *PublishService) CreatePublishOrder(ctx context.Context, order *models.PublishOrder, apps []uint) (*models.PublishOrder, error) {
	// 创建工单
	if err := s.db.Create(order).Error; err != nil {
		s.log.Errorf("创建发布工单失败: %v", err)
		return nil, fmt.Errorf("创建发布工单失败: %v", err)
	}

	// 创建工单应用
	for _, appInfoID := range apps {
		var appInfo models.AppInfo
		if err := s.db.First(&appInfo, appInfoID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				s.log.Warnf("应用信息不存在: %d", appInfoID)
				continue
			}
			s.log.Errorf("查询应用信息失败: %v", err)
			continue
		}

		// 查询MicroApp
		var microApp models.MicroApp
		if err := s.db.First(&microApp, appInfo.AppID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				s.log.Warnf("应用不存在: %d", appInfo.AppID)
				continue
			}
			s.log.Errorf("查询应用失败: %v", err)
			continue
		}

		// 创建发布应用
		publishApp := &models.PublishApp{
			OrderID:     order.OrderID,
			AppID:       fmt.Sprintf("%d", microApp.ID),
			AppInfoID:   appInfo.ID,
			Name:        microApp.Name,
			Project:     microApp.Project.Name,
			Product:     microApp.Project.Product.Name,
			Category:    microApp.CategoryName,
			Environment: int(*appInfo.EnvironmentID),
			Branch:      "master", // TODO: 从实际应用获取
			Status:      0,        // 未发布
		}

		if err := s.db.Create(publishApp).Error; err != nil {
			s.log.Errorf("创建发布应用失败: %v", err)
			continue
		}
	}

	return order, nil
}

// GetPublishOrder 获取发布工单
func (s *PublishService) GetPublishOrder(ctx context.Context, orderID string) (*models.PublishOrder, error) {
	var order models.PublishOrder
	if err := s.db.Where("order_id = ?", orderID).First(&order).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("发布工单不存在: %s", orderID)
		}
		return nil, fmt.Errorf("查询发布工单失败: %v", err)
	}
	return &order, nil
}

// ListPublishOrders 列出发布工单
func (s *PublishService) ListPublishOrders(ctx context.Context, params map[string]interface{}) ([]models.PublishOrder, int64, error) {
	var orders []models.PublishOrder
	var total int64

	// 构建查询
	query := s.db.Model(&models.PublishOrder{})

	// 应用过滤条件
	if params != nil {
		if envID, ok := params["environment"]; ok {
			query = query.Where("environment = ?", envID)
		}
		if status, ok := params["status"]; ok {
			query = query.Where("status = ?", status)
		}
		if creatorID, ok := params["creator_id"]; ok {
			query = query.Where("creator_id = ?", creatorID)
		}
		if startTime, ok := params["start_time"]; ok {
			query = query.Where("created_time >= ?", startTime)
		}
		if endTime, ok := params["end_time"]; ok {
			query = query.Where("created_time <= ?", endTime)
		}
		if search, ok := params["search"].(string); ok && search != "" {
			query = query.Where("order_id LIKE ? OR title LIKE ? OR content LIKE ?",
				"%"+search+"%", "%"+search+"%", "%"+search+"%")
		}
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("计数发布工单失败: %v", err)
	}

	// 应用分页
	if page, ok := params["page"].(int); ok && page > 0 {
		pageSize := 10 // 默认页大小
		if size, ok := params["page_size"].(int); ok && size > 0 {
			pageSize = size
		}
		query = query.Offset((page - 1) * pageSize).Limit(pageSize)
	}

	// 应用排序
	if orderBy, ok := params["order_by"].(string); ok && orderBy != "" {
		query = query.Order(orderBy)
	} else {
		query = query.Order("created_at DESC")
	}

	// 执行查询
	if err := query.Find(&orders).Error; err != nil {
		return nil, 0, fmt.Errorf("查询发布工单列表失败: %v", err)
	}

	return orders, total, nil
}

// GetPublishApps 获取发布工单关联的应用
func (s *PublishService) GetPublishApps(ctx context.Context, orderID string) ([]models.PublishApp, error) {
	var apps []models.PublishApp
	if err := s.db.Where("order_id = ?", orderID).Find(&apps).Error; err != nil {
		return nil, fmt.Errorf("查询发布应用失败: %v", err)
	}
	return apps, nil
}

// UpdatePublishOrderStatus 更新发布工单状态
func (s *PublishService) UpdatePublishOrderStatus(ctx context.Context, orderID string, status int) error {
	// 检查状态是否有效
	if status < 0 || status > 4 {
		return fmt.Errorf("无效的状态值: %d", status)
	}

	// 更新工单状态
	if err := s.db.Model(&models.PublishOrder{}).Where("order_id = ?", orderID).Update("status", status).Error; err != nil {
		return fmt.Errorf("更新工单状态失败: %v", err)
	}

	// 如果工单状态为已取消(4)，同时取消所有未发布的应用
	if status == 4 {
		if err := s.db.Model(&models.PublishApp{}).
			Where("order_id = ? AND status = 0", orderID).
			Update("status", 4).Error; err != nil {
			s.log.Errorf("更新应用状态失败: %v", err)
		}
	}

	return nil
}

// UpdatePublishAppStatus 更新发布应用状态
func (s *PublishService) UpdatePublishAppStatus(ctx context.Context, appID uint, status int) error {
	// 检查状态是否有效
	if status < 0 || status > 4 {
		return fmt.Errorf("无效的状态值: %d", status)
	}

	// 获取应用信息
	var app models.PublishApp
	if err := s.db.First(&app, appID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("发布应用不存在: %d", appID)
		}
		return fmt.Errorf("查询发布应用失败: %v", err)
	}

	// 更新应用状态
	if err := s.db.Model(&app).Update("status", status).Error; err != nil {
		return fmt.Errorf("更新应用状态失败: %v", err)
	}

	// 检查工单状态是否需要更新
	if err := s.checkAndUpdateOrderStatus(ctx, app.OrderID); err != nil {
		s.log.Errorf("检查工单状态失败: %v", err)
	}

	return nil
}

// checkAndUpdateOrderStatus 检查并更新工单状态
func (s *PublishService) checkAndUpdateOrderStatus(ctx context.Context, orderID string) error {
	// 查询工单所有应用
	var apps []models.PublishApp
	if err := s.db.Where("order_id = ?", orderID).Find(&apps).Error; err != nil {
		return fmt.Errorf("查询工单应用失败: %v", err)
	}

	// 检查是否所有应用都已发布或取消
	allCompleted := true
	allCanceled := true
	hasFailed := false
	hasRunning := false

	for _, app := range apps {
		if app.Status != 1 && app.Status != 4 { // 不是发布成功或取消
			allCompleted = false
		}
		if app.Status != 4 { // 不是取消
			allCanceled = false
		}
		if app.Status == 2 { // 发布失败
			hasFailed = true
		}
		if app.Status == 3 { // 发布中
			hasRunning = true
		}
	}

	// 获取当前工单状态
	var order models.PublishOrder
	if err := s.db.Where("order_id = ?", orderID).First(&order).Error; err != nil {
		return fmt.Errorf("查询工单失败: %v", err)
	}

	// 更新工单状态
	var newStatus int
	if allCanceled {
		newStatus = 4 // 已取消
	} else if allCompleted {
		newStatus = 1 // 发布成功
	} else if hasFailed {
		newStatus = 2 // 有失败
	} else if hasRunning {
		newStatus = 3 // 发布中
	} else {
		newStatus = 0 // 未发布
	}

	// 如果状态有变化，更新工单
	if newStatus != order.Status {
		if err := s.db.Model(&order).Update("status", newStatus).Error; err != nil {
			return fmt.Errorf("更新工单状态失败: %v", err)
		}

		// 如果状态变为已完成，更新完成时间
		if newStatus == 1 {
			if err := s.db.Model(&order).Update("deploy_time", time.Now()).Error; err != nil {
				s.log.Errorf("更新工单完成时间失败: %v", err)
			}
		}
	}

	return nil
}

// DeployPublishApp 部署发布应用
func (s *PublishService) DeployPublishApp(ctx context.Context, appID uint) error {
	// 查询发布应用信息
	var app models.PublishApp
	if err := s.db.First(&app, appID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("发布应用不存在: %d", appID)
		}
		return fmt.Errorf("查询发布应用失败: %v", err)
	}

	// 校验状态
	if app.Status != 0 && app.Status != 2 {
		// 非待发布状态或发布失败状态
		return fmt.Errorf("当前应用状态不允许发布: %d", app.Status)
	}

	// 更新状态为发布中
	if err := s.db.Model(&app).Update("status", 3).Error; err != nil {
		return fmt.Errorf("更新应用状态失败: %v", err)
	}

	// 创建部署请求
	deployReq := models.DeployRequest{
		Name:           app.Name,
		Namespace:      fmt.Sprintf("%s-%s", app.Product, app.Environment),
		DockerImage:    app.Image,
		DeploymentType: "kubernetes", // 根据实际情况选择部署类型
		Replicas:       2,            // 默认副本数
		Env: map[string]string{
			"APP_MODULE": app.Modules,
			"APP_ID":     app.AppID,
		},
	}

	// 后台执行部署，避免阻塞主线程
	go func() {
		// 创建新的上下文，避免主线程取消时影响部署进程
		deployCtx := context.Background()

		// 创建部署
		deployID, err := s.cicdService.CreateDeploy(deployCtx, deployReq)
		if err != nil {
			s.log.Errorf("创建部署失败: %v", err)

			// 更新状态为发布失败
			if err := s.db.Model(&app).Update("status", 2).Error; err != nil {
				s.log.Errorf("更新应用状态失败: %v", err)
			}

			// 检查并更新工单状态
			if err := s.checkAndUpdateOrderStatus(deployCtx, app.OrderID); err != nil {
				s.log.Errorf("检查工单状态失败: %v", err)
			}

			return
		}

		// 记录部署ID
		deployJob := &models.DeployJob{
			UniqID:    deployID,
			OrderID:   app.OrderID,
			AppID:     app.AppID,
			AppInfoID: app.AppInfoID,
			Status:    3, // 部署中
			Image:     app.Image,
			Modules:   app.Modules,
		}

		// 确保AppID不为空
		if deployJob.AppID == "" {
			// 使用应用名称作为替代
			deployJob.AppID = app.Name
			s.log.Warnf("应用AppID为空，使用应用名称作为替代: %s", app.Name)
		}

		if err := s.db.Create(deployJob).Error; err != nil {
			s.log.Errorf("创建部署记录失败: %v", err)
		}

		// 等待部署完成
		ticker := time.NewTicker(10 * time.Second)
		defer ticker.Stop()

		timeout := time.After(30 * time.Minute)

		for {
			select {
			case <-ticker.C:
				// 检查部署状态
				deployStatus, err := s.cicdService.GetDeployStatus(deployCtx, deployID)
				if err != nil {
					s.log.Errorf("检查部署状态失败: %v", err)
					continue
				}

				// 解析部署状态
				status, _ := deployStatus["status"].(string)
				if status == "ready" {
					// 部署成功
					s.log.Infof("应用部署成功: %s", deployID)

					// 更新部署任务状态
					if err := s.db.Model(deployJob).Update("status", 1).Error; err != nil {
						s.log.Errorf("更新部署任务状态失败: %v", err)
					}

					// 更新应用状态为发布成功
					if err := s.db.Model(&app).Update("status", 1).Error; err != nil {
						s.log.Errorf("更新应用状态失败: %v", err)
					}

					// 检查并更新工单状态
					if err := s.checkAndUpdateOrderStatus(deployCtx, app.OrderID); err != nil {
						s.log.Errorf("检查工单状态失败: %v", err)
					}

					return
				} else if status == "failed" || status == "not_found" {
					// 部署失败
					s.log.Errorf("应用部署失败: %s, 状态: %s", deployID, status)

					// 更新部署任务状态
					if err := s.db.Model(deployJob).Update("status", 2).Error; err != nil {
						s.log.Errorf("更新部署任务状态失败: %v", err)
					}

					// 更新应用状态为发布失败
					if err := s.db.Model(&app).Update("status", 2).Error; err != nil {
						s.log.Errorf("更新应用状态失败: %v", err)
					}

					// 检查并更新工单状态
					if err := s.checkAndUpdateOrderStatus(deployCtx, app.OrderID); err != nil {
						s.log.Errorf("检查工单状态失败: %v", err)
					}

					return
				}

			case <-timeout:
				// 超时
				s.log.Errorf("应用部署超时: %s", deployID)

				// 尝试取消部署
				if err := s.cicdService.CancelDeploy(deployCtx, deployID); err != nil {
					s.log.Errorf("取消部署失败: %v", err)
				}

				// 更新部署任务状态
				if err := s.db.Model(deployJob).Update("status", 2).Error; err != nil {
					s.log.Errorf("更新部署任务状态失败: %v", err)
				}

				// 更新应用状态为发布失败
				if err := s.db.Model(&app).Update("status", 2).Error; err != nil {
					s.log.Errorf("更新应用状态失败: %v", err)
				}

				// 检查并更新工单状态
				if err := s.checkAndUpdateOrderStatus(deployCtx, app.OrderID); err != nil {
					s.log.Errorf("检查工单状态失败: %v", err)
				}

				return
			}
		}
	}()

	return nil
}

// CancelPublishApp 取消发布应用
func (s *PublishService) CancelPublishApp(ctx context.Context, appID uint) error {
	// 获取应用信息
	var app models.PublishApp
	if err := s.db.First(&app, appID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("发布应用不存在: %d", appID)
		}
		return fmt.Errorf("查询发布应用失败: %v", err)
	}

	// 获取部署ID
	deployID := getDeployIDFromApp(app)

	// 从数据库获取部署记录
	var job models.DeployJob
	if err := s.db.Where("uniq_id = ?", deployID).First(&job).Error; err == nil {
		if job.Status == 3 { // 部署中
			// 取消构建
			if err := s.cicdService.CancelPipeline(ctx, job.UniqID); err != nil {
				s.log.Errorf("取消部署失败: %v", err)
			}
		}
	}

	// 直接更新应用状态
	record := app
	record.Status = 4 // 已取消
	if err := s.db.Save(&record).Error; err != nil {
		s.log.Errorf("取消发布记录失败: %v", err)
	}

	// 检查并更新工单状态
	if err := s.checkAndUpdateOrderStatus(ctx, app.OrderID); err != nil {
		s.log.Errorf("检查工单状态失败: %v", err)
	}

	return nil
}

// CancelPublishOrder 取消发布工单
func (s *PublishService) CancelPublishOrder(ctx context.Context, orderID string) error {
	// 获取工单信息
	var order models.PublishOrder
	if err := s.db.Where("order_id = ?", orderID).First(&order).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("发布工单不存在: %s", orderID)
		}
		return fmt.Errorf("查询发布工单失败: %v", err)
	}

	// 检查工单状态
	if order.Status == 4 { // 已取消
		return nil
	}

	// 获取所有关联应用
	var apps []models.PublishApp
	if err := s.db.Where("order_id = ?", orderID).Find(&apps).Error; err != nil {
		return fmt.Errorf("查询发布应用失败: %v", err)
	}

	// 取消所有应用
	for _, app := range apps {
		if app.Status == 0 || app.Status == 2 { // 未发布或发布失败
			// 直接更新状态
			if err := s.db.Model(&app).Update("status", 4).Error; err != nil {
				s.log.Errorf("更新应用状态失败: %v", err)
			}
		} else if app.Status == 3 { // 发布中
			// 取消发布
			if err := s.CancelPublishApp(ctx, app.ID); err != nil {
				s.log.Errorf("取消发布应用失败: %v", err)
			}
		}
	}

	// 更新工单状态
	if err := s.db.Model(&order).Update("status", 4).Error; err != nil {
		return fmt.Errorf("更新工单状态失败: %v", err)
	}

	return nil
}

// 添加DeployID的获取方法，使用ID字段代替
func getDeployIDFromApp(app models.PublishApp) string {
	// 使用应用ID作为部署ID
	return fmt.Sprintf("deploy-%d", app.ID)
}
