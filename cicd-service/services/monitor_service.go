package services

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/devops-microservices/cicd-service/models"
	"github.com/sirupsen/logrus"
	tektonv1 "github.com/tektoncd/pipeline/pkg/apis/pipeline/v1"
	"gorm.io/gorm"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"knative.dev/pkg/apis"
)

// BuildMonitorService 构建监控服务
type BuildMonitorService struct {
	tektonClient *TektonClientImpl
	db           *gorm.DB
	logger       logrus.FieldLogger
}

// NewBuildMonitorService 创建新的构建监控服务
func NewBuildMonitorService(tektonClient *TektonClientImpl, db *gorm.DB, logger logrus.FieldLogger) *BuildMonitorService {
	return &BuildMonitorService{
		tektonClient: tektonClient,
		db:           db,
		logger:       logger,
	}
}

// MonitorBuildStatus 监控构建状态并更新数据库
func (s *BuildMonitorService) MonitorBuildStatus(ctx context.Context, buildID uint, pipelineRunName, pipelineName string) error {
	// 创建一个带超时的上下文
	ctx, cancel := context.WithTimeout(ctx, 60*time.Minute)
	defer cancel()

	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	// 获取Tekton配置 - 使用传入的pipeline名称
	tektonConfig := &TektonConfig{
		Namespace:    "default",
		PipelineName: pipelineName,
	}

	s.logger.Infof("开始监控构建状态: BuildID=%d, PipelineRun=%s, Pipeline=%s", buildID, pipelineRunName, pipelineName)

	for {
		select {
		case <-ctx.Done():
			// 上下文超时或取消
			s.logger.Warnf("构建监控超时: BuildID=%d, PipelineRun=%s", buildID, pipelineRunName)
			s.updateBuildStatus(buildID, 2) // 构建失败
			return ctx.Err()
		case <-ticker.C:
			// 检查构建状态
			pipelineRun, err := s.getPipelineRunWithRetry(ctx, pipelineRunName, tektonConfig.Namespace)
			if err != nil {
				s.logger.Errorf("获取PipelineRun %s 失败: %v", pipelineRunName, err)
				continue
			}

			// 检查PipelineRun状态
			status := s.mapPipelineRunStatus(pipelineRun)
			if status == 3 {
				// 仍在构建中，继续等待
				continue
			}

			s.logger.Infof("构建完成: BuildID=%d, Status=%d", buildID, status)

			// 收集构建结果和日志
			if err := s.saveBuildResult(ctx, buildID, pipelineRunName, pipelineRun, status, tektonConfig.Namespace); err != nil {
				s.logger.Errorf("保存构建结果失败: %v", err)
			}

			// 更新构建任务状态
			if err := s.updateBuildStatus(buildID, status); err != nil {
				s.logger.Errorf("更新构建任务状态失败: %v", err)
			}

			// 如果构建成功，检查是否需要自动部署
			if status == 1 {
				s.checkAutoDeployment(buildID)
			}

			return nil
		}
	}
}

// getPipelineRunWithRetry 带重试的获取PipelineRun
func (s *BuildMonitorService) getPipelineRunWithRetry(ctx context.Context, pipelineRunName, namespace string) (*tektonv1.PipelineRun, error) {
	const maxRetries = 3
	var lastErr error

	for retry := 0; retry < maxRetries; retry++ {
		pipelineRun, err := s.tektonClient.PipelineClient.TektonV1().PipelineRuns(namespace).Get(ctx, pipelineRunName, metav1.GetOptions{})
		if err == nil {
			return pipelineRun, nil
		}

		lastErr = err
		if retry < maxRetries-1 {
			waitTime := time.Duration(retry+1) * time.Second
			s.logger.Warnf("获取PipelineRun失败，%v后重试 (第%d次): %v", waitTime, retry+1, err)
			time.Sleep(waitTime)
		}
	}

	return nil, fmt.Errorf("获取PipelineRun失败，已重试%d次: %v", maxRetries, lastErr)
}

// mapPipelineRunStatus 映射PipelineRun状态到BuildJob状态
func (s *BuildMonitorService) mapPipelineRunStatus(pipelineRun *tektonv1.PipelineRun) int {
	if pipelineRun.Status.GetCondition(apis.ConditionSucceeded).IsTrue() {
		return 1 // 构建成功
	} else if pipelineRun.Status.GetCondition(apis.ConditionSucceeded).IsFalse() {
		return 2 // 构建失败
	} else if pipelineRun.Spec.Status == tektonv1.PipelineRunSpecStatusCancelled {
		return 4 // 已取消
	}
	return 3 // 构建中
}

// updateBuildStatus 更新构建任务状态
func (s *BuildMonitorService) updateBuildStatus(buildID uint, status int) error {
	return s.db.Model(&models.BuildJob{}).Where("id = ?", buildID).Updates(map[string]interface{}{
		"status": status,
	}).Error
}

// saveBuildResult 保存构建结果
func (s *BuildMonitorService) saveBuildResult(ctx context.Context, buildID uint, pipelineRunName string, pipelineRun *tektonv1.PipelineRun, status int, namespace string) error {
	// 创建构建结果记录
	buildResult := &models.BuildJobResult{
		JobID: buildID,
	}

	// 构建结果数据
	resultData := map[string]interface{}{
		"pipeline_run_name": pipelineRunName,
		"status":            status,
		"started_at":        pipelineRun.Status.StartTime,
		"completed_at":      pipelineRun.Status.CompletionTime,
	}

	// 将构建结果保存为JSON
	resultJSON, _ := json.Marshal(resultData)
	buildResult.Result = string(resultJSON)

	// 收集控制台输出
	consoleOutput, err := s.collectTaskLogs(ctx, pipelineRunName, namespace)
	if err != nil {
		s.logger.Errorf("收集任务日志失败: %v", err)
		consoleOutput = fmt.Sprintf("日志收集失败: %v", err)
	}
	buildResult.ConsoleOutput = consoleOutput

	// 保存到数据库
	return s.db.Create(buildResult).Error
}

// collectTaskLogs 收集任务日志
func (s *BuildMonitorService) collectTaskLogs(ctx context.Context, pipelineRunName, namespace string) (string, error) {
	taskRuns, err := s.tektonClient.PipelineClient.TektonV1().TaskRuns(namespace).List(ctx, metav1.ListOptions{
		LabelSelector: fmt.Sprintf("tekton.dev/pipelineRun=%s", pipelineRunName),
	})

	if err != nil {
		return "", fmt.Errorf("获取TaskRuns失败: %v", err)
	}

	var consoleOutput strings.Builder
	consoleOutput.WriteString(fmt.Sprintf("=== 构建日志 (PipelineRun: %s) ===\n\n", pipelineRunName))

	for _, taskRun := range taskRuns.Items {
		consoleOutput.WriteString(fmt.Sprintf("📋 Task: %s\n", taskRun.Name))
		consoleOutput.WriteString(fmt.Sprintf("⏰ 开始时间: %v\n", taskRun.Status.StartTime))
		consoleOutput.WriteString(fmt.Sprintf("🏁 完成时间: %v\n", taskRun.Status.CompletionTime))

		condition := taskRun.Status.GetCondition(apis.ConditionSucceeded)
		if condition != nil {
			consoleOutput.WriteString(fmt.Sprintf("📊 状态: %s\n", condition.GetReason()))
			if condition.GetMessage() != "" {
				consoleOutput.WriteString(fmt.Sprintf("💬 消息: %s\n", condition.GetMessage()))
			}
		}

		// TODO: 实现真实的Pod日志收集
		// 这里可以添加获取Pod日志的逻辑
		consoleOutput.WriteString("--- 任务执行日志 ---\n")
		consoleOutput.WriteString(fmt.Sprintf("任务 %s 执行完成\n", taskRun.Name))
		consoleOutput.WriteString("--- 日志结束 ---\n\n")
	}

	return consoleOutput.String(), nil
}

// checkAutoDeployment 检查是否需要自动部署
func (s *BuildMonitorService) checkAutoDeployment(buildID uint) {
	var buildJob models.BuildJob
	if err := s.db.First(&buildJob, buildID).Error; err != nil {
		s.logger.Errorf("获取构建任务失败: %v", err)
		return
	}

	if buildJob.IsDeploy == 1 {
		s.logger.Infof("构建成功，准备自动部署应用 ID:%d", buildJob.AppInfoID)
		// TODO: 这里可以调用部署服务
		// deployService.CreateDeploy(...)
	}
}
