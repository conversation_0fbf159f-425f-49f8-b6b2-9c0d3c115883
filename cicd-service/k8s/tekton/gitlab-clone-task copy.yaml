apiVersion: tekton.dev/v1beta1
kind: Task
metadata:
  name: gitlab-clone
  labels:
    app.kubernetes.io/version: "0.9"
  annotations:
    tekton.dev/pipelines.minVersion: "0.38.0"
    tekton.dev/categories: Git
    tekton.dev/tags: git
    tekton.dev/displayName: "git clone"
    tekton.dev/platforms: "linux/amd64,linux/s390x,linux/ppc64le,linux/arm64"
spec:
  description: |
    从GitLab克隆代码仓库，支持新旧两种认证格式
  workspaces:
    - name: output
      description: 代码将被克隆到这个工作空间
    - name: basic-auth
      description: 包含GitLab认证信息的Secret
  params:
    - name: url
      description: 仓库URL
      type: string
    - name: revision
      description: 要检出的修订（分支、标签、SHA等）
      type: string
      default: "main"
    - name: depth
      description: 浅克隆深度，只获取最近的N个提交
      type: string
      default: "1"
    - name: deleteExisting
      description: 如果目标目录已存在，是否清空内容
      type: string
      default: "true"
    - name: gitInitImage
      description: The image providing the git-init binary that this Task runs.
      type: string
      default: "gcr.io/tekton-releases/github.com/tektoncd/pipeline/cmd/git-init:v0.40.2"
    - name: userHome
      description: |
        Absolute path to the user's home directory.
      type: string
      default: "/home/<USER>"
  results:
    - name: commit
      description: The precise commit SHA that was fetched by this Task.
    - name: url
      description: The precise URL that was fetched by this Task.
    - name: committer-date
      description: The epoch timestamp of the commit that was fetched by this Task.
  steps:
    - name: clone
      image: "$(params.gitInitImage)"
      env:
        - name: HOME
          value: "$(params.userHome)"
      script: |
        #!/bin/sh
        set -ex

        echo "🔧 开始克隆GitLab仓库"
        echo "📁 工作空间路径: $(workspaces.output.path)"
        echo "🌐 仓库URL: $(params.url)"
        echo "🏷️  版本: $(params.revision)"

        # 设置工作目录 - 直接克隆到workspace根目录
        CLONE_DIR="$(workspaces.output.path)"
        echo "📂 克隆目录: ${CLONE_DIR}"

        # 清理目录（如果需要）
        DELETE_EXISTING="$(params.deleteExisting)"
        if [ "${DELETE_EXISTING}" = "true" ]; then
          echo "🧹 清理现有内容..."
          find "${CLONE_DIR}" -mindepth 1 -maxdepth 1 -not -name "lost+found" -exec rm -rf {} + 2>/dev/null || true
        fi

        # 切换到工作目录
        cd "${CLONE_DIR}"

        # 从basic-auth workspace读取认证信息
        echo "🔍 检查basic-auth workspace:"
        BASIC_AUTH_PATH="$(workspaces.basic-auth.path)"
        echo "  Path: ${BASIC_AUTH_PATH}"

        # 初始化认证变量
        GIT_USERNAME=""
        GIT_PASSWORD=""
        GIT_TOKEN=""

        if [ -d "${BASIC_AUTH_PATH}" ]; then
          echo "📋 Workspace内容:"
          ls -la "${BASIC_AUTH_PATH}/" || true
          
          # 方法1: 检查新格式文件 (username, password, token)
          if [ -f "${BASIC_AUTH_PATH}/username" ]; then
            GIT_USERNAME=$(cat "${BASIC_AUTH_PATH}/username")
            echo "  ✅ 读取username文件: ${GIT_USERNAME}"
          fi
          
          if [ -f "${BASIC_AUTH_PATH}/password" ]; then
            GIT_PASSWORD=$(cat "${BASIC_AUTH_PATH}/password")
            echo "  ✅ 读取password文件: [已设置]"
          fi
          
          if [ -f "${BASIC_AUTH_PATH}/token" ]; then
            GIT_TOKEN=$(cat "${BASIC_AUTH_PATH}/token")
            if [ -n "${GIT_TOKEN}" ]; then
              echo "  ✅ 读取token文件: [已设置]"
            else
              echo "  ⚠️  token文件存在但为空"
            fi
          fi
          
          # 方法2: 检查旧格式文件 (.git-credentials)
          if [ -f "${BASIC_AUTH_PATH}/.git-credentials" ]; then
            echo "  🔍 发现.git-credentials文件，解析认证信息..."
            CREDENTIALS_CONTENT=$(cat "${BASIC_AUTH_PATH}/.git-credentials")
            echo "  📄 Credentials内容: ${CREDENTIALS_CONTENT}"
            
            # 解析.git-credentials格式: ******************************
            if echo "${CREDENTIALS_CONTENT}" | grep -q "@"; then
              # 提取用户名和密码
              AUTH_PART=$(echo "${CREDENTIALS_CONTENT}" | sed 's|https\?://||' | cut -d'@' -f1)
              if echo "${AUTH_PART}" | grep -q ":"; then
                PARSED_USERNAME=$(echo "${AUTH_PART}" | cut -d':' -f1)
                PARSED_PASSWORD=$(echo "${AUTH_PART}" | cut -d':' -f2)
                
                # 如果新格式没有设置，使用旧格式的值
                if [ -z "${GIT_USERNAME}" ]; then
                  GIT_USERNAME="${PARSED_USERNAME}"
                  echo "  ✅ 从.git-credentials解析username: ${GIT_USERNAME}"
                fi
                
                if [ -z "${GIT_PASSWORD}" ]; then
                  GIT_PASSWORD="${PARSED_PASSWORD}"
                  echo "  ✅ 从.git-credentials解析password: [已设置]"
                fi
              fi
            fi
          fi
          
          # 方法3: 设置git配置文件
          if [ -f "${BASIC_AUTH_PATH}/.gitconfig" ]; then
            echo "  📋 发现.gitconfig文件，应用配置..."
            cp "${BASIC_AUTH_PATH}/.gitconfig" ~/.gitconfig
            echo "  ✅ 应用.gitconfig"
          fi
        else
          echo "❌ Basic-auth workspace不存在"
        fi

        # 构建认证URL
        REPO_URL="$(params.url)"

        echo "🔍 检查认证信息:"
        echo "  GIT_TOKEN: ${GIT_TOKEN:+[设置]} ${GIT_TOKEN:-[未设置]}"
        echo "  GIT_USERNAME: ${GIT_USERNAME:+[设置]} ${GIT_USERNAME:-[未设置]}"
        echo "  GIT_PASSWORD: ${GIT_PASSWORD:+[设置]} ${GIT_PASSWORD:-[未设置]}"

        # 如果有Token且不为空，使用Token认证
        if [ -n "${GIT_TOKEN}" ] && [ "${GIT_TOKEN}" != "" ]; then
          echo "🔑 使用Token认证"
          # 提取主机名
          REPO_HOST=$(echo "${REPO_URL}" | sed 's|https://||' | sed 's|http://||' | cut -d'/' -f1)
          REPO_PATH=$(echo "${REPO_URL}" | sed 's|https://[^/]*||' | sed 's|http://[^/]*||')
          AUTHENTICATED_URL="https://oauth2:${GIT_TOKEN}@${REPO_HOST}${REPO_PATH}"
          echo "🔗 使用Token认证URL: https://oauth2:[TOKEN]@${REPO_HOST}${REPO_PATH}"
        elif [ -n "${GIT_USERNAME}" ] && [ "${GIT_USERNAME}" != "" ] && [ -n "${GIT_PASSWORD}" ] && [ "${GIT_PASSWORD}" != "" ]; then
          echo "🔑 使用用户名密码认证"
          # 提取主机名
          REPO_HOST=$(echo "${REPO_URL}" | sed 's|https://||' | sed 's|http://||' | cut -d'/' -f1)
          REPO_PATH=$(echo "${REPO_URL}" | sed 's|https://[^/]*||' | sed 's|http://[^/]*||')
          
          # 保持原有协议
          if echo "${REPO_URL}" | grep -q "^https://"; then
            AUTHENTICATED_URL="https://${GIT_USERNAME}:${GIT_PASSWORD}@${REPO_HOST}${REPO_PATH}"
            echo "🔗 使用HTTPS认证URL: https://${GIT_USERNAME}:[PASSWORD]@${REPO_HOST}${REPO_PATH}"
          else
            AUTHENTICATED_URL="http://${GIT_USERNAME}:${GIT_PASSWORD}@${REPO_HOST}${REPO_PATH}"
            echo "🔗 使用HTTP认证URL: http://${GIT_USERNAME}:[PASSWORD]@${REPO_HOST}${REPO_PATH}"
          fi
        else
          echo "⚠️  未配置认证信息，尝试公开仓库访问"
          AUTHENTICATED_URL="${REPO_URL}"
          echo "🔗 使用公开URL: ${REPO_URL}"
        fi

        # 配置Git以避免交互式提示
        git config --global user.email "<EMAIL>"
        git config --global user.name "Tekton Pipeline"
        git config --global init.defaultBranch main

        # 设置credential helper
        if [ -n "${GIT_USERNAME}" ] && [ -n "${GIT_PASSWORD}" ]; then
          git config --global credential.helper '!f() { echo "username=${GIT_USERNAME}"; echo "password=${GIT_PASSWORD}"; }; f'
        fi

        # 禁用交互式提示
        export GIT_TERMINAL_PROMPT=0
        export GIT_ASKPASS=/bin/true

        # 克隆仓库
        echo "📥 正在克隆仓库..."
        git clone --depth=$(params.depth) --branch=$(params.revision) "${AUTHENTICATED_URL}" temp_clone

        # 移动文件到当前目录
        echo "📦 移动源代码文件..."
        mv temp_clone/* . 2>/dev/null || true
        mv temp_clone/.* . 2>/dev/null || true
        rm -rf temp_clone

        # 验证克隆结果
        echo "✅ 克隆完成"
        echo "📋 目录内容:"
        ls -la

        echo "🔍 检查关键文件:"
        if [ -f "Dockerfile" ]; then
          echo "✅ 找到Dockerfile"
          echo "📄 Dockerfile前5行:"
          head -5 Dockerfile
        else
          echo "⚠️  未找到Dockerfile，搜索位置:"
          find . -name "Dockerfile" -type f | head -5
        fi

        if [ -f "go.mod" ]; then
          echo "✅ 找到go.mod (Go项目)"
        elif [ -f "package.json" ]; then
          echo "✅ 找到package.json (Node.js项目)"
        elif [ -f "pom.xml" ]; then
          echo "✅ 找到pom.xml (Java项目)"
        elif [ -f "requirements.txt" ]; then
          echo "✅ 找到requirements.txt (Python项目)"
        fi

        # 显示提交信息
        echo "📝 当前提交信息:"
        git config --global --add safe.directory "$(pwd)"
        git log -1 --pretty=format:"%h - %an, %ar : %s" || echo "无法获取提交信息"

        echo "🎉 GitLab代码克隆完成！"
