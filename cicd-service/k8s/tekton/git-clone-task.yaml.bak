apiVersion: tekton.dev/v1beta1
kind: Task
metadata:
  name: git-clone
spec:
  description: >-
    These Tasks are Git tasks to work with repositories used by other tasks in
    your Pipeline.
    The git-clone Task will clone a repo from the provided url into the output
    Workspace. By default the repo will be cloned into the root of your
    Workspace.
  workspaces:
    - name: output
      description: The git repo will be cloned into this workspace
  params:
    - name: url
      description: Git repository URL to clone
      type: string
    - name: revision
      description: Git revision to checkout (branch, tag, sha, ref…)
      type: string
      default: ""
    - name: refspec
      description: Git refspec to fetch before checking out revision
      default: ""
      type: string
    - name: submodules
      description: Initialize and fetch git submodules
      type: string
      default: "true"
    - name: depth
      description: Perform a shallow clone, fetching only the most recent N commits
      type: string
      default: "1"
    - name: sslVerify
      description: Set to false to skip SSL verification
      type: string
      default: "true"
    - name: deleteExisting
      description: Clean out the contents of the destination directory if it already exists before cloning
      type: string
      default: "false"
    - name: httpProxy
      description: HTTP proxy server for non-SSL requests
      type: string
      default: ""
    - name: httpsProxy
      description: HTTPS proxy server for SSL requests
      type: string
      default: ""
    - name: noProxy
      description: Opt out of proxying HTTP/HTTPS requests
      type: string
      default: ""
    - name: verbose
      description: Log the commands used during execution
      type: string
      default: "true"
  results:
    - name: commit
      description: The precise commit SHA that was fetched by this Task
    - name: url
      description: The precise URL that was fetched by this Task
  podTemplate:
    securityContext:
      fsGroup: 1000
  steps:
    - name: clone
      image: alpine/git:v2.36.3
      env:
        - name: PARAM_URL
          value: $(params.url)
        - name: PARAM_REVISION
          value: $(params.revision)
        - name: PARAM_REFSPEC
          value: $(params.refspec)
        - name: PARAM_SUBMODULES
          value: $(params.submodules)
        - name: PARAM_DEPTH
          value: $(params.depth)
        - name: PARAM_SSL_VERIFY
          value: $(params.sslVerify)
        - name: PARAM_DELETE_EXISTING
          value: $(params.deleteExisting)
        - name: PARAM_HTTP_PROXY
          value: $(params.httpProxy)
        - name: PARAM_HTTPS_PROXY
          value: $(params.httpsProxy)
        - name: PARAM_NO_PROXY
          value: $(params.noProxy)
        - name: PARAM_VERBOSE
          value: $(params.verbose)
        - name: WORKSPACE_OUTPUT_PATH
          value: $(workspaces.output.path)
      securityContext:
        runAsUser: 1000
        runAsGroup: 1000
      script: |
        #!/usr/bin/env sh
        set -eu

        if [ "${PARAM_VERBOSE}" = "true" ] ; then
          set -x
        fi

        if [ "${PARAM_DELETE_EXISTING}" = "true" ] ; then
          rm -rf "${WORKSPACE_OUTPUT_PATH:?}"/*
          rm -rf "${WORKSPACE_OUTPUT_PATH:?}"/.??*
        fi

        GIT_SSL_CAINFO=/etc/ssl/certs/ca-certificates.crt
        export GIT_SSL_CAINFO

        if [ "${PARAM_SSL_VERIFY}" = "false" ] ; then
          GIT_SSL_NO_VERIFY=true
          export GIT_SSL_NO_VERIFY
        fi

        CHECKOUT_DIR="${WORKSPACE_OUTPUT_PATH}"

        # Setting up the config for the git.
        git config --global user.email "<EMAIL>"
        git config --global user.name "Tekton Pipeline"

        # Prepare to clone the repo
        cd "${WORKSPACE_OUTPUT_PATH}"

        HTTPS_PROXY="${PARAM_HTTPS_PROXY}"
        export HTTPS_PROXY
        HTTP_PROXY="${PARAM_HTTP_PROXY}"
        export HTTP_PROXY
        NO_PROXY="${PARAM_NO_PROXY}"
        export NO_PROXY

        # Clone the repo
        if [ -n "${PARAM_REFSPEC}" ]; then
          git init "${CHECKOUT_DIR}"
          cd "${CHECKOUT_DIR}"
          git remote add origin "${PARAM_URL}"
          git fetch --depth="${PARAM_DEPTH}" origin "${PARAM_REFSPEC}"
          git checkout -b main FETCH_HEAD
        else
          git clone --depth="${PARAM_DEPTH}" --single-branch "${PARAM_URL}" "${CHECKOUT_DIR}"
          cd "${CHECKOUT_DIR}"
          if [ -n "${PARAM_REVISION}" ]; then
            git checkout "${PARAM_REVISION}"
          fi
        fi

        # Initialize and fetch submodules if PARAM_SUBMODULES is true
        if [ "${PARAM_SUBMODULES}" = "true" ]; then
          git submodule init
          git submodule update --recursive
        fi

        RESULT_COMMIT="$(git rev-parse HEAD)"
        RESULT_URL="${PARAM_URL}"

        printf "%s" "${RESULT_COMMIT}" > "$(results.commit.path)"
        printf "%s" "${RESULT_URL}" > "$(results.url.path)"
