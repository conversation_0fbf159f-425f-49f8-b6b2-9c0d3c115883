apiVersion: tekton.dev/v1
kind: Task
metadata:
  name: check-harbor-project
  labels:
    app.kubernetes.io/version: "0.1"
  annotations:
    tekton.dev/pipelines.minVersion: "0.17.0"
    tekton.dev/categories: Registry
    tekton.dev/tags: harbor,registry
    tekton.dev/displayName: "Check and create Harbor project if needed"
spec:
  description: >-
    This Task checks if a Harbor project exists and creates it if necessary.
    It parses the image name to extract project information and uses Harbor API.
  params:
    - name: IMAGE
      description: Full image name to extract project information from
      type: string
    - name: HARBOR_USERNAME
      description: Harbor registry username
      type: string
      default: "admin"
    - name: HARBOR_PASSWORD
      description: Harbor registry password
      type: string
      default: ""
  steps:
    - name: check-and-create-project
      image: curlimages/curl:8.5.0
      script: |
        #!/bin/sh
        set -ex

        echo "🔍 检查Harbor项目状态"

        # 解析镜像名称以获取项目信息
        IMAGE="$(params.IMAGE)"
        echo "📦 镜像名称: $IMAGE"

        # 从镜像名称中提取各部分: harbor-host/project-name/app-name:tag
        # 移除可能的协议前缀
        CLEAN_IMAGE=$(echo "$IMAGE" | sed 's|^https://||' | sed 's|^http://||')
        echo "🧹 清理后的镜像名称: $CLEAN_IMAGE"

        # 分解镜像名称
        HARBOR_HOST=$(echo "$CLEAN_IMAGE" | cut -d'/' -f1)
        PROJECT_NAME=$(echo "$CLEAN_IMAGE" | cut -d'/' -f2)
        APP_WITH_TAG=$(echo "$CLEAN_IMAGE" | cut -d'/' -f3)
        APP_NAME=$(echo "$APP_WITH_TAG" | cut -d':' -f1)

        echo "🏠 Harbor Host: $HARBOR_HOST"
        echo "📁 项目名称: $PROJECT_NAME"
        echo "🚀 应用名称: $APP_NAME"

        # Harbor认证信息
        HARBOR_USERNAME="$(params.HARBOR_USERNAME)"
        HARBOR_PASSWORD="$(params.HARBOR_PASSWORD)"

        if [ -z "$HARBOR_PASSWORD" ]; then
          echo "❌ Harbor密码为空"
          exit 1
        fi

        # 构建Harbor API URL
        HARBOR_API_URL="https://${HARBOR_HOST}/api/v2.0"
        PROJECT_API_URL="${HARBOR_API_URL}/projects/${PROJECT_NAME}"

        echo "🔗 项目API URL: $PROJECT_API_URL"

        # 检查项目是否存在
        echo "🔍 检查项目是否存在..."
        HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" \
          -u "${HARBOR_USERNAME}:${HARBOR_PASSWORD}" \
          "$PROJECT_API_URL")

        echo "📊 HTTP状态码: $HTTP_CODE"

        if [ "$HTTP_CODE" = "200" ]; then
          echo "✅ 项目 ${PROJECT_NAME} 已存在"
        elif [ "$HTTP_CODE" = "404" ]; then
          echo "🚀 项目 ${PROJECT_NAME} 不存在，正在创建..."
          
          # 创建项目
          CREATE_RESPONSE=$(curl -s -X POST \
            -u "${HARBOR_USERNAME}:${HARBOR_PASSWORD}" \
            -H "Content-Type: application/json" \
            -d "{\"project_name\": \"${PROJECT_NAME}\", \"public\": true}" \
            "${HARBOR_API_URL}/projects" \
            -w "HTTP_CODE:%{http_code}")
          
          CREATE_HTTP_CODE=$(echo "$CREATE_RESPONSE" | grep -o 'HTTP_CODE:[0-9]*' | cut -d':' -f2)
          CREATE_BODY=$(echo "$CREATE_RESPONSE" | sed 's/HTTP_CODE:[0-9]*$//')
          
          echo "📝 创建响应: $CREATE_BODY"
          echo "📊 创建HTTP状态码: $CREATE_HTTP_CODE"
          
          if [ "$CREATE_HTTP_CODE" = "201" ] || [ "$CREATE_HTTP_CODE" = "200" ]; then
            echo "✅ 项目 ${PROJECT_NAME} 创建成功"
            
            # 验证项目是否真正创建成功
            VERIFY_CODE=$(curl -s -o /dev/null -w "%{http_code}" \
              -u "${HARBOR_USERNAME}:${HARBOR_PASSWORD}" \
              "$PROJECT_API_URL")
            
            if [ "$VERIFY_CODE" = "200" ]; then
              echo "✅ 项目创建验证成功"
            else
              echo "❌ 项目创建验证失败，HTTP状态码: $VERIFY_CODE"
              exit 1
            fi
          else
            echo "❌ 项目创建失败，HTTP状态码: $CREATE_HTTP_CODE"
            echo "📄 响应内容: $CREATE_BODY"
            exit 1
          fi
        else
          echo "❌ 检查项目时出现错误，HTTP状态码: $HTTP_CODE"
          
          # 尝试获取详细错误信息
          ERROR_RESPONSE=$(curl -s \
            -u "${HARBOR_USERNAME}:${HARBOR_PASSWORD}" \
            "$PROJECT_API_URL")
          echo "📄 错误响应: $ERROR_RESPONSE"
          exit 1
        fi

        echo "🎉 Harbor项目检查完成"
