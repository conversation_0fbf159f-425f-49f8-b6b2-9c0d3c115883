apiVersion: tekton.dev/v1beta1
kind: PipelineRun
metadata:
  name: test-run-fix-new
  namespace: default
spec:
  pipelineRef:
    name: build-pipeline
  params:
    - name: git-url
      value: http://source.fundpark.com/charleslai/gonotice.git
    - name: git-revision
      value: main
    - name: image-name
      value: harbor.fundpark.com/ci-test/gonotice:test-123
    - name: build-id
      value: "test-123"
    - name: language
      value: go
    - name: HARBOR_USERNAME
      value: "admin"
    - name: HARBOR_PASSWORD
      value: "Harbor12345"
  podTemplate:
    hostAliases:
      - ip: "************"
        hostnames:
          - "harbor.fundpark.com"
  serviceAccountName: tekton-sa
  workspaces:
    - name: shared-workspace
      persistentVolumeClaim:
        claimName: tekton-workspace-pvc
    - name: docker-credentials
      secret:
        secretName: docker-config
    - name: gitlab-credentials
      secret:
        secretName: gitlab-auth
