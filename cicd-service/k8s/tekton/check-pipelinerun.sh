#!/bin/bash

# 设置PipelineRun名称
PIPELINE_RUN=${1:-build-gonotice-79n5w}

echo "检查PipelineRun状态: $PIPELINE_RUN"
kubectl get pipelinerun $PIPELINE_RUN -o yaml | grep -A5 status

echo -e "\n检查TaskRun状态:"
kubectl get taskrun -l tekton.dev/pipelineRun=$PIPELINE_RUN

echo -e "\n检查Pod状态:"
kubectl get pods -l tekton.dev/pipelineRun=$PIPELINE_RUN

echo -e "\n检查TaskRun日志:"
FETCH_TASK=$(kubectl get taskrun -l tekton.dev/pipelineRun=$PIPELINE_RUN,tekton.dev/pipelineTask=fetch-source -o name | head -1)
if [ ! -z "$FETCH_TASK" ]; then
  echo "Fetch source task日志:"
  kubectl logs $FETCH_TASK
fi

BUILD_TASK=$(kubectl get taskrun -l tekton.dev/pipelineRun=$PIPELINE_RUN,tekton.dev/pipelineTask=build-image -o name | head -1)
if [ ! -z "$BUILD_TASK" ]; then
  echo -e "\nBuild image task日志:"
  kubectl logs $BUILD_TASK
fi 