#!/bin/bash
set -e

echo "正在应用Tekton配置..."

# 切换到脚本所在目录
cd "$(dirname "$0")"

# 应用NFS PV和PVC
echo "创建NFS PV和PVC..."
kubectl apply -f tekton-nfs-pv.yaml

# 应用ServiceAccount和权限
echo "创建ServiceAccount和权限..."
kubectl apply -f tekton-sa.yaml

# 修复NFS权限
echo "修复NFS权限..."
#kubectl apply -f nfs-permission-fix.yaml
echo "等待权限修复完成..."
#kubectl wait --for=condition=complete --timeout=60s job/nfs-permission-fix || true

# 应用Task定义
echo "创建Tasks..."
kubectl apply -f git-clone-task.yaml
kubectl apply -f kaniko-task.yaml

# 应用Pipeline定义
echo "创建Pipeline..."
kubectl apply -f build-pipeline.yaml

# 应用PipelineRun
#echo "创建PipelineRun..."
#kubectl apply -f pipelinerun-gonotice.yaml

echo "所有配置已应用完成。" 
