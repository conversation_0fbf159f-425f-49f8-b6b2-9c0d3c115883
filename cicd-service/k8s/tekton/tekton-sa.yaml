apiVersion: v1
kind: ServiceAccount
metadata:
  name: tekton-sa
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: tekton-role
rules:
  - apiGroups: [""]
    resources:
      ["pods", "pods/log", "secrets", "configmaps", "persistentvolumeclaims"]
    verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
  - apiGroups: ["tekton.dev"]
    resources: ["tasks", "taskruns", "pipelines", "pipelineruns"]
    verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: tekton-role-binding
subjects:
  - kind: ServiceAccount
    name: tekton-sa
roleRef:
  kind: Role
  name: tekton-role
  apiGroup: rbac.authorization.k8s.io
