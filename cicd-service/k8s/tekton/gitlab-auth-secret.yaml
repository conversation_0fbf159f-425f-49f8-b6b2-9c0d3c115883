apiVersion: v1
kind: Secret
metadata:
  name: gitlab-auth
  namespace: default
type: Opaque
stringData:
  # 当前正确的认证配置
  username: "git"
  password: "T-_xS8UP5yWmA8H2pk5s"
  token: "T-_xS8UP5yWmA8H2pk5s"
# ---
# # 配置示例1: 仅使用GitLab Personal Access Token（推荐）
# # 使用此配置可提供更高的安全性和更好的权限控制
# apiVersion: v1
# kind: Secret
# metadata:
#   name: gitlab-auth-token-only
#   namespace: default
# type: Opaque
# stringData:
#   token: "glpat-xxxxxxxxxxxxxxxxxxxx" # 在GitLab中生成的Personal Access Token
#   username: "" # Token认证时不需要用户名
#   password: "" # Token认证时不需要密码

# ---
# # 配置示例2: 仅使用用户名 + 密码
# # 适用于传统认证方式或企业内部GitLab
# apiVersion: v1
# kind: Secret
# metadata:
#   name: gitlab-auth-password-only
#   namespace: default
# type: Opaque
# stringData:
#   token: "" # 不使用Token认证
#   username: "your-gitlab-username" # GitLab用户名
#   password: "your-gitlab-password" # GitLab密码或应用专用密码

# ---
# # 配置示例3: 混合配置（备用方案）
# # Token优先，如果Token失效则使用用户名密码作为备选
# apiVersion: v1
# kind: Secret
# metadata:
#   name: gitlab-auth-hybrid
#   namespace: default
# type: Opaque
# stringData:
#   token: "glpat-xxxxxxxxxxxxxxxxxxxx" # 主要认证方式
#   username: "your-gitlab-username" # 备用认证用户名
#   password: "your-gitlab-password" # 备用认证密码

# ---
# 📖 使用说明：
#
# 🎯 认证优先级：
#   1. GitLab Personal Access Token（如果token字段有值且不为空）
#   2. 用户名 + 密码（如果username和password字段都有值）
#   3. 公开仓库访问（如果以上都没有配置）
#
# 🔑 GitLab Personal Access Token生成步骤：
#   1. 登录GitLab → 用户设置 → Access Tokens
#   2. 创建新Token，选择合适的权限范围（通常需要read_repository）
#   3. 复制生成的Token（格式：glpat-xxxxxxxxxxxxxxxxxxxxx）
#   4. 填入上面的token字段
#
# ⚙️  部署命令：
#   kubectl apply -f gitlab-auth-secret.yaml
#
# 🧪 测试命令：
#   make test-gitlab-auth
#   make diagnose-pipeline-auth
#
# 📋 查看当前Secret：
#   kubectl get secret gitlab-auth -o yaml
#
# ✅ 认证问题已解决：
#   - Secret格式正确：包含username、password、token字段
#   - gitlab-clone Task已更新：支持新旧认证格式兼容
#   - 认证逻辑增强：支持.git-credentials文件解析
#   - 协议兼容性：同时支持http://和https://
#   - 测试验证通过：TaskRun成功克隆代码
