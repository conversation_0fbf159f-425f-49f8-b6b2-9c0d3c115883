apiVersion: tekton.dev/v1
kind: Task
metadata:
  name: kaniko
  labels:
    app.kubernetes.io/version: "0.7"
  annotations:
    tekton.dev/pipelines.minVersion: "0.43.0"
    tekton.dev/categories: Image Build
    tekton.dev/tags: image-build
    tekton.dev/displayName: "Build and upload container image using Kaniko"
    tekton.dev/platforms: "linux/amd64,linux/arm64,linux/ppc64le"
spec:
  description: >-
    This Task builds a simple Dockerfile with kaniko and pushes to a registry.
    This Task stores the image digest in a result named IMAGE_DIGEST.
  params:
    - name: IMAGE
      description: Name (reference) of the image to build.
      type: string
    - name: DOCKERFILE
      description: Path to the Dockerfile to build.
      type: string
      default: ./Dockerfile
    - name: CONTEXT
      description: The build context used by Kaniko.
      type: string
      default: ./
    - name: EXTRA_ARGS
      description: Extra arguments to pass to kaniko
      type: array
      default: []
    - name: BUILDER_IMAGE
      description: The image on which builds will run (default is v1.5.1)
      default: gcr.io/kaniko-project/executor:v1.5.1@sha256:c6166717f7fe0b7da44908c986137ecfeab21f31ec3992f6e128fff8a94be8a5
    - name: WRITER_IMAGE
      description: The image on which the write-url step will run (default is docker.io/library/bash:5.1.4@sha256:c523c636b722339f41b6a431b44588ab2f762c5de5ec3bd7964420ff982fb1d9)
      default: docker.io/library/bash:5.1.4@sha256:c523c636b722339f41b6a431b44588ab2f762c5de5ec3bd7964420ff982fb1d9
  workspaces:
    - name: source
      description: Contains the build context and Dockerfile
    - name: dockerconfig
      description: Contains the Docker config.json for authentication
      optional: true
      mountPath: /kaniko/.docker
  results:
    - name: IMAGE_DIGEST
      description: Digest of the image just built
      type: string
    - name: IMAGE_URL
      description: URL of the image just built
      type: string
  steps:
    - name: prepare-build-context
      workingDir: $(workspaces.source.path)
      image: busybox:1.36
      script: |
        #!/usr/bin/env sh
        set -ex

        echo "🔧 准备构建上下文"
        echo "📁 当前工作目录: $(pwd)"
        echo "📋 列出当前目录内容:"
        ls -la ./

        # 直接检查workspace根目录中的Dockerfile
        if [ -f "./Dockerfile" ]; then
          echo "✅ 找到Dockerfile: ./Dockerfile"
          echo "📄 Dockerfile内容预览:"
          head -10 "./Dockerfile"
        else
          echo "❌ 未找到Dockerfile在当前目录"
          echo "🔍 搜索Dockerfile位置:"
          find . -name "Dockerfile" -type f | head -5 || echo "未找到任何Dockerfile"
          exit 1
        fi

        echo "✅ 构建上下文准备完成"

    - name: build-and-push
      workingDir: $(workspaces.source.path)
      image: $(params.BUILDER_IMAGE)
      args:
        - $(params.EXTRA_ARGS[*])
        - --dockerfile=./Dockerfile
        - --context=./
        - --destination=$(params.IMAGE)
        - --digest-file=$(results.IMAGE_DIGEST.path)
        - --image-name-with-digest-file=$(results.IMAGE_URL.path)
      securityContext:
        runAsUser: 0

    - name: write-url
      image: $(params.WRITER_IMAGE)
      script: |
        set -e
        image="$(params.IMAGE)"
        printf "%s" "${image}" | tee "$(results.IMAGE_URL.path)"
