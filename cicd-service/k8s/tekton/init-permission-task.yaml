apiVersion: tekton.dev/v1beta1
kind: Task
metadata:
  name: init-permissions
spec:
  description: |
    初始化工作空间的权限，解决NFS权限问题
  workspaces:
    - name: workspace
      description: 需要修复权限的工作空间
  params:
    - name: serviceName
      description: 服务名称，用于构造工作空间路径
      type: string
      default: "default"
  steps:
    - name: fix-permissions
      image: busybox
      securityContext:
        runAsUser: 0 # 以root用户运行
      script: |
        #!/bin/sh
        set -eu
        echo "正在初始化工作空间..."

        # 创建服务专用工作空间目录
        SERVICE_DIR="/workspace/$(params.serviceName)"
        mkdir -p $SERVICE_DIR

        # 创建源码目录
        SRC_DIR="$SERVICE_DIR/source"
        mkdir -p $SRC_DIR

        # 给予所有用户对源码目录的读写执行权限
        chown -R 1000:1000 $SERVICE_DIR
        chmod -R 775 $SERVICE_DIR

        # 添加粘滞位，确保创建的文件继承目录权限
        chmod g+s $SERVICE_DIR
        chmod g+s $SRC_DIR

        echo "工作空间初始化完成！路径: $SERVICE_DIR"
