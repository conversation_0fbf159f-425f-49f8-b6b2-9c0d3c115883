apiVersion: tekton.dev/v1
kind: Pipeline
metadata:
  name: build-pipeline
  labels:
    app.kubernetes.io/version: "0.2"
  annotations:
    tekton.dev/pipelines.minVersion: "0.17.0"
    tekton.dev/categories: Build
    tekton.dev/tags: build,docker,kaniko
spec:
  description: |
    This pipeline clones a git repo, checks Harbor project, builds a Go application and creates a docker image
  params:
    - name: git-url
      type: string
      description: Git repository URL
    - name: git-revision
      type: string
      description: Git revision to checkout (branch, tag, commit SHA)
      default: "main"
    - name: image-name
      type: string
      description: Name of the image to build
    - name: build-id
      type: string
      description: Build job ID
    - name: language
      type: string
      description: Programming language
      default: "go"
    - name: build-command
      type: string
      description: Custom build command (optional)
      default: ""
    - name: modules
      type: string
      description: Modules to build (optional)
      default: ""
  tasks:
    # - name: init-workspace
    #   taskRef:
    #     name: init-permissions
    #   workspaces:
    #     - name: workspace
    #       workspace: shared-workspace

    - name: fetch-source
      taskRef:
        name: gitlab-clone
      params:
        - name: url
          value: $(params.git-url)
        - name: revision
          value: $(params.git-revision)
        - name: deleteExisting
          value: "true"
      workspaces:
        - name: output
          workspace: shared-workspace
        - name: basic-auth
          workspace: gitlab-credentials

    - name: build-image
      runAfter:
        - fetch-source
      taskRef:
        name: kaniko
      params:
        - name: IMAGE
          value: $(params.image-name)
        - name: EXTRA_ARGS
          value:
            - "--skip-tls-verify"
      workspaces:
        - name: source
          workspace: shared-workspace
        - name: dockerconfig
          workspace: docker-credentials

  workspaces:
    - name: shared-workspace
      description: Workspace for source code and build artifacts
    - name: docker-credentials
      description: Workspace for Docker credentials
    - name: gitlab-credentials
      description: Workspace for GitLab credentials
