apiVersion: tekton.dev/v1beta1
kind: PipelineRun
metadata:
  name: build-gonotice-79n5w
  labels:
    app.kubernetes.io/managed-by: cicd-service
    app.kubernetes.io/name: gonotice
    app.kubernetes.io/part-of: project-5
    build-id: "361"
    tekton.dev/pipeline: build-pipeline
spec:
  pipelineRef:
    name: build-pipeline
  params:
    - name: git-url
      value: http://source.fundpark.com/charleslai/gonotice.git
    - name: git-revision
      value: main
    - name: image-name
      value: https://harbor.fundpark.com/project-5/gonotice:ae4b8548-**************
    - name: build-id
      value: "361"
    - name: language
      value: go
    - name: build-command
      value: ""
  workspaces:
    - name: shared-workspace
      persistentVolumeClaim:
        claimName: tekton-workspace-pvc
    - name: docker-credentials
      secret:
        secretName: docker-config
  serviceAccountName: tekton-sa
