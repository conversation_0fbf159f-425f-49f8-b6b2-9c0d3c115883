{"apiVersion": "tekton.dev/v1beta1", "kind": "Pipeline", "metadata": {"name": "build-pipeline"}, "spec": {"description": "This pipeline clones a git repo, builds a Go application and creates a docker image\n", "params": [{"description": "Git repository URL", "name": "git-url", "type": "string"}, {"default": "main", "description": "Git revision to checkout (branch, tag, commit SHA)", "name": "git-revision", "type": "string"}, {"description": "Name of the image to build", "name": "image-name", "type": "string"}, {"description": "Build job ID", "name": "build-id", "type": "string"}, {"default": "go", "description": "Programming language", "name": "language", "type": "string"}, {"default": "", "description": "Custom build command (optional)", "name": "build-command", "type": "string"}, {"default": "", "description": "Modules to build (optional)", "name": "modules", "type": "string"}, {"default": "$(params.build-id)", "description": "服务名称，用于构造工作空间路径", "name": "serviceName", "type": "string"}, {"default": "admin", "description": "Harbor用户名", "name": "HARBOR_USERNAME", "type": "string"}, {"default": "4DNSNszww1B^9DbfENLe", "description": "Harbor密码", "name": "HARBOR_PASSWORD", "type": "string"}], "tasks": [{"name": "init-workspace", "params": [{"name": "serviceName", "value": "$(params.build-id)"}], "taskRef": {"name": "init-permissions"}, "workspaces": [{"name": "workspace", "workspace": "shared-workspace"}]}, {"name": "fetch-source", "params": [{"name": "url", "value": "$(params.git-url)"}, {"name": "revision", "value": "$(params.git-revision)"}, {"name": "deleteExisting", "value": "true"}, {"name": "subdirectory", "value": "source"}, {"name": "serviceName", "value": "$(params.build-id)"}], "runAfter": ["init-workspace"], "taskRef": {"name": "gitlab-clone"}, "workspaces": [{"name": "output", "workspace": "shared-workspace"}, {"name": "basic-auth", "workspace": "gitlab-credentials"}]}, {"name": "build-image", "params": [{"name": "IMAGE", "value": "$(params.image-name)"}, {"name": "serviceName", "value": "$(params.serviceName)"}, {"name": "HARBOR_USERNAME", "value": "$(params.HARBOR_USERNAME)"}, {"name": "HARBOR_PASSWORD", "value": "$(params.HARBOR_PASSWORD)"}, {"name": "EXTRA_ARGS", "value": ["--skip-tls-verify"]}], "runAfter": ["fetch-source"], "taskRef": {"name": "kaniko"}, "workspaces": [{"name": "source", "workspace": "shared-workspace"}, {"name": "dockerconfig", "workspace": "docker-credentials"}]}], "workspaces": [{"description": "Workspace for source code and build artifacts", "name": "shared-workspace"}, {"description": "Workspace for Docker credentials", "name": "docker-credentials"}, {"description": "Workspace for GitLab credentials", "name": "gitlab-credentials"}]}}