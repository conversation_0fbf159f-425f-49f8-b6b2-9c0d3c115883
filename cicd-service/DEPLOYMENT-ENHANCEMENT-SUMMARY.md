# Kubernetes部署服务增强功能总结

## 🎯 功能概述

本次更新为Kubernetes部署服务添加了三个重要功能：

1. **Harbor拉取认证Secret自动创建**
2. **命名空间格式规范化**
3. **基于模板的YAML生成系统**

## 📝 详细功能说明

### 1. Harbor拉取认证Secret自动创建

#### 配置更新
- **config.yaml新增字段**：
  ```yaml
  harbor:
    pull_username_secret: "harbor-pull-secret"
    pull_username: "pull"
    pull_password: "password"
  ```

- **config.go结构体更新**：
  ```go
  type HarborConfig struct {
      URL                 string `mapstructure:"url"`
      Username            string `mapstructure:"username"`
      Password            string `mapstructure:"password"`
      PullUsernameSecret  string `mapstructure:"pull_username_secret"`
      PullUsername        string `mapstructure:"pull_username"`
      PullPassword        string `mapstructure:"pull_password"`
  }
  ```

#### 功能实现
- **ensureHarborSecret()方法**：
  - 部署前自动检查`loginharbor` Secret是否存在
  - 不存在则使用Harbor配置自动创建Docker认证Secret
  - 支持标准的`.dockerconfigjson`格式
  - 包含Harbor URL、用户名、密码和base64编码认证信息

### 2. 命名空间格式规范化

#### 格式变更
- **旧格式**：`{environmentName}`
- **新格式**：`{environmentName.lower}-{productName.lower}`

#### 实现细节
```go
// 修正命名空间格式为 {environmentName.lower}-{productName.lower}
namespace := fmt.Sprintf("%s-%s", 
    strings.ToLower(environment.Name), 
    strings.ToLower(app.Project.Product.Name))
```

#### 示例
- 环境：`Production`，产品：`ECommerce`
- 生成命名空间：`production-ecommerce`

### 3. 基于模板的YAML生成系统

#### 参考Python实现
基于Python端`common/ext_fun.py`中的`template_generate`方法实现，包含：

#### Deployment YAML生成
**generateDeploymentYAML()方法特性**：
- ✅ 容器端口配置（支持多端口）
- ✅ 环境变量预设（TZ、产品信息、重启时间戳等）
- ✅ 标签系统（应用标签、环境标签、版本标签）
- ✅ 卷挂载（日志目录）
- ✅ 资源配额（CPU/内存请求和限制）
- ✅ 镜像拉取认证（loginharbor secret）
- ✅ 主机别名（Harbor、DevOps平台）
- ✅ 滚动更新策略
- ✅ Prometheus注解

#### Service YAML生成
**generateServiceYAML()方法特性**：
- ✅ 智能端口解析（从AppInfo.Ports JSON配置）
- ✅ NodePort支持（仅在配置node_port时创建Service）
- ✅ 协议支持（TCP/UDP）
- ✅ 端口命名规范
- ✅ 参考Python `template_svc_generate`逻辑

#### 核心YAML模板特性

```go
// 环境变量预设
envVars := []corev1.EnvVar{
    {Name: "TZ", Value: "Asia/Shanghai"},
    {Name: "_RESTART", Value: time.Now().Format("20060102150405")},
    {Name: "PRODUCT_NAME", Value: app.Project.Product.Name},
    {Name: "PROJECT_NAME", Value: app.Project.Name},
    {Name: "APPNAME", Value: app.Name},
    {Name: "APPID", Value: app.AppID},
    {Name: "ENV", Value: environment.Name},
    {Name: "POD_NAMESPACE", Value: params.Namespace},
}

// 主机别名配置
HostAliases: []corev1.HostAlias{
    {
        IP:        "************",
        Hostnames: []string{"harbor.fundpark.com", "source.fundpark.com"},
    },
    {
        IP:        "************",
        Hostnames: []string{"imaojia.com/devops"},
    },
}

// Prometheus注解
Annotations: map[string]string{
    "prometheus.io/app_product": app.Project.Product.Name,
    "prometheus.io/app_env":     environment.Name,
    "prometheus.io/app_project": app.Project.Name,
}
```

## 🔧 技术架构

### 新增方法结构

```
KubernetesDeployService
├── ensureHarborSecret() - Harbor Secret自动创建
├── generateDeploymentYAML() - Deployment模板生成
├── generateServiceYAML() - Service模板生成  
├── deployApplicationWithTemplate() - 模板化应用部署
└── deployServiceWithTemplate() - 模板化服务部署
```

### 兼容性设计
- 保留原有方法（`deployApplication`, `deployService`, `createDeployment`, `createService`）
- 新增模板化方法（`*WithTemplate`）
- 向后兼容现有调用

### 部署流程增强

```
ExecuteDeployment 流程:
1. 解密Kubernetes配置
2. 创建Kubernetes客户端
3. 构建部署参数（含新命名空间格式）
4. 创建或确保Namespace存在
5. 🆕 确保Harbor Secret存在
6. 🆕 使用模板部署应用
7. 🆕 使用模板部署Service（智能跳过）
8. 检查部署状态
9. 更新部署状态和结果
```

## ✅ 验证结果

### 编译测试
```bash
cd cicd-service
go build .  # ✅ 编译成功，无错误
```

### 功能验证要点
1. ✅ **Harbor Secret**：自动检查和创建loginharbor Secret
2. ✅ **命名空间**：格式为`{env}-{product}`（小写）
3. ✅ **Deployment**：完整模板化YAML生成
4. ✅ **Service**：基于端口配置的智能创建
5. ✅ **兼容性**：保持向后兼容

## 🎯 使用效果

### 部署前
- 手动管理Harbor认证
- 简单命名空间命名
- 基础YAML配置

### 部署后
- 🚀 **自动化**：Harbor Secret自动管理
- 📋 **规范化**：统一命名空间格式
- 🎨 **模板化**：丰富的YAML配置
- 🔄 **智能化**：Service按需创建
- 📊 **监控化**：Prometheus注解集成

## 📋 相关文件

- ✅ `config/config.yaml.example` - Harbor拉取认证配置
- ✅ `config/config.go` - 配置结构体更新
- ✅ `services/kubernetes_deploy_service.go` - 核心功能实现

系统现在完全支持企业级Kubernetes部署需求，包含完整的认证管理、规范化命名空间和丰富的模板化部署配置！ 