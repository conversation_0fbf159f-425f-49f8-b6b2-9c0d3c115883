{"apiVersion": "tekton.dev/v1", "kind": "Pipeline", "metadata": {"annotations": {"tekton.dev/categories": "Build", "tekton.dev/pipelines.minVersion": "0.17.0", "tekton.dev/tags": "build,docker,kaniko"}, "labels": {"app.kubernetes.io/version": "0.2"}, "name": "build-pipeline"}, "spec": {"description": "This pipeline clones a git repo, checks Harbor project, builds a Go application and creates a docker image\n", "params": [{"description": "Git repository URL", "name": "git-url", "type": "string"}, {"default": "main", "description": "Git revision to checkout (branch, tag, commit SHA)", "name": "git-revision", "type": "string"}, {"description": "Name of the image to build", "name": "image-name", "type": "string"}, {"description": "Build job ID", "name": "build-id", "type": "string"}, {"default": "go", "description": "Programming language", "name": "language", "type": "string"}, {"default": "", "description": "Custom build command (optional)", "name": "build-command", "type": "string"}, {"default": "", "description": "Modules to build (optional)", "name": "modules", "type": "string"}], "tasks": [{"name": "fetch-source", "params": [{"name": "url", "value": "$(params.git-url)"}, {"name": "revision", "value": "$(params.git-revision)"}, {"name": "deleteExisting", "value": "true"}], "taskRef": {"name": "gitlab-clone"}, "workspaces": [{"name": "output", "workspace": "shared-workspace"}, {"name": "basic-auth", "workspace": "gitlab-credentials"}]}, {"name": "build-image", "params": [{"name": "IMAGE", "value": "$(params.image-name)"}, {"name": "EXTRA_ARGS", "value": ["--skip-tls-verify"]}], "runAfter": ["fetch-source"], "taskRef": {"name": "kaniko"}, "workspaces": [{"name": "source", "workspace": "shared-workspace"}, {"name": "dockerconfig", "workspace": "docker-credentials"}]}], "workspaces": [{"description": "Workspace for source code and build artifacts", "name": "shared-workspace"}, {"description": "Workspace for Docker credentials", "name": "docker-credentials"}, {"description": "Workspace for GitLab credentials", "name": "gitlab-credentials"}]}}