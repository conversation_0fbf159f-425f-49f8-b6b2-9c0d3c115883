# Tekton v1 更新和Workspace优化说明

## 概述

本文档说明了Tekton pipeline从v1beta1升级到v1版本的更新，以及workspace配置的优化，解决了NFS挂载目录下混乱的目录结构问题。

## 🚀 主要更新内容

### 1. Tekton API版本更新

#### kaniko-task.yaml
**更新前 (v1beta1)**：
```yaml
apiVersion: tekton.dev/v1beta1
kind: Task
metadata:
  name: kaniko
spec:
  # 复杂的Harbor认证逻辑
  # 多个step处理项目检查和Docker配置
```

**更新后 (v1)**：
```yaml
apiVersion: tekton.dev/v1
kind: Task
metadata:
  name: kaniko
  labels:
    app.kubernetes.io/version: "0.6"
  annotations:
    tekton.dev/pipelines.minVersion: "0.17.0"
spec:
  # 简化的构建逻辑，专注于镜像构建
  # 移除Harbor认证相关参数
```

#### build-pipeline.yaml
**更新前 (v1beta1)**：
```yaml
apiVersion: tekton.dev/v1beta1
kind: Pipeline
metadata:
  name: build-pipeline
```

**更新后 (v1)**：
```yaml
apiVersion: tekton.dev/v1
kind: Pipeline
metadata:
  name: build-pipeline
  labels:
    app.kubernetes.io/version: "0.2"
  annotations:
    tekton.dev/pipelines.minVersion: "0.17.0"
```

### 2. Harbor认证简化

根据[Tekton Hub官方kaniko task](https://hub.tekton.dev/tekton/task/kaniko)的最佳实践，我们：

#### 创建独立的Harbor项目检查Task
新增 `check-harbor-project-task.yaml`：
- ✅ 专门负责Harbor项目检查和创建
- ✅ 使用最新的curl镜像 (8.5.0)
- ✅ 完善的错误处理和状态验证
- ✅ 清晰的日志输出

#### 简化kaniko Task
- ✅ 移除Harbor相关参数 (`HARBOR_USERNAME`, `HARBOR_PASSWORD`)
- ✅ 删除复杂的Harbor项目检查逻辑
- ✅ 专注于镜像构建功能
- ✅ 使用最新的kaniko executor (v1.23.2)

### 3. Workspace配置优化

#### 问题描述
**修改前**：在NFS挂载目录 `/data/nfsshare` 下生成了混乱的目录结构：
```
/data/nfsshare/
├── 0/
├── build-context/
├── 12/
└── ... (其他随机目录)
```

#### 解决方案
**修改后**：使用PipelineRun名称作为workspace子目录：

**cicd_service.go中的更改**：
```go
// 生成唯一的PipelineRun名称
pipelineRunName := fmt.Sprintf("build-%s-%d", 
    strings.Replace(appInfoObj.UniqTag, ".", "-", -1), 
    time.Now().Unix())

// 在workspace配置中使用SubPath
Workspaces: []tektonv1.WorkspaceBinding{
    {
        Name: "shared-workspace",
        PersistentVolumeClaim: &corev1.PersistentVolumeClaimVolumeSource{
            ClaimName: "tekton-workspace-pvc",
        },
        SubPath: pipelineRunName, // 使用PipelineRun名称作为子目录
    },
    // ...
}
```

#### 优化结果
**现在的目录结构**：
```
/data/nfsshare/
├── build-gonotice-dev-fundpark-com-1716456789/
│   ├── source/
│   │   └── 12/
│   │       ├── Dockerfile
│   │       └── ...
│   └── ...
├── build-webapp-test-example-com-1716456790/
│   ├── source/
│   └── ...
└── build-api-service-prod-company-com-1716456791/
    ├── source/
    └── ...
```

## ✅ 改进效果

### 1. API版本标准化
- ✅ 使用最新的Tekton v1 API
- ✅ 符合Tekton Hub最佳实践
- ✅ 添加了标准的labels和annotations
- ✅ 提升了兼容性和稳定性

### 2. Harbor认证简化
- ✅ 分离关注点：Harbor检查 vs 镜像构建
- ✅ 更清晰的任务边界
- ✅ 更好的错误处理和日志
- ✅ 易于维护和调试

### 3. Workspace管理优化
- ✅ 每个PipelineRun有独立的工作目录
- ✅ 避免目录冲突和数据污染
- ✅ 清晰的目录命名规则
- ✅ 便于日志查看和问题排查

### 4. 性能提升
- ✅ 减少了不必要的step数量
- ✅ 优化了镜像版本选择
- ✅ 简化了任务依赖关系
- ✅ 提高了构建效率

## 🧪 验证工具

提供了完整的测试套件验证更新效果：

### 1. Workspace配置测试
```bash
make test-workspace
```
验证PipelineRun名称生成和workspace路径配置。

### 2. Harbor解析测试
```bash
make test-harbor-parsing  
```
验证Harbor项目检查逻辑的正确性。

### 3. 完整编译验证
```bash
make build
```
验证所有代码更改的正确性。

## 📋 迁移检查清单

在部署更新时，请确认以下项目：

- [ ] 确认Tekton Pipelines版本 >= 0.17.0
- [ ] 部署新的 `check-harbor-project` Task
- [ ] 更新现有的 `kaniko` Task
- [ ] 更新 `build-pipeline` Pipeline
- [ ] 验证NFS PVC配置正确
- [ ] 测试Harbor认证配置
- [ ] 监控首次运行的workspace目录创建

## 🔗 相关资源

- [Tekton Hub - Kaniko Task](https://hub.tekton.dev/tekton/task/kaniko)
- [Tekton v1 API文档](https://tekton.dev/docs/pipelines/pipelines/)
- [Tekton v1beta1 到 v1 迁移指南](https://tekton.dev/docs/pipelines/migrating-v1beta1-to-v1/)

这些更新确保了CI/CD系统的现代化、稳定性和可维护性！🎉 